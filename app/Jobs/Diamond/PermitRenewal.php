<?php

namespace App\Jobs\Diamond;

use Exception;
use App\Jobs\Job;

use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;

use App\Classes\DatacapPaymentGateway;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;

use App\Models\User;
use App\Models\PermitRequest;
use App\Models\PermitRate;
use App\Models\PermitRequestRenewHistory;

use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;

use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;

class PermitRenewal extends Job implements ShouldQueue
{
	use InteractsWithQueue, SerializesModels;

    protected $permit;
    protected $log;
	protected $user;
	protected $permitRate;
	protected $partnerDetails;
	protected $partner_name;
	protected $facility_brand_setting;
	protected $brand_setting;
	protected $paymentProfile;
	protected $business_user_id;
	/**
     * Create a new job instance.
     *
     * @return void
     */

	public function __construct($permit, $log)
    {
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/are/permitRenewal')->createLogger('permitrenewjob');    

        $this->permit = $permit;    

		$this->user 					= User::where('id', $this->permit->user_id)->first();
		$this->permitRate 				= PermitRate::where('id', $this->permit->permit_rate_id)->first();

		// Partner Data
		$this->partnerDetails 			= User::where('id', $this->permit->partner_id)->first();

		// Brand Settings
		$this->facility_brand_setting 	= FacilityBrandSetting::where('facility_id', $this->permit->facility_id)->first();
		$this->brand_setting 			= BrandSetting::where('user_id', $this->permit->partner_id)->first();
		
		#DD PIMS-11336 SUBORIDNATE
		if(!is_null($this->permit->business_id) && $this->permit->business_id > 0){
			$businessUser = User::where('id', $this->permit->business_id)->first();
		}		
		if(isset($businessUser) && $businessUser->payment_mode=='0'){
			$this->paymentProfile 			= HeartlandPaymentProfile::where('user_id', $this->permit->business_id)->where('is_default',1)->orderBy('id', 'DESC')->first();
			$this->business_user_id 		= $this->permit->business_id;
		}else{
			$this->paymentProfile 			= HeartlandPaymentProfile::where('user_id', $this->permit->user_id)->where('is_default',1)->orderBy('id', 'DESC')->first();
			$this->business_user_id 		= $this->permit->user_id;
		}
		#DD PIMS-11336 END

		if($this->facility_brand_setting){
			$this->paymentProfile->facility_logo_id = $this->facility_brand_setting->id;
		} else {
			$this->paymentProfile->logo_id  = $this->brand_setting->id;
		}
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
	{
		try {
			QueryBuilder::setCustomTimezone($this->permit->facility_id);

			$desired_end_date = date("Y-m-t");
			$desired_start_date = date('Y-m-01');

			$permitRenewDate = date('Y-m-d h:i:s');

			$this->log->info('Under Permit Renewal handle function start.');
			$this->log->info("Request For Permit Renewal :" . $this->permit);

			$permitData = PermitRequest::with(['facility','user','PermitVehicle'])->where('id',$this->permit->id)->first();

			$this->log->info("Permit Rate Found " . $permitData->account_number . "--"   . json_encode($this->permitRate));


			if ($this->permitRate->rate == '0.00') {
				$this->createPermitRequestHistoryNew($this->permit);   
				$maxDays=date('t');
				
				$permitData->no_of_days          = $maxDays;    
				$permitData->permit_rate         = $this->permitRate->rate;
				$permitData->desired_start_date  = $desired_start_date;
				$permitData->desired_end_date    = $desired_end_date;
				$permitData->transaction_retry   = ++$permitData->transaction_retry;
				$permitData->save();

				if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
					$view = 'are.permit-renew-free';
				} else {
					$view = 'are-diamond.permit-renew';
				}

				$this->log->info("Free Permit Renew Permit Id #:" . $permitData->account_number);
				$subject = "Your Permit #". $permitData->account_number." has been Renewed Successfully";

				// Brand Setting Details
				if ($this->facility_brand_setting) {
					$permitData->facility_logo_id = $this->facility_brand_setting->id;
				}else{
					$permitData->logo_id  = $this->brand_setting->id;
				}

				// Send Mail
				$this->sendMail($permitData, $view, $subject);
			} else if ($this->permitRate->rate > "0.00") {
				
				if (isset($permitData->facility->FacilityPaymentDetails) && ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '1')) { 
					dd('stop planet');
				}else if (isset($permitData->facility->FacilityPaymentDetails) && ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '2')) {
					dd('stop datacap');
				}else if (isset($permitData->facility->FacilityPaymentDetails) && ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
					

					$this->log->info("Payment Profile Data --" . $permitData->account_number . "--"  . json_encode($this->paymentProfile));
					$amount = ($permitData->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $this->permitRate->rate;

					if ($this->paymentProfile) {
						$request = new Request([
							'Amount'   => $amount,
							'total'   => $amount,
							'token' 			=> $this->paymentProfile->token,
							'zipcode' 			=> $this->paymentProfile->zipcode,
							'card_last_four' 		=> $this->paymentProfile->card_last_four,
							'expiration_date' 		=> $this->paymentProfile->expiry,
						]);
						$this->log->info("Payment Request to HL Data --" . $permitData->account_number . "--" . json_encode($request->all()));
						try {
							$paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $permitData->facility);
							if (isset($paymentResponse) && $paymentResponse->responseMessage == 'APPROVAL') {
								$this->log->info("Heartland Payment Success Response to Permit Id #:" .  $permitData->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode . "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId);
								$this->createPermitRequestHistoryNew($permitData);
								$user_id = $permitData->user_id;
								$authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request,$paymentResponse, $this->business_user_id);  #PIMS-11336
								$this->log->info("Payment Transaction Data make Heartland Payment -- " .  $permitData->account_number. "--" . json_encode($authorized_anet_transaction));
								$maxDays=date('t');
								$permitData->no_of_days          = $maxDays;
								$permitData->anet_transaction_id = $authorized_anet_transaction->id;
								$permitData->permit_rate         = $this->permitRate->rate;
								$permitData->permit_final_amount = $this->permitRate->rate;
								$permitData->desired_start_date  = $desired_start_date;
								$permitData->desired_end_date    = $desired_end_date;
								$permitData->transaction_retry   = $permitData->transaction_retry + 1;
								$permitData->save();

								$expiry    = $this->paymentProfile->expiry;
								if($expiry){
									$expiry_data = str_split($expiry, 2);
									$this->paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
								} else {
									$this->paymentProfile->card_expiry = "-";
								}

								if ($this->facility_brand_setting) {
									$this->paymentProfile->facility_logo_id = $this->facility_brand_setting->id;
								} else {
									$this->paymentProfile->logo_id  = $this->brand_setting->id;
								}

								if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
									$view = 'are.permit-renew';
								}else{
									$view = 'are-diamond.permit-renew';
								}
								
								$subject = "Autopayment Confirmation for Permit # " . $permitData->account_number;

								// Send Mail
								$this->sendMail($permitData, $view, $subject);
							} else {
								$this->log->info("Heartland Payment Failure Response to Permit Id #:" .  $permitData->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode . "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId);
								$permitData->transaction_retry   = $permitData->transaction_retry + 1;
								$permitData->save();

								if ($this->facility_brand_setting) {
									$permitData->facility_logo_id = $this->facility_brand_setting->id;
								}else{
									$permitData->logo_id  = $this->brand_setting->id;
								}

								$day_of_month = date('d');
								if($day_of_month =='03'){
									$view='usm.permit-cancel-reminder';  
									//call skiData Api to cancel resuable ticket
									$subject = "Important Notice: Permit Cancellation";
									if ($permitData->skidata_id != '') {
										if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
											$this->cancelSkiData($permitData->id);
										}
										$permitData->cancelled_at = $permitRenewDate;
										$permitData->save();	
									}
								}else{
									if($permitData->facility_id==config('parkengage.WAILUKU_FACILITY')){
										$view='are.permit-renew-fail';  
									}else{
										$view='are-diamond.permit-renew-fail';  
									}	
									$subject = "Important: Action Required for Your Permit Payment";									
								}		
								$this->log->info("Mail subject " . $subject);
								
								// Send Mail
								$this->sendMail($permitData, $view, $subject);
							}
							$this->log->info("Heartland Payment Response to Permit Id #:" .  $permitData->account_number . "-- HL Gateway Response" . json_encode($paymentResponse));
						} catch (Exception $e) {
							$this->log->info("Heartland Payment Charge Error Response Permit Id #:" . $permitData->account_number . "--" . json_encode($e->getMessage()));
							if (str_contains($e->getMessage(), 'duplicate')) {

							// Send Mail
								$this->sendMail($permitData, $view, $subject);
							}
						} catch (Exception $e) {
							$this->log->info("Payment Charge Error Response Permit Id #:" . $permitData->account_number . "--" . json_encode($e->getMessage()));
							if (str_contains($e->getMessage(), 'duplicate')) {
						}else{
							$permitData->transaction_retry   = $permitData->transaction_retry + 1;
							$permitData->save();
								$expiry    = $this->paymentProfile->expiry;
							
								if ($expiry) {
									$expiry_data = str_split($expiry, 2);
									$this->paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
							} else {
									$this->paymentProfile->card_expiry = "-";
							}
							
							$day_of_month = date('d');
							if($day_of_month =='03'){
								$view='usm.permit-cancel-reminder';  
								//call skiData Api to cancel resuable ticket
								$subject = "Important Notice: Permit Cancellation";
								if ($permitData->skidata_id != '') {
									if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
										$this->cancelSkiData($permitData->id);
									}
									$permitData->cancelled_at = $permitRenewDate;
									$permitData->save();	
								}
							}else{
								if($permitData->facility_id==config('parkengage.WAILUKU_FACILITY')){
									$view='are.permit-renew-fail';  
								}else{
									$view='are-diamond.permit-renew-fail';  
								}	
								$subject = "Important: Action Required for Your Permit Payment";								
							}							
							
								$this->log->info("Mail subject " . $subject);
							// Send Mail
								$this->sendMail($permitData, $view, $subject);
							}
						}
					} else {
						$this->log->info("Payment Profile Not Found" . $permitData->account_number);
					}
				}
			}
            $this->log->info('Under handle function end.');    
		}catch (\Exception $th) {
            $this->log->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
        }
	}

	public function createPermitRequestHistoryNew($monthlyRequest)
	{
	  	if($monthlyRequest){
            $permitRenewDate = date('Y-m-d h:i:s');
			$permitData = new PermitRequestRenewHistory();

			$permitData->permit_request_id 		= $monthlyRequest->id;
			$permitData->user_id 				= $monthlyRequest->user_id;
			$permitData->facility_id 			= $monthlyRequest->facility_id;                
			$permitData->anet_transaction_id 	= $monthlyRequest->anet_transaction_id;
			$permitData->tracking_code 			= $monthlyRequest->tracking_code;
			$permitData->email 					= $monthlyRequest->email;                
			$permitData->name 					= $monthlyRequest->name;
			$permitData->phone 					= $monthlyRequest->phone;
			$permitData->permit_rate 			= $monthlyRequest->permit_rate;
			$permitData->permit_rate_id 		= $monthlyRequest->permit_rate_id;
			$permitData->approved_on 			= $monthlyRequest->approved_on;
			$permitData->account_number 		= $monthlyRequest->account_number;
			$permitData->monthly_duration_value = $monthlyRequest->monthly_duration_value;
			$permitData->no_of_days 			= $monthlyRequest->no_of_days;
			$permitData->partner_id 			= $monthlyRequest->partner_id;
			$permitData->license_number 		= $monthlyRequest->license_number;
			$permitData->mer_reference 			= $monthlyRequest->mer_reference;
			$permitData->image_front 			= $monthlyRequest->image_front;
			$permitData->image_back 			= $monthlyRequest->image_back;
			$permitData->user_consent 			= $monthlyRequest->user_consent;
			$permitData->vehicle_id 			= $monthlyRequest->vehicle_id;
			$permitData->is_admin 				= $monthlyRequest->is_admin;
			$permitData->ex_month 				= $monthlyRequest->ex_month;
			$permitData->ex_year 				= $monthlyRequest->ex_year;
			$permitData->payment_gateway 		= $monthlyRequest->payment_gateway;
			$permitData->permit_type 			= $monthlyRequest->permit_type;
			$permitData->is_payment_authrize 	= $monthlyRequest->is_payment_authrize;
			$permitData->session_id 			= $monthlyRequest->session_id;
			$permitData->permit_type_name 		= $monthlyRequest->permit_type_name;
			$permitData->skidata_id 			= $monthlyRequest->skidata_id;
			$permitData->skidata_value 			= $monthlyRequest->skidata_value;
			$permitData->acknowledge 			= $monthlyRequest->acknowledge;
			$permitData->facility_zone_id 		= $monthlyRequest->facility_zone_id;
			$permitData->desired_start_date 	= $monthlyRequest->desired_start_date;
			$permitData->desired_end_date 		= $monthlyRequest->desired_end_date;
			$permitData->cancelled_at 			= $monthlyRequest->cancelled_at;
			$permitData->created_at 			= $permitRenewDate;
			$permitData->updated_at 			= $permitRenewDate;
			$permitData->deleted_at 			= $monthlyRequest->deleted_at;

			$permitData->hid_card_number 		= $monthlyRequest->hid_card_number;
			$permitData->account_name 			= $monthlyRequest->account_name;
			$permitData->permit_final_amount 	= $monthlyRequest->permit_final_amount;
			$permitData->user_remark 			= $monthlyRequest->user_remark;
			$permitData->user_type_id 			= $monthlyRequest->user_type_id;
			$permitData->is_antipass_enabled 	= $monthlyRequest->is_antipass_enabled;
			$permitData->admin_user_id 			= $monthlyRequest->admin_user_id;
			$permitData->discount_amount 		= $monthlyRequest->discount_amount;
			$permitData->promocode 				= $monthlyRequest->promocode;
			$permitData->negotiated_amount 		= $monthlyRequest->negotiated_amount;
			$permitData->processing_fee         = $monthlyRequest->processing_fee;
			$permitData->refund_amount          = $monthlyRequest->refund_amount;
			$permitData->refund_type            = $monthlyRequest->refund_type;
			$permitData->refund_remarks         = $monthlyRequest->refund_remarks;
			$permitData->refund_date            = $monthlyRequest->refund_date;
			$permitData->refund_by              = $monthlyRequest->refund_by;
			$permitData->refund_transaction_id  = $monthlyRequest->refund_transaction_id;
			$permitData->refund_status          = $monthlyRequest->refund_status;
			$permitData->business_id          	= $monthlyRequest->business_id;
			$permitData->permit_prorate         = $monthlyRequest->permit_prorate;
			$permitData->service_prorate        = $monthlyRequest->service_prorate;
			$permitData->renew_type        		= $monthlyRequest->renew_type;

			$permitData->save();
        }
        return true;
	}

	public function cancelSkiData($permit_id)
	{
		try {
			$this->log->info("Get Permit Request id for cancel SkiData Autorenew" . json_encode($permit_id));
			$permit = PermitRequest::with(['facility'])->where("id", $permit_id)->first();
			//$guarage_code = $permit->facility->garage_code;
			$guarage_code = config('parkengage.SKIDATA_GUARAGE');
			$skidata_id =  $permit->skidata_id;
			$skiDataAuth = config('parkengage.SKIDATA_USERNAME') . ":" . config('parkengage.SKIDATA_PASSWORD');
			$dataAuthEncrption = base64_encode($skiDataAuth);
			$headers = [
				'Authorization: Basic ' . $dataAuthEncrption,
				'Content-Type: application/json',
			];
			$curl = curl_init();
			$url = config('parkengage.SKIDATA_CREATE_URL') . $guarage_code . '/' . $skidata_id;
			curl_setopt($curl, CURLOPT_URL, $url);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
			curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
			$response = curl_exec($curl);
			curl_close($curl);
			$response = json_decode($response);
			$this->log->info("SkIData success completed.");
			return $response;
		} catch (\Exception $e) {
			$msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
			$this->log->error($msg);
		}
	}

	public function sendMail($permitData, $view, $subject)
    {

		try{ 
			$brand_setting  = $this->brand_setting;
			$partner_name   =  "Diamond Parking at (808) 451-2792 or email <NAME_EMAIL>";
			$paymentProfile =  isset($this->paymentProfile) ? $this->paymentProfile : "";
			#PIMS-11336 DD
			if($this->business_user_id == $permitData->business_id){
				$userEmail = $permitData->business->email;
			}else{
				$userEmail = $permitData->user->email;
			}
			$this->log->info("Permit Email sending:". $userEmail);
            Mail::send(
				$view, ['data' => $permitData, 'brand_setting' => $brand_setting,'paymentProfile'=>$paymentProfile,'partner_name'=>$partner_name], 
				function ($message) use($permitData,$subject,$userEmail) {
					$message->to($userEmail)->subject($subject);
					$message->from(config('parkengage.default_sender_email'));
				}
			);            
            $this->log->info("email sent:". $userEmail);
		}catch (\Exception $e)
        {
			$this->log->info('Issue in email sending ' . $e->getMessage() . ' File Name :  ' . $e->getFile() . ' Line ' . $e->getLine());
		}
		return true;
	}
}
