<?php

namespace App\Jobs\USM;

use Exception;
use App\Jobs\Job;

use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;

use App\Classes\DatacapPaymentGateway;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;

use App\Models\User;
use App\Models\PermitRequest;
use App\Models\PermitRate;
use App\Models\PermitRequestRenewHistory;

use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;

use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\PermitServices;
use App\Models\PermitRateDescription;
use App\Models\ParkEngage\PermitRequestServiceMapping;

class UsmPermitCharge extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $permit;
    protected $log;
	protected $user;
	protected $permitRate;
	protected $partnerDetails;
	protected $partner_name;
	protected $facility_brand_setting;
	protected $brand_setting;
	protected $paymentProfile;
    protected $business_user_id;
	/**
     * Create a new job instance.
     *
     * @return void
     */

	public function __construct($permit,$log)
    {
        $logFactory                     = new LoggerFactory();
    $this->log                          = $logFactory->setPath('logs/usm/permitRenewal')->createLogger('permitrenewjob');    
        $this->permit                   = $permit;    
		$this->user                     = User::where('id', $this->permit->user_id)->first();
		$this->permitRate               = PermitRate::where('id',$this->permit->permit_rate_id)->first();
		// Partner Data
		$this->partnerDetails           = User::where('id',$this->permit->partner_id)->first();
        $this->partner_name             = "USM Parking Services";
		// Brand Settings
		$this->facility_brand_setting   = FacilityBrandSetting::where('facility_id', $this->permit->facility_id)->first();
		$this->brand_setting            = BrandSetting::where('user_id', $this->permit->partner_id)->first();
        
        #DD PIMS-11336 SUBORIDNATE
        if(!is_null($this->permit->business_id) && $this->permit->business_id > 0){
			$businessUser = User::where('id', $this->permit->business_id)->first();
		}		
		if(isset($businessUser) && $businessUser->payment_mode=='0'){
			$this->paymentProfile 			= HeartlandPaymentProfile::where('user_id', $this->permit->business_id)->where('is_default',1)->orderBy('id', 'DESC')->first();
			$this->business_user_id 		= $this->permit->business_id;
		}else{
			$this->paymentProfile 			= HeartlandPaymentProfile::where('user_id', $this->permit->user_id)->where('is_default',1)->orderBy('id', 'DESC')->first();
			$this->business_user_id 		= $this->permit->user_id;
		}
         #PIMS-11336 END
        if($this->facility_brand_setting){
            $this->paymentProfile->facility_logo_id = $this->facility_brand_setting->id;
        }else{
            $this->paymentProfile->logo_id  = $this->brand_setting->id;
        }
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
	{
		try{
			QueryBuilder::setCustomTimezone($this->permit->facility_id);
			
            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            
			$this->log->info('Under USM Permit Renew handle function start.');
			$this->log->info("Request For Permit Renewal :". $this->permit);
			
            $permitData = PermitRequest::with(['facility','user','PermitVehicle'])->where('id',$this->permit->id)->first();
             
            $this->log->info("Permit Rate Found " . $permitData->account_number . "--"   . json_encode($this->permitRate));


            if($this->permitRate->rate=='0.00'){
                $this->createPermitRequestHistoryNew($this->permit);
                $maxDays=date('t');
                $permitData->no_of_days          = $maxDays;    
                $permitData->permit_rate         = $this->permitRate->rate; 
                $permitData->desired_start_date  = $desired_start_date;
                $permitData->desired_end_date    = $desired_end_date;
				$permitData->transaction_retry   = ++$permitData->transaction_retry;
                $permitData->save();
                
                if($this->facility_brand_setting){
                    $permitData->facility_logo_id = $this->facility_brand_setting->id;
                }else{
                    $permitData->logo_id  = $this->brand_setting->id;
                }
                                
                $this->log->info("Free Permit Renew Permit Id #:" . $permitData->account_number );
				$subject = "Your Permit #". $permitData->account_number." has been Renewed Successfully";	
				
                $view = "usm.permit-renew-free";

                // Send Mail
			    $this->sendMail($permitData,$view,$subject,"","","","");
            }else if($this->permitRate->rate >"0.00"){
                if ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {

                }else if ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                }else if ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '4') {
                            
                    $paymentProfile = HeartlandPaymentProfile::where('user_id',$permitData->user_id)->first();
                    //dd($permitData->user_id,$permitData->id,$permitData->email,$paymentProfile);
                    $this->log->info("Payment Profile Data --" . $permitData->account_number . "--" . json_encode($paymentProfile));

                    $processing_fee = $permitData->facility->permit_processing_fee;
                    $permit_rate = $this->permitRate->rate;
                    $final_amount = $processing_fee + $permit_rate;

                    $permit_services = PermitRequestServiceMapping::where('permit_request_id',$permitData->id)->pluck('permit_service_id');
            
                    $services  = PermitServices::with([
                    'permit_service_criteria_mappings' => function($query) use ($permit_services) {
                        $query->whereIn('permit_service_id',$permit_services)
                        ->with('criteria');
                    }])
                    ->select('permit_services.*')
                    ->whereIn('id',$permit_services)
                    ->orderBy('permit_services.id', 'asc') 
                    ->get();
                    
                    if ($services) {                        
                        foreach ($services as $permitService) {
                            $final_amount += (float) $permitService->permit_service_rate;
                        }
                    }
                
                    if(count($services) > 0){
                        $services = $this->formatPermitServiceCriteria($services);
                    }

                    $amount = ($permitData->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;
                    //dd($permitData->user_id,$permitData->id,$permitData->email,$paymentProfile->token,$amount);
                    
                    if($paymentProfile){
                        $request = new Request([
                            'Amount'   => $amount,
                            'total'   => $amount,
                            'token' => $paymentProfile->token,
                            'zipcode' => $paymentProfile->zipcode,
                            'card_last_four' => $paymentProfile->card_last_four,
                            'expiration_date' => $paymentProfile->expiry,
                            'original_total' => $this->permitRate->permit_final_amount
                        ]);

                        $this->log->info("Payment Request to HL Data --" . $permitData->account_number . "--" . json_encode($request->all()));
                        
                        #end add parking time in email
                        $permit_validity = '';
                        $permitRateDescHour = array();
                        if($this->permitRate){
                            $permitRateDescHour = PermitRateDescription::find($this->permitRate->permit_rate_description_id);
                            if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")){
                                $permit_validity = '1 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")){
                                $permit_validity = '2 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")){
                                $permit_validity = 'Quarterly';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")){
                                $permit_validity = '4 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")){
                                $permit_validity = '5 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")){
                                $permit_validity = 'Half Yearly';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")){
                                $permit_validity = '7 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")){
                                $permit_validity = '8 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")){
                                $permit_validity = '9 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")){
                                $permit_validity = '10 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")){
                                $permit_validity = '11 Month';
                            }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")){
                                $permit_validity = '1 Year';
                            }else{
                                $permit_validity = '1 Month';
                            }                               
                        }
                        $paymentResponse = '---';	
                        try{
                            $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $permitData->facility); 
                            
                            if (isset($paymentResponse->responseMessage) && $paymentResponse->responseMessage == 'APPROVAL') {
                                $this->log->info("Heartland Payment Success Response to Permit Id #:" .  $permitData->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode. "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId);
			
                                $this->createPermitRequestHistoryNew($permitData);
                                $user_id = $permitData->user_id;
                                $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request,$paymentResponse, $this->business_user_id); #PIMS-11336
                                $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
                                $maxDays=date('t');
                                $permitData->no_of_days          = $maxDays;
                                $permitData->anet_transaction_id = $authorized_anet_transaction->id;
                                $permitData->permit_rate         = $this->permitRate->rate; 
                                $permitData->desired_start_date  = $desired_start_date;
                                $permitData->desired_end_date    = $desired_end_date;
                                $permitData->permit_final_amount = $final_amount;
                                $permitData->processing_fee      = $processing_fee;
                                $permitData->promocode           = NULL;
                                $permitData->discount_amount     = '0.00';
                                $permitData->transaction_retry   = $permitData->transaction_retry + 1;
                                $permitData->save();
                                #DD PIMS-11368	
                                if(isset($authorized_anet_transaction->id)){
                                    QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $permitData->account_number);
                                }
                                //user mail
                                
                                $expiry    = $paymentProfile->expiry;
                                if($expiry){
                                    $expiry_data = str_split($expiry, 2);                                
                                    $paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                }else{
                                    $paymentProfile->card_expiry = "-";
                                }
                            
                                $view = "usm.permit-renew"; 
                                $subject = "Autopayment Confirmation for Permit #". $permitData->account_number;

                                // Send Mail
                                $this->sendMail($permitData,$view,$subject,$services,$permitRateDescHour,$permit_validity);
                            }else{
                                $this->log->info("Heartland Payment Failure Response to Permit Id #:" .  $permitData->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode. "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId);
			
                                $permitData->transaction_retry = $permitData->transaction_retry + 1;
                                $permitData->save();
                                #DD PIMS-11368	                 
                                if(!isset($paymentResponse)){
                                    $paymentResponse = NULL;
                                }
                                QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "heartland", $permitData->account_number,'UsmPermitCharge',$permitData->user_id,null,$request);
                                // Failure mail send to user
                                $expiry    = $paymentProfile->expiry;
                                if($expiry){
                                    $expiry_data = str_split($expiry, 2);
                                    $paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                }else{
                                    $paymentProfile->card_expiry = "-";
                                }

                                $day_of_month = date('d');
                                if($day_of_month =='05'){
                                    $view='usm.permit-cancel-reminder';  
                                    $subject = "Important Notice: Permit Cancellation";
                                    
                                    $permitData->cancelled_at = $permitRenewDate;
                                    $permitData->save();                                            
                                }else{
                                    $view="usm.permit-renew-fail"; 
                                    $subject = "Important: Action Required for Your Permit Payment";
                                }
                            
                                $permitData->final_amount = $final_amount;
                                
                                // Send Mail
                                $this->sendMail($permitData,$view,$subject,$services,$permitRateDescHour,$permit_validity);
                            }	
                            $this->log->info("Heartland Payment Response to Permit Id #:" .  $permitData->account_number ."-- HL Gateway Response" . json_encode($paymentResponse));
											
                        }catch (Exception $e) {
                            $this->log->info("Heartland Payment Charge Error Response Permit Id #:" . $permitData->account_number . "--" . json_encode($e->getMessage()));
                            #DD PIMS-11368	                                    
                            QueryBuilder::setAllFailedTransactions(json_encode($e->getMessage()), "heartland", $permitData->account_number,'UsmPermitCharge',$permitData->user_id,null,$request);
                            if (str_contains($e->getMessage(), 'duplicate')) { 
                            }else{
                                $permitData->transaction_retry   = $permitData->transaction_retry + 1;
                                $permitData->save();
                                // Failure mail send to user
                                $expiry    = $paymentProfile->expiry;
                                if($expiry){
                                    $expiry_data = str_split($expiry, 2);
                                    $paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                }else{
                                    $paymentProfile->card_expiry = "-";
                                }
                               
                                $day_of_month = date('d');
                                if($day_of_month =='05'){
                                    $view='usm.permit-cancel-reminder';  
                                    $subject = "Important Notice: Permit Cancellation";
                                    
                                    $permitData->cancelled_at = $permitRenewDate;
                                    $permitData->save();                                            
                                }else{
                                    $view="usm.permit-renew-fail"; 
                                    $subject = "Important: Action Required for Your Permit Payment";
                                }
                                                               
                                $permitData->final_amount = $final_amount;
                                // Send Mail
                                $this->sendMail($permitData,$view,$subject,$services,$permitRateDescHour,$permit_validity);                                
                            }                                    
                        }                        
                    }
                }
            }
            $this->log->info('Under handle function end.');                
		}catch (\Exception $th) {
            $this->log->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
        }
    }

	public function createPermitRequestHistoryNew($monthlyRequest)
	{
	  	if($monthlyRequest){
            $permitRenewDate = date('Y-m-d h:i:s');
			$permitData = new PermitRequestRenewHistory();
                
			$permitData->permit_request_id 		= $monthlyRequest->id;
			$permitData->user_id 				= $monthlyRequest->user_id;
			$permitData->facility_id 			= $monthlyRequest->facility_id;                
			$permitData->anet_transaction_id 	= $monthlyRequest->anet_transaction_id;
			$permitData->tracking_code 			= $monthlyRequest->tracking_code;
			$permitData->email 					= $monthlyRequest->email;                
			$permitData->name 					= $monthlyRequest->name;
			$permitData->phone 					= $monthlyRequest->phone;
			$permitData->permit_rate 			= $monthlyRequest->permit_rate;
			$permitData->permit_rate_id 		= $monthlyRequest->permit_rate_id;
			$permitData->approved_on 			= $monthlyRequest->approved_on;
			$permitData->account_number 		= $monthlyRequest->account_number;
			$permitData->monthly_duration_value = $monthlyRequest->monthly_duration_value;
			$permitData->no_of_days 			= $monthlyRequest->no_of_days;
			$permitData->partner_id 			= $monthlyRequest->partner_id;
			$permitData->license_number 		= $monthlyRequest->license_number;
			$permitData->mer_reference 			= $monthlyRequest->mer_reference;
			$permitData->image_front 			= $monthlyRequest->image_front;
			$permitData->image_back 			= $monthlyRequest->image_back;
			$permitData->user_consent 			= $monthlyRequest->user_consent;
			$permitData->vehicle_id 			= $monthlyRequest->vehicle_id;
			$permitData->is_admin 				= $monthlyRequest->is_admin;
			$permitData->ex_month 				= $monthlyRequest->ex_month;
			$permitData->ex_year 				= $monthlyRequest->ex_year;
			$permitData->payment_gateway 		= $monthlyRequest->payment_gateway;
			$permitData->permit_type 			= $monthlyRequest->permit_type;
			$permitData->is_payment_authrize 	= $monthlyRequest->is_payment_authrize;
			$permitData->session_id 			= $monthlyRequest->session_id;
			$permitData->permit_type_name 		= $monthlyRequest->permit_type_name;
			$permitData->skidata_id 			= $monthlyRequest->skidata_id;
			$permitData->skidata_value 			= $monthlyRequest->skidata_value;
			$permitData->acknowledge 			= $monthlyRequest->acknowledge;
			$permitData->facility_zone_id 		= $monthlyRequest->facility_zone_id;
			$permitData->desired_start_date 	= $monthlyRequest->desired_start_date;
			$permitData->desired_end_date 		= $monthlyRequest->desired_end_date;
			$permitData->cancelled_at 			= $monthlyRequest->cancelled_at;
			$permitData->created_at 			= $permitRenewDate;
			$permitData->updated_at 			= $permitRenewDate;
			$permitData->deleted_at 			= $monthlyRequest->deleted_at;
                
			$permitData->hid_card_number 		= $monthlyRequest->hid_card_number;
			$permitData->account_name 			= $monthlyRequest->account_name;
			$permitData->permit_final_amount 	= $monthlyRequest->permit_final_amount;
			$permitData->user_remark 			= $monthlyRequest->user_remark;
			$permitData->user_type_id 			= $monthlyRequest->user_type_id;
			$permitData->is_antipass_enabled 	= $monthlyRequest->is_antipass_enabled;
			$permitData->admin_user_id 			= $monthlyRequest->admin_user_id;
			$permitData->discount_amount 		= $monthlyRequest->discount_amount;
			$permitData->promocode 				= $monthlyRequest->promocode;
			$permitData->negotiated_amount 		= $monthlyRequest->negotiated_amount;
			$permitData->processing_fee         = $monthlyRequest->processing_fee; 
			$permitData->refund_amount          = $monthlyRequest->refund_amount;
			$permitData->refund_type            = $monthlyRequest->refund_type;
			$permitData->refund_remarks         = $monthlyRequest->refund_remarks;
			$permitData->refund_date            = $monthlyRequest->refund_date;
			$permitData->refund_by              = $monthlyRequest->refund_by;
			$permitData->refund_transaction_id  = $monthlyRequest->refund_transaction_id;
			$permitData->refund_status          = $monthlyRequest->refund_status;
            $permitData->business_id          	= $monthlyRequest->business_id;
			$permitData->permit_prorate         = $monthlyRequest->permit_prorate;
			$permitData->service_prorate        = $monthlyRequest->service_prorate;
			$permitData->renew_type        		= $monthlyRequest->renew_type;

            $permitData->save();
                       
        }
        return true;
	}

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        
		$i=0;
		foreach ($services as $service) {
			if(count($service->permit_service_criteria_mappings) > 0){
				$formatted = [];
				foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
					$item = $permit_service_criteria_mapping->criteria;
					$days = explode(',', $item->days);
					sort($days); // Sort days to match the sequence of $allDays
					if ($days == $allDays) {
						$dayNamesStr = 'All Days';
					} else {
						$dayNames = array_map(function($day) use ($daysMap) {
							return $daysMap[$day];
						}, $days);
						$dayNamesStr = implode(',', $dayNames);
					}
	
					$entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
					$entry_time_end = date('h:iA', strtotime($item->entry_time_end));
					$exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
			
					// Adjust exit time if it's greater than 24 hours
					$exit_time = explode(":",$item->exit_time_end);
					if($exit_time[0]>23){
						$next_hr = $exit_time[0] - 24;
						$item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
						$exit_time_overflow = ' (next day)';
					}else{
						$exit_time_overflow = '';
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
					}
				
					$formatted[] = [
						'days' => $dayNamesStr,
						'entry_time_begin' => $entry_time_begin,
						'entry_time_end' => $entry_time_end,
						'exit_time_begin' => $exit_time_begin,
						'exit_time_end' => $exit_time_end . $exit_time_overflow,
					];
				}
				$services[$i]->criteria=$formatted;
			}
			$i++;
		}

        return $services;
    }

    public function sendMail($permitData,$view,$subject,$services="",$permitRateDescHour="",$permit_validity=""){

		try{
            $brand_setting  = $this->brand_setting;  
            $paymentProfile =  isset($this->paymentProfile) ? $this->paymentProfile : "";
            #PIMS-11336 DD
			if($this->business_user_id == $permitData->business_id){
				$userEmail = $permitData->business->email;
			}else{
				$userEmail = $permitData->user->email;
			}
			$this->log->info("Permit Email sending:". $userEmail);  
            Mail::send(
                $view, ['data' => $permitData, 'brand_setting' => $brand_setting,'permitRate' => $this->permitRate,'paymentProfile'=>$paymentProfile,'services' => $services,'permitRateDescHour' => $permitRateDescHour,'permit_validity'=>$permit_validity,'partner_details' => $this->partnerDetails,'partner_name'=>$this->partner_name], 
                function ($message) use($permitData,$subject,$userEmail) {
                    $message->to([$userEmail])->subject($subject);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("email sent:". $userEmail);
		}catch (\Exception $e)
        {
			$this->log->info('Issue in email sending ' . $e->getMessage() . ' File Name :  ' . $e->getFile() . ' Line ' . $e->getLine());
        }
		return true;
	}

}
