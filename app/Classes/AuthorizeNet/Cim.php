<?php

namespace App\Classes\AuthorizeNet;

use App\Models\User;
use App\Models\UserCim;
use App\Models\MonthlyParkingCim;
use App\Models\PaymentProfile;
use App\Models\ExpirationDate;
use App\Models\ParkEngage\AutopayMethod;

use App\Classes\AuthorizeNet\Authentication;
use App\Classes\AuthorizeNet;
use App\Classes\ArApi;

use App\Exceptions\AuthorizeNetException;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;

use net\authorize\api\constants\ANetEnvironment as AnetConstants;
use net\authorize\api\contract\v1 as AnetApi;
use net\authorize\api\controller as AnetController;
use net\authorize\api\controller\CreateCustomerProfileController;
use net\authorize\api\controller\GetCustomerPaymentProfileController;
use net\authorize\api\controller\GetCustomerProfileController;

/**
 * <PERSON>les creating authorizenet CIMS and payment profiles.
 * CIMS and payment profiles are saved to DB after creation.
 */
class Cim extends AuthorizeNet
{
    // Auth net code returned if we try to create a CIM and that CIM already existsa
    const DUPLICATE_RECORD = 'E00039';

    /**
     * Response to a request to create a CIM
     *
     * @var [type]
     */
    protected $cimResponse;

    /**
     * Response to a request to create a payment profile
     *
     * @var [type]
     */
    protected $paymentProfileResponse;

    /**
     * Customer profile
     *
     * @var AnetApi\CustomerProfileType()
     */
    protected $profile;

    /**
     * Payment profile that can be added to an existing cim
     *
     * @var AnetAPI\CustomerPaymentProfileType
     */
    protected $cimPaymentProfile;

    /**
     * Get the customer profile information
     *
     * @param  $profileId integer Id of the customer profile to retrieve. If you do not pass this,
     *                           the profile $this->user will be returned
     * @return [type] [description]
     */
    public function getCustomerProfile($profileId = false, $paymentDetails = null)
    {
        if (!$profileId) { // Default to getting CIM for current user
            $profileId = $this->getUserCimId();

            if (!$profileId) {
                return false;
            }
        }

        $request = new AnetApi\GetCustomerProfileRequest();
        if($paymentDetails){
          $this->merchantAuthentication->setName($paymentDetails->authorize_net_login);
        $this->merchantAuthentication->setTransactionKey($paymentDetails->authorize_net_transaction_key);
        $this->authNetEnvironment = $paymentDetails->authorize_net_live == '1' ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
          $request->setMerchantAuthentication($this->merchantAuthentication);  
        }else{
          $request->setMerchantAuthentication($this->merchantAuthentication);  
        }
        
        $request->setCustomerProfileId($profileId);
        $controller = new AnetController\GetCustomerProfileController($request);

        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        if (!$response) {
            throw new AuthorizeNetException('Invalid response returned from Authorize.net');
        }

        if ($response->getMessages()->getResultCode() !== 'Ok') {
            $errorMessages = $response->getMessages()->getMessage();
            throw new AuthorizeNetException("Response : " . $errorMessages[0]->getCode() . "  " .$errorMessages[0]->getText() . "\n");
        }

        $profile = $response->getProfile();
        $profileArray = $this->arrayFromProfile($profile);

        // Save payment ID for any payments we don't have stored locally
        $this->savePayments($profileArray['payments']);

        return $profileArray;
    }


    /*public function setAauthenticationForPartner($partnerPaymentDetails)
    {
        $this->partner = true;
        $this->partnerDetails = $partnerPaymentDetails;
        $this->merchantAuthentication->setName($partnerPaymentDetails->authorize_net_login);
        $this->merchantAuthentication->setTransactionKey($partnerPaymentDetails->authorize_net_transaction_key);
        $this->authNetEnvironment = env('RESERVATIONS_AUTHORIZE_NET_LIVE') ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
        return $this;
    }*/

    public function isPartner($partnerPaymentDetails)
    {
        $this->partner = true;
        $this->partnerDetails = $partnerPaymentDetails;
        $this->merchantAuthentication->setName($partnerPaymentDetails->authorize_net_login);
        $this->merchantAuthentication->setTransactionKey($partnerPaymentDetails->authorize_net_transaction_key);
        //$this->authNetEnvironment = env('RESERVATIONS_AUTHORIZE_NET_LIVE') ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
        $this->authNetEnvironment = $partnerPaymentDetails->authorize_net_live == '1' ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
        return $this;
    }

    /**
     * Loop through payments returned from auth net and save locally if we don't have them
     *
     * @param  array $payments [description]
     * @return [type]           [description]
     */
    protected function savePayments(array $payments)
    {
        $cim = $this->getUserCim();

        if (!$cim) {
            return false;
        }

        foreach ($payments as $payment) {
            $paymentProfile = PaymentProfile::firstOrNew(['payment_profile' => $payment['payment_profile_id']]);

            if ($paymentProfile->exists) {
                continue; // Payment already saved
            }

            $paymentProfile->payment_profile =  $payment['payment_profile_id'];
            $cim->paymentProfiles()->save($paymentProfile);
        }
    }

    /**
     * Verify that a user owns the given payment ID
     *
     * @param  [type] $paymentProfileId [description]
     * @return [type]                   [description]
     */
    public function verifyUserOwnsPayment($paymentProfileId, $paymentDetails = null)
    {
        if (!$this->getUser()) {
            throw new AuthorizeNetException('No user set to verify payment profile against.');
        }
        $cim = $this->getCustomerProfile(false, $paymentDetails);
        // $paymentIds = array_map(
        //     function ($payment) {
        //         return $payment['payment_profile_id'];
        //     }, $cim['payments']
        // );
        if($cim) {
            $paymentIds = array_map(
                function ($payment) {
                    return $payment['payment_profile_id'];
                }, $cim['payments']
            );
        } else {
            return true;
        }

        if (!in_array($paymentProfileId, $paymentIds)) {
            throw new AuthorizeNetException('Payment profile ID does not match any payment profiles for current user.');
        }
    }

    /**
     * Given a payment profile retrieved from auth net, extract relevant information
     * about the credit card and payment profile ID
     *
     * @param  [type] $profile [description]
     * @return [type]          [description]
     */
    protected function arrayFromProfile($profile)
    {
        $paymentProfiles = $profile->getPaymentProfiles();

        if (!$paymentProfiles || !count($paymentProfiles)) {
            $payments = [];
        } else {
            foreach ($paymentProfiles as $payment) {
                $payments[] = $this->arrayFromPaymentProfile($payment);
            }
        }

        return [
            'email' => $profile->getEmail(),
            'profile_id' => $profile->getCustomerProfileId(),
            'payments' => $payments
        ];
    }

    /**
     * Given a payemnt profile, convert it into an array that we can consume
     *
     * @param  PaymentProfileType $payment The payment profile from authorize.net response
     * @return array relevant information from payemnt profile
     */
    protected function arrayFromPaymentProfile($payment)
    {
        $data = [
            'payment_profile_id' => $payment->getCustomerPaymentProfileId(),
            'expiration_year' => null,
            'expiration_month' => null,
            'address' => null,
            'card' => null,
            'bank' => null
        ];

        $address = $payment->getBillTo() ?: null;

        if ($address) {
            $data['address'] = [
                'first_name' => $address->getFirstName(),
                'last_name' => $address->getLastName(),
                'company' => $address->getCompany(),
                'address' => $address->getAddress(),
                'city' => $address->getCity(),
                'state' => $address->getState(),
                'zip' => $address->getZip(),
                'country' => $address->getCountry()
            ];
        }

        $card = $payment->getPayment()->getCreditCard();
        $bank = $payment->getPayment()->getBankAccount();

        
                
        if (!$card && !$bank) {
            return $data;
        }

        if ($card) {
            $data['card'] = [
                'card_number' => $card->getcardNumber(),
                'card_expiration' => $card->getExpirationDate(),
                'card_type' => $card->getCardType(),
            ];
        } else {
             $data['bank'] = [
                 'bank_routing' => $bank->getRoutingNumber(),
                 'bank_account' => $bank->getAccountNumber(),
                 'bank_type' => $bank->getAccountType(),
                 'bank_name' => $bank->getNameOnAccount()
             ];
        }
        /** code written for expiration **/
      $paymentProfileIDExpiration = PaymentProfile::where('payment_profile', $data['payment_profile_id'])->first();
      if($paymentProfileIDExpiration)
      {
        $expirationDate = ExpirationDate::where('payment_profile_id', $paymentProfileIDExpiration->id)->first();
        if($expirationDate)
        {
            $data['expiration_month'] = $expirationDate->expiration_month;
            $data['expiration_year'] = $expirationDate->expiration_year;
        }
      }
        return $data;
    }

    /**
     * Create an auth net customer profile
     *
     * @return [type] [description]
     */
    public function createCustomerProfile()
    {
        if (!$this->getUserId()) {
            return false;
        }

        $user = $this->user ?: $this->monthlyParkingUser;

        $description = $this->monthlyParkingUser
                            ? $user->name . ' Facility ID: ' . $this->facility->id . $user->account_number
                            : $user->name;

        $description = $this->reservation ? $user->name . ' reservations' : $description; // reservations

        $email = $this->user ? $this->user->email : $this->monthlyParkingUser->user->email;

        // Set user basic info
        $this->profile = new AnetApi\CustomerProfileType();
        $this->profile->setDescription($description);
        $this->profile->setMerchantCustomerId(substr("M_" . $email, 0, 20));
        $this->profile->setEmail($email);

        // Add payment info
        $paymentProfile = new AnetApi\CustomerPaymentProfileType();
        $paymentProfile->setBillTo($this->billingAddress);
        $paymentProfile->setPayment($this->payment);

        $this->profile->setPaymentProfiles([$paymentProfile]);

        return $this;
    }


    public function createCustomerProfileForAutopay()
    {
        if (!$this->getUserId()) {
            return false;
        }

        $user = $this->user;

        $description = $user->name.'MembershipPayment';

        
        $email = $this->user->email;

        // Set user basic info
        $this->profile = new AnetApi\CustomerProfileType();
        $this->profile->setDescription($description);
        $this->profile->setMerchantCustomerId(substr("M_" . $email, 0, 20));
        $this->profile->setEmail($email);

        // Add payment info
        $paymentProfile = new AnetApi\CustomerPaymentProfileType();
        $paymentProfile->setBillTo($this->billingAddress);
        $paymentProfile->setPayment($this->payment);

        $this->profile->setPaymentProfiles([$paymentProfile]);

        return $this;
    }
    /**
     * Fire off the request to create this customer profile
     *
     * @return [type] [description]
     */
    public function executeCustomerProfileRequest()
    {
        // Set up request
        $request = new AnetApi\CreateCustomerProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setRefId($this->refId);
        $request->setProfile($this->profile);
        $request->setValidationMode('testMode'); // Runs basic checks on payment

        $controller = new AnetController\CreateCustomerProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        $this->cimResponse = $response;

        // Will delete the profile on auth net if it returns that this is a duplicate record
        $isDuplicate = $this->handleDuplicateCustomerProfileResponse($response);

        if ($isDuplicate) {
            return $this->executeCustomerProfileRequest();
        }

        $this->checkAuthNetResponse($response);

        $paymentProfiles = $response->getCustomerPaymentProfileIdList();

        // Success, save the Cim and the payment profiles to the database
        $this->saveCim();

        $paymentProfile = array_shift($paymentProfiles);

        // Credit card must always be saved after the payment profile
        $this->savePaymentProfile($paymentProfile);
        $this->saveCreditCardExpiration($paymentProfile);

        return [
            'customer_profile_id' => $response->getCustomerProfileId(),
            'payment_profile_id' => $paymentProfile
        ];
    }
    /**
     * Fire off the request to create this customer profile
     *
     * @return [type] [description]
     */
    public function executeCustomerProfileRequestAddNewMethod($partnerPaymentDetails = null)
    {
        // Set up request
        $request = new AnetApi\CreateCustomerProfileRequest();
        if($partnerPaymentDetails){
          $this->merchantAuthentication->setName($partnerPaymentDetails->authorize_net_login);
        $this->merchantAuthentication->setTransactionKey($partnerPaymentDetails->authorize_net_transaction_key);
          //$request->setMerchantAuthentication($partnerPaymentDetails->authorize_net_live == '1' ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX);  
        $this->authNetEnvironment = $partnerPaymentDetails->authorize_net_live == '1' ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
        $request->setMerchantAuthentication($this->merchantAuthentication);
        }else{
          $request->setMerchantAuthentication($this->merchantAuthentication);
        }
        $request->setRefId($this->refId);
        $request->setProfile($this->profile);
        $request->setValidationMode('testMode'); // Runs basic checks on payment

        $controller = new AnetController\CreateCustomerProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        $this->cimResponse = $response;

        // Will delete the profile on auth net if it returns that this is a duplicate record
        $isDuplicate = $this->handleDuplicateCustomerProfileResponse($response);

        if ($isDuplicate) {
            return $this->executeCustomerProfileRequest();
        }

        $this->checkAuthNetResponse($response);

        $paymentProfiles = $response->getCustomerPaymentProfileIdList();

        // Success, save the Cim and the payment profiles to the database
        $this->saveCim();

        $paymentProfile = array_shift($paymentProfiles);

        // Credit card must always be saved after the payment profile
        $this->savePaymentProfile($paymentProfile);
        $this->saveCreditCardExpiration($paymentProfile);

        return [
            'customer_profile_id' => $response->getCustomerProfileId(),
            'payment_profile_id' => $paymentProfile
        ];
    }

    /**
     * Update email address of the currently set user or monthly parking user
     */
    public function updateCustomerEmail($email)
    {
        if (!$this->getUserCimId()) {
            return;
        }

        $updateCustomerProfile = new AnetApi\CustomerProfileExType();
        $updateCustomerProfile->setCustomerProfileId($this->getUserCimId());
        $updateCustomerProfile->setEmail($email);

        $request = new AnetApi\UpdateCustomerProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setProfile($updateCustomerProfile);

        $controller = new AnetController\UpdateCustomerProfileController($request);

        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        $this->checkAuthNetResponse($response);
    }

    /**
     * Delete a customer profile. This should only be used when we get a duplicate response
     * from authorize.net
     *
     * @return [type] [description]
     */
    protected function deleteCustomerProfile($cim)
    {
        $request = new AnetApi\DeleteCustomerProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setCustomerProfileId($cim);

        $controller = new AnetController\DeleteCustomerProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        $this->checkAuthNetResponse($response);

        return true;
    }

    /**
     * Get a payment profile from auth net.
     *
     * @param  $paymentProfileId The auth net payment profile ID of the profile to retrieve
     * @return array
     */
    public function getPaymentProfile($paymentProfileId)
    {
        $request = new AnetApi\GetCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setCustomerProfileId($this->getUserCimId());
        $request->setCustomerPaymentProfileId($paymentProfileId);

        $controller = new AnetController\GetCustomerPaymentProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        // Check our response for errors
        $this->checkAuthNetResponse($response);

        // Convert the profile to an array so we can use it
        $profile = $response->getPaymentProfile();
        $data = $this->arrayFromPaymentProfile($profile);

         // Set our local data from the response
        if ($data['address']) {
            $this->setBillingAddress($data['address']);
        }

        if ($card = $data['card']) {
            $this->setCreditCard($card['card_number'], $card['card_expiration'], null);
        }

        if ($bank = $data['bank']) {
            $this->setBankAccount($bank['bank_type'], $bank['bank_routing'], $bank['bank_account'], $bank['bank_name']);
        }

        return $data;
    }

    public function getPaymentLastFour()
    {
        $payment = $this->getPaymentProfile($this->paymentProfile->getPaymentProfile()->getPaymentProfileId());
        return isset($payment['card']) ? $payment['card']['card_number'] : $payment['bank']['bank_account'];
    }

    /**
     * Build a new payment profile for a user that already has a cim
     *
     * @return [type] [description]
     */
    public function createPaymentProfile()
    {
        $this->cimPaymentProfile = new AnetApi\CustomerPaymentProfileType();
        $this->cimPaymentProfile->setCustomerType('individual');
        $this->cimPaymentProfile->setPayment($this->payment);

        if (isset($this->billingAddress)) {
            $this->cimPaymentProfile->setBillTo($this->billingAddress);
        }

        return $this;
    }

    /**
     * Send the API request to create a new payment profile that was set up through $this->createPaymentProfile()
     *
     * @return [type] [description]
     */
    public function executePaymentProfileRequest()
    {
        $cimId = $this->getUserCimId();

        if (!$cimId) {
            throw new AuthorizeNetException('No user set for profile create operation');
        }

        $request = new AnetApi\CreateCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setCustomerProfileId($cimId);
        $request->setPaymentProfile($this->cimPaymentProfile);
        $request->setValidationMode('testMode'); // Runs basic checks on payment

        $controller = new AnetController\CreateCustomerPaymentProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);
        $this->paymentProfileResponse = $response;
        // Will delete the profile on auth net if it returns that this is a duplicate record
        $isDuplicate = $this->handleDuplicatePaymentProfileResponse($response);

        if ($isDuplicate) {
           
            return false;
        }

        $this->checkAuthNetResponse($response);

        $paymentProfileId = $response->getCustomerPaymentProfileId();

        $this->savePaymentProfile($paymentProfileId);
        $this->saveCreditCardExpiration($paymentProfileId);

        return [
            'payment_profile_id' => $paymentProfileId
        ];
    }
    /**
     * Send the API request to create a new payment profile that was set up through $this->createPaymentProfile()
     *
     * @return [type] [description]
     */
    public function executePaymentProfileRequestAddNewMethod()
    {
        $cimId = $this->getUserCimId();

        if (!$cimId) {
            throw new AuthorizeNetException('No user set for profile create operation');
        }

        $request = new AnetApi\CreateCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setCustomerProfileId($cimId);
        $request->setPaymentProfile($this->cimPaymentProfile);
        $request->setValidationMode('testMode'); // Runs basic checks on payment

        $controller = new AnetController\CreateCustomerPaymentProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);
        $this->paymentProfileResponse = $response;
        // Will delete the profile on auth net if it returns that this is a duplicate record
        $isDuplicate = $this->handleDuplicatePaymentProfileResponseAddNewMethod($response);

        if ($isDuplicate) {
           
            return false;
        }

        $this->checkAuthNetResponse($response);

        $paymentProfileId = $response->getCustomerPaymentProfileId();

        $this->savePaymentProfile($paymentProfileId);
        $this->saveCreditCardExpiration($paymentProfileId);

        return [
            'payment_profile_id' => $paymentProfileId
        ];
    }

    /**
     * Update the payment profile set on this object with currently set data.
     *
     * @return boolean true/false for succes/failure
     */
    public function updatePaymentProfile($paymentProfileId)
    {
        $cimId = $this->getUserCimId();

        if (!$cimId) {
            throw new AuthorizeNetException('No user set for profile delete operation');
        }

        // Set up our request with data in this object
        $request = new AnetApi\UpdateCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setCustomerProfileId($cimId);

        $paymentProfile = new AnetAPI\CustomerPaymentProfileExType();
        $paymentProfile->setCustomerPaymentProfileId($paymentProfileId);
        $paymentProfile->setBillTo($this->billingAddress);
        $paymentProfile->setPayment($this->payment);

        $request->setPaymentProfile($paymentProfile);

        // Send request
        $controller = new AnetController\UpdateCustomerPaymentProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        $this->checkAuthNetResponse($response);

        // Update our expiration date if relevant
        $this->saveCreditCardExpiration($paymentProfileId);

        return true;
    }

    /**
     * Delete the payment profile given in the args for the currently set user
     *
     * @param  [type] $paymentProfileId The full auth net payment profile ID
     * @return [type]                   [description]
     */
    public function deleteCustomerPaymentProfile($paymentProfileId, $paymentDetails = null)
    {
        $cimId = $this->getUserCimId();

        if (!$cimId) {
            throw new AuthorizeNetException('No user set for profile delete operation');
        }

        $request = new AnetApi\DeleteCustomerPaymentProfileRequest();
        if($paymentDetails){
          $this->merchantAuthentication->setName($paymentDetails->authorize_net_login);
        $this->merchantAuthentication->setTransactionKey($paymentDetails->authorize_net_transaction_key);
          $request->setMerchantAuthentication($this->merchantAuthentication);  
        }else{
          $request->setMerchantAuthentication($this->merchantAuthentication);
        }
        $request->setCustomerProfileId($cimId);
        $request->setCustomerPaymentProfileId($paymentProfileId);

        $controller = new AnetController\DeleteCustomerPaymentProfileController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        // Delete the payment profile from local database
        $this->deletePaymentProfile($paymentProfileId);

        $this->checkAuthNetResponse($response);

        return true;
    }

    public function getBasicChargeDetails()
    {
        if (!isset($this->paymentProfile) || !$this->paymentProfile) {
            return parent::getBasicChargeDetails();
        }

        $profileId = $this->paymentProfile->getPaymentProfile()->getPaymentProfileId();

        // Sets credit card or bank account on the current object from the payment profile so we can get charge method
        $this->getPaymentProfile($profileId);

        // Save our payment profile
        $details = parent::getBasicChargeDetails();

        if ($payment = $this->payment->getCreditCard()) {
            $paymentProfile = PaymentProfile::where('payment_profile', $profileId)->first();
            if ($paymentProfile && $paymentProfile->expirationDate) {
                $details['expiration'] = $paymentProfile->expirationDate->formatted;
            }
        }

        return $details;
    }

    /**
     * It's possible for a record to exist in auth net that we don't have in the database.
     * Checks the auth net response for this response code and parses their ill-formatted response
     * so we can save the duplicate to the database
     *
     * @param  [type] $response [description]
     * @return [type]           [description]
     */
    protected function handleDuplicateCustomerProfileResponse($response)
    {
        if ($response->getMessages()->getResultCode() === 'Ok') {
            return false;
        }

        $message = $response->getMessages()->getMessage();
        $code = $message[0]->getCode();

        if ($code !== self::DUPLICATE_RECORD) {
            return false;
        }

        // Error text returned will be of the format "A duplicate record with ID {integer} already exists."
        // The duplicate record ID is not returned in any other way, and authorize.net doesn't have a way to search for a
        // cim ID programmatically, so we have to parse this text.
        $text = $message[0]->getText();
        $textArray = explode(' ', $text);
        $idIndex = array_search('ID', $textArray);
        $cim = $textArray[$idIndex + 1];

        // Delete the cim so we can resend our create request
        $this->deleteCustomerProfile($cim);

        return true;
    }

    /**
     * It's possible for a record to exist in auth net that we don't have in the database.
     * Checks the auth net response for this response code and parses their ill-formatted response
     * so we can save the duplicate to the database
     *
     * @param  [type] $response [description]
     * @return [type]           [description]
     */
    protected function handleDuplicatePaymentProfileResponse($response)
    {
        if ($response->getMessages()->getResultCode() === 'Ok') {
            return false;
        }

        $message = $response->getMessages()->getMessage();
        $code = $message[0]->getCode();

        if ($code !== self::DUPLICATE_RECORD) {
            return false;
        }

        // Error text returned will be of the format "A duplicate record with ID {integer} already exists."
        // The duplicate record ID is not returned in any other way, and authorize.net doesn't have a way to search for a
        // cim ID programmatically, so we have to parse this text.
        $text = $message[0]->getText();
        $textArray = explode(' ', $text);
        $idIndex = array_search('ID', $textArray);
        $cim = $textArray[$idIndex + 1];

        // Delete the cim so we can resend our create request
        $profileId = $response->getCustomerPaymentProfileId();
        $this->deletePaymentProfile($profileId);

        return true;
    }
    /**
     * It's possible for a record to exist in auth net that we don't have in the database.
     * Checks the auth net response for this response code and parses their ill-formatted response
     * so we can save the duplicate to the database
     *
     * @param  [type] $response [description]
     * @return [type]           [description]
     */
    protected function handleDuplicatePaymentProfileResponseAddNewMethod($response)
    {
        if ($response->getMessages()->getResultCode() === 'Ok') {
            return false;
        }

        $message = $response->getMessages()->getMessage();
        $code = $message[0]->getCode();

        if ($code !== self::DUPLICATE_RECORD) {
            return false;
        }

        // Error text returned will be of the format "A duplicate record with ID {integer} already exists."
        // The duplicate record ID is not returned in any other way, and authorize.net doesn't have a way to search for a
        // cim ID programmatically, so we have to parse this text.
        $text = $message[0]->getText();
        $textArray = explode(' ', $text);
        $idIndex = array_search('ID', $textArray);
        $cim = $textArray[$idIndex + 1];
        if(isset($text))
        {
          if($text == "A duplicate customer payment profile already exists."){
           throw new ApiGenericException("A duplicate card already exists.");
          }
          throw new ApiGenericException($text);
        }
//        // Delete the cim so we can resend our create request
//        $profileId = $response->getCustomerPaymentProfileId();
//        $this->deletePaymentProfile($profileId);

        return true;
    }

    /**
     * Save a successful Cim request information to the database
     *
     * @return [type] [description]
     */
    protected function saveCim($id = false)
    {
        if (!$this->cimResponse && !$id) {
            return false;
        }

        if (!$id) {
            $id = $this->cimResponse->getCustomerProfileId();
        }

        // Need to save different types of cims if we are using a regular user
        // or a monthly parking user
        if ($this->user) {
            $cim = UserCim::create(
                [
                'user_id' => $this->user->id,
                'cim_id' => $id
                ]
            );
        }

        if ($this->monthlyParkingUser) {
            $cim = MonthlyParkingCim::create(
                [
                'mp_user_id' => $this->monthlyParkingUser->id,
                'cim_id' => $id
                ]
            );
        }

        return $this;
    }

    /**
     * Save payment profile ID when it is created
     *
     * @return S [description]
     */
    protected function savePaymentProfile($paymentProfileId)
    {
        $paymentProfile = new PaymentProfile();
        $paymentProfile->payment_profile = $paymentProfileId;

        if ($this->user) {
            $cim = $this->user->cim;
            if (!$cim) {
                $cim = UserCim::where('user_id', $this->user->id)->first();
            }
            $cim->paymentProfiles()->save($paymentProfile);
        }

        if ($this->monthlyParkingUser) {
            $cim = MonthlyParkingCim::where('mp_user_id', $this->monthlyParkingUser->id)->first();
            $cim->paymentProfiles()->save($paymentProfile);
        }
    }

    /**
     * Save credit card expiration date to the database so we can inform
     * users when their credit card is about to expire
     *
     * @param  $paymentProfileId string The full auth  net payment profile ID
     * @return $expirationDate Created expiration date
     */
    protected function saveCreditCardExpiration($paymentProfileId)
    {
        if (!$this->creditCard->getCardNumber()) {
            return false;
        }

        $expiration = $this->creditCard->getExpirationDate();

        $paymentProfile = PaymentProfile::where('payment_profile', $paymentProfileId)->first();

        if (!$paymentProfile) {
            throw new NotFoundException('No payment profile to attach expiration to.');
        }

        $expiration = (string) $expiration;

        // Get month and year values from the expiration date
        $month = (strlen($expiration) === 4) ? substr($expiration, 0, 2) : substr($expiration, 0, 1);
        $year = substr($expiration, -2);

        // Update existing date or create new one
        $date = ExpirationDate::where('payment_profile_id', $paymentProfile->id)->first();

        if (!$date) {
            $date = new ExpirationDate(['payment_profile_id' => $paymentProfile->id]);
        }

        $date->expiration_month = $month;
        $date->expiration_year = $year;
        $date->save();

        return $date;
    }

    /**
     * Delete the given payment profile from the database
     *
     * @param  integer $paymentProfileId Full auth net payment profile id
     * @return [type]                   [description]
     */
    protected function deletePaymentProfile($paymentProfile)
    {
        $profile = PaymentProfile::where('payment_profile', $paymentProfile)->first();
        
        if (!$profile) {
            return;
        }

        // delete any expiration dates associated with this profile
        $expiration = $profile->expirationDate;

        if ($expiration) {
            $expiration->delete();
        }

        // Delete any autopay methods associated with this payment profile
        $autopayMethod = $profile->autopayMethod;
        if ($autopayMethod) {
            // Delete all autopay methods for this user to prevent errors with disabling one but not both methods
            AutopayMethod::where('user_membership_id', $autopayMethod->user_membership_id)->delete();
            // ArApi::autopayStatus($this->monthlyParkingUser->account_number, ArApi::PAYMENT_METHOD_DISABLED);
        }

        return $profile->delete();
    }
}
