<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use Illuminate\Support\Facades\Auth;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Request;
// This class allows masquerade functionality

use App\Models\UserSocialLogin;
use Illuminate\Support\Facades\Hash;
use App\Http\Helpers\QueryBuilder;

class PasswordGrantVerifier
{
    protected $countryCode;

    public function verify($username, $password)
    {
        // Get country Code
        $this->countryCode = QueryBuilder::appendCountryCode();

        if (isset($_SESSION['partner_id'])) {
            if ($_SESSION['partner_id'] != '') {
                $user = User::where(['email' => $username, 'anon' => '0'])->where('created_by', $_SESSION['partner_id'])->first();
                if ($user) {
                    if (!Hash::check($password, $user->password)) {
                        throw new ApiGenericException("The user credentials were incorrect.");
                    }
                    if ($user->status == '0') {
                        throw new ApiGenericException("You dont have permission to log in. Please contact your Admin for further details.");
                    }
                    $user = \Auth::loginUsingId($user->id);
                    return $user->id;
                }
            }
        }
        /* 
        // Vijay Added two column  created_by : anon to prevent cross partner login  
            PIMS - 13230 
         */
        if (isset($_SESSION['client_id']) && isset($_SESSION['client_secret'])) {
            $credentials = [
                'email' => $username,
                'password' => $password
            ];
        } else {
            $credentials = [
                'email' => $username,
                'password' => $password,
                'created_by' => $_SESSION['partner_id'],
                'anon'  => '0',
            ];
        }

        // Allow masquerading for our admins
        if (Auth::user() && (Auth::user()->isAdmin || Auth::user()->hasRole(Role::CUSTOMER_SERVICE) || Auth::user()->hasRole(Role::PARTNER) || Auth::user()->hasRole(Role::SUBPARTNER) || Auth::user()->hasRole(Role::REGIONAL_MANAGER) || Auth::user()->hasRole(Role::BUSINESSUSER) || Auth::user()->hasRole(Role::BUSINESSCLERK))) {
            $user = User::where('email', $username)->first();

            if ($user->status == '0') {
                throw new ApiGenericException("You dont have permission to log in. Please contact your Admin for further details.");
            }

            if ($user->user_type == '3' || $user->user_type == '4' || $user->user_type == '12' || $user->user_type == '10' || $user->user_type == '8') {
                return $user->id;
            }
            if ($user->roles && count($user->roles)) {
                throw new ApiGenericException("Cannot masquerade as a user that has back end access.");
            }

            return $user->id;
        }

        if (Auth::once($credentials)) {

            if (Auth::user()->status == '0') {
                throw new ApiGenericException("You dont have permission to log in. Please contact your Admin for further details.");
            }
            return Auth::user()->id;
        }

        return false;
    }

    public function verifySocialUser($username, $password)
    {
        // Allow masquerading for our admins
        if (Auth::user() && (Auth::user()->isAdmin || Auth::user()->hasRole(Role::CUSTOMER_SERVICE) || Auth::user()->hasRole(Role::PARTNER) || Auth::user()->hasRole(Role::SUBPARTNER))) {
            $user = User::where('email', $username)->first();

            if ($user->roles && count($user->roles)) {
                throw new ApiGenericException("Cannot masquerade as a user that has back end access.");
            }

            return $user->id;
        }

        //get user info by email 
        if (isset($username) && ($username != '')) {
            $userDetail = User::where('email', $username)->first();

            if ($userDetail) {
                //check if social id and user id match
                $userSocialDetail = UserSocialLogin::where('social_id', $password)->where('user_id', $userDetail->id)->first();
                if ($userSocialDetail) {
                    if (Auth::loginUsingId($userSocialDetail->user_id)) {
                        return Auth::user()->id;
                    }
                }
            }
        } else {
            //check if social id and user id match
            $userSocialDetail = UserSocialLogin::where('social_id', $password)->first();
            if ($userSocialDetail) {
                if (Auth::loginUsingId($userSocialDetail->user_id)) {
                    return Auth::user()->id;
                }
            }
        }

        return false;
    }

    public function verifyByUserId($username, $password)
    {
        // Allow masquerading for our admins
        if (Auth::user() && (Auth::user()->isAdmin || Auth::user()->hasRole(Role::CUSTOMER_SERVICE) || Auth::user()->hasRole(Role::PARTNER) || Auth::user()->hasRole(Role::SUBPARTNER) || Auth::user()->hasRole(Role::REGIONAL_MANAGER))) {
            $user = User::where('email', $username)->first();

            if ($user->roles && count($user->roles)) {
                throw new ApiGenericException("Cannot masquerade as a user that has back end access.");
            }

            return $user->id;
        }

        //get user info by id 
        if (isset($username) && ($username != '')) {
            //check if social id and user id match
            $userDetails = User::where('id', $username)->first();
            if ($userDetails) {
                if (Auth::loginUsingId($userDetails->id)) {
                    return Auth::user()->id;
                }
            }
        }

        return false;
    }
}
