<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\Reservation;
use App\Models\Facility;
use App\Models\RefundTransaction;
use App\Models\PermitRequest;
use Illuminate\Support\Facades\DB;
use App\Jobs\Reservation\ReservationPreAuthStatus;

/**
 * Utility class for interacting with the Ticketech APIs
 */
class DatacapPaymentGateway
{
    protected static $log;
    const QUEUE_NAME_PRE_AUTH_STATUS = 'reservation-pre-auth-status';

    public function __construct(LoggerFactory $logFactory)
    {
        self::$log = $logFactory->setPath('logs/roc/payment')->createLogger('autopay-heartland');
    }

    protected static function baseCall($vars, $headers, $url)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        curl_close($curl);
        $result = json_decode($response, TRUE);
        return $result;
    }

    // Datacap one time payment token
    public static function otuPaymentTokenDataCap($request, $facility)
    {
        $data['Account']            = $request->card_number;
        $data['ExpirationMonth']    = $request->expiration_month;
        $data["ExpirationYear"]     = $request->expiration_year;
        $data["CVV"]                = $request->security_code;

        $token = $facility->FacilityPaymentDetails->datacap_ecommerce_token;
        if ($token == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $token,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        $url = $facility->FacilityPaymentDetails->datacap_otu_url;
        return self::baseCall($vars, $headers, $url);
    }

    // zero Auth Payment
    public static function makeZeroAuthPaymentDataCap($request, $facility)
    {
        $data['Token']          = $request->token;
        $data['Amount']         = 0.00;
        $data["CardHolderID"]   = "Allow_V2";
        $mid                    = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        $url = $facility->FacilityPaymentDetails->datacap_authonly_url;
        return self::baseCall($vars, $headers, $url);
    }

    //  Pre Auth Transaction
    public static function makePreAuthPaymentDataCap($data, $facility)
    {
        $mid = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        $url = $facility->FacilityPaymentDetails->datacap_preauth_url;
        return self::baseCall($vars, $headers, $url);
    }

    public static function makePaymentDataCapCharge($data, $facility, $token_expire = false)
    {
        $mid = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
        $ref_id = $data['ref_id'];
        $vars = json_encode($data);

        if ($token_expire) {
            $url = $facility->FacilityPaymentDetails->datacap_script_url;
        } else {
            $url = $facility->FacilityPaymentDetails->datacap_preauth_url;
        }

        // dd($mid, $ref_id, $vars);
        // $this->log->info("Payment Request Data --" . $vars);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        // $this->log->info("Payment Request Header --" . json_encode($headers));
        // $this->log->info("Payment Request URL --" . $url . $ref_id);

        $curl = curl_init();
        if ($token_expire) {
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_POST, 1);
        } else {
            curl_setopt($curl, CURLOPT_URL, $url . $ref_id);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        }
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);

        curl_close($curl);
        // $this->log->info("Payment Data --" . json_encode($response));

        return $response;
    }

    // This function is used for permit renew cron
    public static function makePaymentDataCap($data, $ecommerce_mid, $url)
    {
        $vars = json_encode($data);
        $mid = $ecommerce_mid;
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        //return self::baseCall($vars,$headers,$url);
        $result = self::baseCall($vars, $headers, $url);
        return $result;
    }

    public static function makePaymentRefundDataCap($id, $ecommerce_mid, $url, $refund_type, $refund_amount = 0)
    {
        $amount = 0;
        if ($refund_type == 'permit') {
            $result = PermitRequest::with('transaction')->where("id", $id)->first();
            $data['Token']    = $result->payment_token;
            $data['card_last_four'] = $result->transaction->payment_last_four;
            $amount = $refund_amount;
        } else if ($refund_type == 'reservation') {
            $result = Reservation::with('transaction')->find($id);
            $amount = $result->total;
            if ($result->facility_id == config('parkengage.ROC_FACILITY')) {
                $amount = $refund_amount;
            }

            if ($result) {
                if (!$result->transaction && config('parkengage.ROC_FACILITY') == $result->facility_id) {
                    $result->transaction = DatacapTransaction::select(
                        '*',
                        DB::raw('card_last_four as payment_last_four')
                    )->where('reservation_id', $result->id)->first();
                }

                $data['Token'] = $result->payment_token ?? null;
                $data['card_last_four'] = $result->transaction->payment_last_four ?? null;
            }
        }

        // $card_last_four = $result->transaction->payment_last_four;
        $amount = ($result->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $amount;

        $data['Amount'] = $amount;
        // dd($data['card_last_four'], $data['Amount']);


        $vars = json_encode($data);
        $mid = $ecommerce_mid;
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        //dd($vars,$headers,$url);
        return self::baseCall($vars, $headers, $url);
    }

    public static function makeDatacapPaymentTransaction($request, $paymentResponse, $user_id)
    {
        // Check if a transaction with the given reservation_id already exists
        $datacapTransaction = null;
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $datacapTransaction = DatacapTransaction::where('reservation_id', $request->reservation_id)->first();
            // dd($datacapTransaction);
            $facility = Facility::with('FacilityPaymentDetails')->find($request->facility_id);

            // dd($datacapTransaction && isset($facility->FacilityPaymentDetails) && isset($facility->FacilityPaymentDetails->datacap_ecommerce_mid) && isset($facility->FacilityPaymentDetails->datacap_preauth_url));

            if ($datacapTransaction && isset($facility->FacilityPaymentDetails) && isset($facility->FacilityPaymentDetails->datacap_ecommerce_mid) && isset($facility->FacilityPaymentDetails->datacap_preauth_url)) {

                $mid = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                $url = $facility->FacilityPaymentDetails->datacap_preauth_url;

                // dd(isset($datacapTransaction->total) && isset($datacapTransaction->token));
                if (isset($datacapTransaction->total) && isset($datacapTransaction->token)) {
                    $data['Amount'] = $datacapTransaction->total;
                    $data['Token'] = $datacapTransaction->token;
                    $tran_id = $datacapTransaction->ref_id;

                    // dd($data, $mid, $tran_id, $url);
                    $paymentReleaseResponse = self::makeVoidPaymentChargeDataCap($data, $mid, $tran_id, $url);
                    $paymentReleaseResponseNew = json_decode($paymentReleaseResponse);

                    if($paymentReleaseResponseNew->Status == "Approved"){
                        $arrResponseData = [
                            'paymentReleaseResponse' => $paymentReleaseResponse,
                            'user_id' => $user_id,
                            'reservation_id' => $request->reservation_id,
                            'ticket_code' => $request->ticket_code,
                            'refund_for' => $datacapTransaction
                        ];
    
                        self::saveVoidTransactions($arrResponseData);
                    } else {    
                        \Log::info("Pre Auth Release response: ". json_encode($paymentReleaseResponseNew));
                        $arrResponseData = [
                            'paymentReleaseRequest' => $data,
                            'paymentReleaseResponse' => $paymentReleaseResponseNew,
                            'ticketech_code' => $request->ticket_code,
                            'refund_for' => (object)$datacapTransaction->toArray()
                        ];
                        
                        // Convert to JSON string
                        $jsonRequestData = json_encode($arrResponseData);
                        
                        // Trigger email
                        dispatch((new ReservationPreAuthStatus($jsonRequestData))->onQueue(self::QUEUE_NAME_PRE_AUTH_STATUS));
                        
                    }
                }
            }
        }

        // If no existing transaction is found, create a new one
        if (!$datacapTransaction) {
            $datacapTransaction = new DatacapTransaction();
        }

        //to resolve conflict of same key in reservation: Lokesh -15-Nov-2024
        $name = isset($request->card_name) && !empty($request->card_name) ?  $request->card_name : $request->name;

        // Set the transaction details
        $datacapTransaction->user_id = $user_id;
        $datacapTransaction->partner_id = $request->partner_id;
        $datacapTransaction->ip_address = \Request::ip();
        //    $datacapTransaction->total = $paymentResponse->Amount;
        $datacapTransaction->total = $request->Amount;
        $datacapTransaction->response_message = $paymentResponse->Message;
        $datacapTransaction->ref_id = $paymentResponse->RefNo;
        $datacapTransaction->invoice_no = $paymentResponse->InvoiceNo;
        $datacapTransaction->trans_id = $paymentResponse->AuthCode;
        $datacapTransaction->name = $name;
        $datacapTransaction->card_last_four = $request->card_last_four;
        $datacapTransaction->card_type = $paymentResponse->Brand;
        $datacapTransaction->card_name = isset($name) && !empty($name) ? $name : $paymentResponse->Brand;
        $datacapTransaction->expiry = $request->expiration ?? $request->expiration_month.$request->expiration_year;
        $datacapTransaction->session_id = $paymentResponse->CardHolderID;
        $datacapTransaction->token = $paymentResponse->Token;
        $datacapTransaction->result_reason = $paymentResponse->Message;
        $datacapTransaction->currency_used = "USD";

        // Set or update the reservation_id if provided
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $datacapTransaction->reservation_id = $request->reservation_id;
        }

        // Save the transaction
        $datacapTransaction->save();

        if (!$datacapTransaction) {
            throw new ApiGenericException("Record Not Added");
        }

        return $datacapTransaction;
    }

    // Save Payment Transaction
    public static function saveDatacapTransaction($request, $paymentResponse, $user_id, $permitRes)
    {
        $total = floatval($request->total);
        $brand = str_replace('/', '', $paymentResponse['Brand']);
        $authorized_anet_transaction = new AuthorizeNetTransaction();

        $authorized_anet_transaction->sent = '1';
        $authorized_anet_transaction->user_id = $user_id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = $total;
        $authorized_anet_transaction->description = "Datacap Payment Done User : " .  $user_id;
        $authorized_anet_transaction->card_type = $brand;
        $authorized_anet_transaction->ref_id = $paymentResponse["RefNo"];
        $authorized_anet_transaction->method = "card";
        $authorized_anet_transaction->payment_last_four = isset($request->card_last_four) ? $request->card_last_four : '0';
        $authorized_anet_transaction->auth_code = $paymentResponse["AuthCode"];
        $authorized_anet_transaction->response_code = $paymentResponse["ReturnCode"];
        $authorized_anet_transaction->response_message = "Processed";
        $authorized_anet_transaction->invoice_number = $paymentResponse["InvoiceNo"];
        $authorized_anet_transaction->anet_trans_id = $paymentResponse["InvoiceNo"];
        $authorized_anet_transaction->status_message = $paymentResponse["Status"];
        $authorized_anet_transaction->expiration = isset($request->expiration) ? $request->expiration : '0';
        $authorized_anet_transaction->save();
        if ($permitRes) {
            $permitRes->ex_month = $request->expiration_month;
            $permitRes->ex_year =  $request->expiration_year;
            $permitRes->anet_transaction_id = $authorized_anet_transaction->id;
            $permitRes->payment_token = $paymentResponse['Token'];
            $permitRes->save();
        }
        return $authorized_anet_transaction;
    }

    public static function saveDatacapCard($paymentResponse, $user_id, $request)
    {
        $logFactory = new LoggerFactory();
        $addcard = $logFactory->setPath('logs/AddCard')->createLogger('AddCard');

        $addcard->info("Card Request Data --" . json_encode($request));
        $addcard->info("PG Response  --" . json_encode($paymentResponse));
        if (is_object($paymentResponse)) {
            $paymentResponse = (array)$paymentResponse;
        }
        $brand              = str_replace('/', '', $paymentResponse['Brand']);
        $card_last_four     = substr($paymentResponse['Account'], -4);
        $cardCheck          = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $request->partner_id)->where('card_holder_id', $paymentResponse['CardHolderID'])->where('partner_payment_gateway_id', $request->partner_payment_gateway_id)->first();

        $addcard->info("check Card  --" . json_encode($cardCheck));
        if ($cardCheck) {
            return $cardCheck;
        }
        $status = 0;

        $user_card = DatacapPaymentProfile::whereNull('deleted_at')->where(['user_id' => $user_id])->first();

        $dataProfile['user_id']                         = $user_id;
        $dataProfile['partner_id']                      = $request->partner_id;
        $dataProfile['facility_payment_type_id']        = $request->facility_payment_type_id;
        $dataProfile['partner_payment_gateway_id']      = $request->partner_payment_gateway_id;
        $dataProfile['name']                            = isset($request->name_on_card) ? $request->name_on_card : '';
        $dataProfile['card_last_four']                  = $card_last_four;
        $dataProfile['card_type']                       = $brand;
        $dataProfile['card_name']                       = isset($request->name_on_card) ? $request->name_on_card : $brand;
        $dataProfile['expiry']                          = $request->expiration_date;
        $dataProfile['token']                           = $paymentResponse['Token'];
        $dataProfile['card_holder_id']                  = $paymentResponse['CardHolderID'];
        $dataProfile['result_reason']                   = $paymentResponse['Message'];
        $dataProfile['currency_used']                   = "USD";
        $dataProfile['RefNo']                           = $paymentResponse['RefNo'];
        $dataProfile['InvoiceNo']                       = $paymentResponse['InvoiceNo'];
        $dataProfile['AuthCode']                        = $paymentResponse['AuthCode'];
        if (isset($request->onBehalf) && !empty($request->onBehalf)) {
            $dataProfile['on_behalf_status']            = isset($request->creater_id) ? $request->creater_id : 1;
        }

        if (!$user_card) {
            $dataProfile['is_default'] = 1;
        } else {
            if (is_numeric($request->is_default)) {
                if (is_numeric($request->is_default)) {
                    $status = $request->is_default;
                }

                if (!empty($status)) {
                    DatacapPaymentProfile::where(['user_id' => $user_id])->update(array('is_default' => 0));
                }

                $dataProfile['is_default'] = $status;
            }
        }
        $result = DatacapPaymentProfile::create($dataProfile);
        if ($result && isset($request->is_default) && $request->is_default == 1) {
            DatacapPaymentProfile::where(['user_id' => $user_id, 'card_last_four' => $result->card_last_four, 'expiry' => $result->expiry])->update(array('is_default' => 1));
        }
        $addcard->info("Card Done  --" . json_encode($result));
        return $result;
    }

    public static function saveDatacapCardVpDevice($payment_details, $user_id, $partner_id)
    {
        $card_last_four = substr($payment_details['MaskedPAN'], -4);
        $card_holder_name = stripslashes($payment_details['CardHolderName']);
        $card_holder_name = str_replace('/', ' ', $card_holder_name);
        $cardHolderID = isset($payment_details['CardHolderID']) ? $payment_details['CardHolderID'] : '';
        if (!$cardHolderID) {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('card_holder_id', $cardHolderID)->first();

        if ($cardCheck) {
            return $cardCheck;
        }
        $status = 0;

        $user_card = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->first();
        $dataProfile['user_id'] = $user_id;
        $dataProfile['partner_id'] = $partner_id;
        $dataProfile['name'] = $card_holder_name;
        $dataProfile['card_last_four'] = $card_last_four;
        $dataProfile['card_type'] = $payment_details['CardType'];
        $dataProfile['card_name'] = isset($card_holder_name) && !empty($card_holder_name) ? $card_holder_name : $payment_details['CardType'];
        $dataProfile['expiry'] = $payment_details['expiry'];
        $dataProfile['token'] = $payment_details["token"];
        $dataProfile['card_holder_id'] = $payment_details['CardHolderID'];
        $dataProfile['result_reason'] = $payment_details['StatusMessage'];
        $dataProfile['currency_used'] = "USD";
        //$dataProfile['RefNo'] = $paymentResponse['RefNo'];
        //$dataProfile['InvoiceNo'] = $paymentResponse['InvoiceNo'];
        //$dataProfile['AuthCode'] = $paymentResponse['AuthCode'];
        if (!$user_card) {
            $data['is_default'] = 1;
        } else {
            if (is_numeric($request->is_default)) {

                if (is_numeric($request->is_default)) {
                    $status = $request->is_default;
                }

                if (!empty($status)) {

                    DatacapPaymentProfile::where(['user_id' => $user_id])->update(array('is_default' => 0));
                }

                $data['is_default'] = $status;
            }
        }
        $result = DatacapPaymentProfile::create($dataProfile);

        return $result;
    }

    public static function datacapPaymentTicketRefund($grandTotal, $checkinData, $ecommerce_mid, $url)
    {
        $amount = $grandTotal;

        $data['Amount'] = $amount;
        $data['Token']    = $checkinData->payment_token;

        $vars = json_encode($data);
        $mid = $ecommerce_mid;
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        //dd($vars,$headers,$url);
        return self::baseCall($vars, $headers, $url);
    }

    // This function is used to make payment charge of Transient Ticket PreAuth Capture Flow
    public static function makeTicketPaymentChargeDataCap($data, $mid, $tran_id, $url, $device_type)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/datacap')->createLogger('datacap-api-call');
        $vars = json_encode($data);
        $log->info("Payment Request Data --" . $vars);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        $log->info("Payment Request Header --" . json_encode($headers));

        $curl = curl_init();

        if ($device_type == 'IM30') {
            //	$log->info("Payment Request URL --" . $url);
            //	curl_setopt($curl, CURLOPT_URL, $url);
            //	curl_setopt($curl, CURLOPT_POST, 1);

            $log->info("Payment Request URL --" . $url . $tran_id);
            curl_setopt($curl, CURLOPT_URL, $url . $tran_id);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        } else {
            $log->info("Payment Request URL --" . $url . $tran_id);
            curl_setopt($curl, CURLOPT_URL, $url . $tran_id);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        }

        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($curl);
        curl_close($curl);
        $log->info("Payment Data --" . json_encode($response));
        return $response;
    }

    // This function is used to make void payment charge of Transient Ticket PreAuth Capture Flow to release the hold payment
    public static function makeVoidPaymentChargeDataCap($data, $mid, $tran_id, $url)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/datacap')->createLogger('datacap-api-call');
        $vars = json_encode($data);
        $log->info("Void Payment Request Data --" . $vars);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        $log->info("Void Payment Request Header --" . json_encode($headers));

        $curl = curl_init();
        $log->info("Void Payment Request URL --" . $url . $tran_id . "/void");
        curl_setopt($curl, CURLOPT_URL, $url . $tran_id . "/void");
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($curl);
        curl_close($curl);
        $log->info("Void Payment Response Data --" . json_encode($response));
        return $response;
    }

    public static function saveVoidTransactions($arrResponseData = [])
    {
        if (count($arrResponseData) > 0) {
            $paymentReleaseResponse = $arrResponseData['paymentReleaseResponse'];
            $user_id = $arrResponseData['user_id'];
            $ticket_code = $arrResponseData['ticket_code'];
            $refund_for = $arrResponseData['refund_for'];

            $logFactory = new LoggerFactory();
            $log = $logFactory->setPath('logs/roc/preauth/release')->createLogger('payment-void');

            $paymentReleaseData = json_decode($paymentReleaseResponse);
            $total   = floatval($paymentReleaseData->Amount);
            $brand = str_replace('/', '', $paymentReleaseData->Brand);
            $card_last_four = substr($paymentReleaseData->Account, -4);

            $refundTransaction = new  RefundTransaction();
            if ($paymentReleaseData->Status == 'Approved') {
                $refundTransaction->sent = '1';
                $refundTransaction->user_id = $user_id;
                $refundTransaction->ip_address = \Request::ip();
                $refundTransaction->total = $total;

                $refundTransaction->description = "Datacap Void Sale Done User : " .  $user_id;
                $refundTransaction->refund_for = json_encode($refund_for);
                $refundTransaction->card_type = $brand;
                $refundTransaction->ref_id = $paymentReleaseData->RefNo;
                $refundTransaction->method = "card";
                $refundTransaction->payment_last_four = isset($card_last_four) ? $card_last_four : '0';
                $refundTransaction->auth_code = $paymentReleaseData->AuthCode;
                $refundTransaction->response_code = $paymentReleaseData->ReturnCode;
                $refundTransaction->response_message = $paymentReleaseData->TranCode;
                $refundTransaction->invoice_number = $paymentReleaseData->InvoiceNo;
                $refundTransaction->anet_trans_id = $paymentReleaseData->InvoiceNo;
                $refundTransaction->status_message = $paymentReleaseData->Status;
                $refundTransaction->reference_key = $ticket_code;
                $refundTransaction->created_at = date("Y-m-d H:i:s");
                $refundTransaction->save();
                $log->info("PreAuth Payment Release Response Save -- " . json_encode($refundTransaction));
                return $refundTransaction;
            } else {
                $log->info("PreAuth Payment Release Response Failed");
                return true;
            }
        }
    }
}
