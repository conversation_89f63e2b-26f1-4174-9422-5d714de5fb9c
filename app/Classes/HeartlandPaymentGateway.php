<?php

namespace App\Classes;

use Exception;
use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use App\Models\AuthorizeNetTransaction;
use App\Models\Facility;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\Reservation;
use GlobalPayments\Api\ServiceConfigs\Gateways\PorticoConfig;
use GlobalPayments\Api\ServicesContainer;
use GlobalPayments\Api\Entities\Address;
use GlobalPayments\Api\Entities\Customer;
use GlobalPayments\Api\PaymentMethods\CreditCardData;
use GlobalPayments\Api\PaymentMethods\RecurringPaymentMethod;
use GlobalPayments\Api\Entities\Transaction;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\RefundTransaction;
use GlobalPayments\Api\Entities\Enums\MobilePaymentMethodType;
use GlobalPayments\Api\Entities\Enums\PaymentDataSourceType;
use GlobalPayments\Api\Entities\Exceptions\ApiException;
use GlobalPayments\Api\Entities\Exceptions\GatewayException;
use GlobalPayments\Api\Services\ReportingService;

/**
 * Utility class for interacting with the Ticketech APIs
 */
class HeartlandPaymentGateway
{

    protected $log;
    public function __construct()
    {
        $logFactory         =  new  LoggerFactory();
        $this->log = $logFactory->setPath('logs/heartland')->createLogger('heartland-payment-gateway');
    }

    protected static function baseCall($vars, $headers, $url)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
        //$log->info("Request Data 1: " . $vars);
        $log->info("Request Header: " . json_encode($headers));
        $log->info("Request Url: " . json_encode($url));

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        $log->info("Heartland Response Url: " . $response);
        curl_close($curl);
        $result = json_decode($response, TRUE);
        return $result;
    }

    public static function heartlandConfig($facility)
    {
        if(isset($facility->FacilityPaymentDetails->heartland_secret_api_key) && empty($facility->FacilityPaymentDetails->heartland_secret_api_key)){
            throw new ApiGenericException('Payment Gateway MID Setup Not Done, Please contact to Admin');
        }
        if(isset($facility->FacilityPaymentDetails->heartland_developer_id) && empty($facility->FacilityPaymentDetails->heartland_developer_id)){
            throw new ApiGenericException('Payment Gateway MID Setup Not Done, Please contact to Admin');
        }
        if(isset($facility->FacilityPaymentDetails->heartland_version_number) && empty($facility->FacilityPaymentDetails->heartland_version_number)){
            throw new ApiGenericException('Payment Gateway MID Setup Not Done, Please contact to Admin');
        }
        if(isset($facility->FacilityPaymentDetails->heartland_service_url) && empty($facility->FacilityPaymentDetails->heartland_service_url)){
            throw new ApiGenericException('Payment Gateway MID Setup Not Done, Please contact to Admin');
        }

        $config = new PorticoConfig();
        $config->secretApiKey = isset($facility->FacilityPaymentDetails->heartland_secret_api_key) ? $facility->FacilityPaymentDetails->heartland_secret_api_key : NULL;
        $config->developerId = isset($facility->FacilityPaymentDetails->heartland_developer_id) ? $facility->FacilityPaymentDetails->heartland_developer_id : NULL;
        $config->versionNumber = isset($facility->FacilityPaymentDetails->heartland_version_number) ? $facility->FacilityPaymentDetails->heartland_version_number : NULL;
        $config->serviceUrl = isset($facility->FacilityPaymentDetails->heartland_service_url) ? $facility->FacilityPaymentDetails->heartland_service_url : NULL;
        ServicesContainer::configureService($config);
    }

    public static function otuPaymentTokenHeartland($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);

        $card_month = substr($request->expiration_date, 0, 2);
        $card_year = substr($request->expiration_date, -2);

        $data['object'] = "token";
        $data['token_type'] = "supt";
        $data["card"]["number"] = $request->card_number;
        $data["card"]["cvc"] = $request->security_code;
        $data["card"]["exp_month"] = $card_month;
        $data["card"]["exp_year"] = "20" . $card_year;

        $url = $facility->FacilityPaymentDetails->heartland_service_url . "/" . $facility->FacilityPaymentDetails->heartland_single_token_slug . "?api_key=" . $facility->FacilityPaymentDetails->heartland_public_api_key;

        $token = $facility->FacilityPaymentDetails->heartland_public_api_key;
        if ($token == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);

        $headers = [
            'Authorization: ' . $token,
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.9',
        ];
        // dd($vars, $headers, $url);
        return self::baseCall($vars, $headers, $url);
    }

    public static function heartlandPaymentByToken($request, $facility)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');

        // call heartland config
        self::heartlandConfig($facility);

        $card = new CreditCardData();
        $card->token = $request->token;

        $address = new Address();
        $address->postalCode = isset($request->zip_code) ? $request->zip_code : NULL;

        $response = $card->charge($request->Amount)
            ->withCurrency("USD")
            ->withAddress($address);
        if ($request->user_consent == 1) {
            $response = $response->withRequestMultiUseToken(true);
        }
        $response = $response->execute();

        $request->request->remove('nonce');
        // $request->request->remove('card_number');
        $request->request->remove('security_code');
        $request->request->remove('password');
        $request->request->remove('confirm_password');
        // $log->info("Request Data 4: " . json_encode($request->all()));
        $log->info("Response Heartland: " . json_encode($response));
        return $response;
    }

    public static function verifyHeartlandCard($request, $facility)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
        // call heartland config
        self::heartlandConfig($facility);

        $card = new CreditCardData();
        $card->token = $request->token;

        $address = new Address();
        $address->postalCode = $request->zip_code;

        $response = $card->verify()
            ->withCurrency("USD")
            ->withAddress($address)
            ->withRequestMultiUseToken(true)
            ->execute();
        $log->info("Response verify Card: " . json_encode($response));
        return $response;
    }

    public static function saveHeartlandCard($cardResponse, $user_id, $request)
    {
        $card_last_four = "";
        if (isset($request->card_last_four) && !empty($request->card_last_four)) {
            $card_last_four = $request->card_last_four;
        } else {
            $card_last_four = substr($request->card_number, -4);
        }
        $cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $request->partner_id)->where('card_last_four', $card_last_four)->where('partner_payment_gateway_id', $request->partner_payment_gateway_id)->first();

        if ($cardCheck) {
            return $cardCheck;
        }
        $status = 0;

        $user_card = HeartlandPaymentProfile::whereNull('deleted_at')->where(['user_id' => $user_id])->first();

        $data['user_id']                        = $user_id;
        $data['partner_id']                     = $request->partner_id;
        $data['facility_payment_type_id']       = $request->facility_payment_type_id;
        $data['partner_payment_gateway_id']     = $request->partner_payment_gateway_id;
        $data['name']                           = isset($request->name_on_card) ? $request->name_on_card : '';
        $data['card_last_four']                 = $card_last_four;
        $data['card_type']                      = $cardResponse->cardType;
        $data['card_name']                      = $cardResponse->cardType;
        $data['expiry']                         = $request->expiration_date;
        $data['card_holder_id']                 = isset($cardResponse->token) ? $cardResponse->token : '';
        $data['token']                          = isset($cardResponse->token) ? $cardResponse->token : '';
        $data['result_reason']                  = $cardResponse->responseMessage;
        $data['zipcode']                        = isset($request->zip_code) ? $request->zip_code : NULL;
        $data['currency_used']                  = "USD";
        $data['AuthCode']                       = isset($cardResponse->transactionReference->authCode) ? $cardResponse->transactionReference->authCode : NULL;
        $data['card_brand_transaction_id']      = isset($cardResponse->cardBrandTransactionId) ? $cardResponse->cardBrandTransactionId : NULL;

        if (!$user_card) {
            $data['is_default'] = 1;
        } else {
            if (is_numeric($request->is_default)) {

                if (is_numeric($request->is_default)) {
                    $status = $request->is_default;
                }

                if (!empty($status)) {
                    HeartlandPaymentProfile::where(['user_id' => $user_id])->update(array('is_default' => 0));
                }

                $data['is_default'] = $status;
            }
        }
        $result = HeartlandPaymentProfile::create($data);
        if ($result && isset($request->is_default) && $request->is_default == 1) {
            HeartlandPaymentProfile::where(['user_id' => $user_id, 'card_last_four' => $result->card_last_four, 'expiry' => $result->expiry])->update(array('is_default' => 1));
        }

        return $result;
    }

    public static function saveTransaction($request, $cardResponse, $user_id)
    {
        $anet_trans_id = 0;
        if (isset($request->pre_auth_transctions)) {
            $anet_trans_id = $request->pre_auth_transctions;
        } else if (isset($cardResponse->transactionReference->transactionId)) {
            $anet_trans_id = $cardResponse->transactionReference->transactionId;
        }

        $card_type = $cardResponse->cardType;
        if (isset($request->card_type)) {
            $card_type = $request->card_type;
        }

        $name = "";
        if (isset($request->name)) {
            $name = $request->name;
        } elseif (isset($request->name_on_card)) {
            $name = $request->name_on_card;
        }

        $total = $request->total;
        $authorized_anet_transaction                    = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent              = '1';
        $authorized_anet_transaction->user_id           = $user_id;
        $authorized_anet_transaction->ip_address        = \Request::ip();
        $authorized_anet_transaction->total             = isset($request->original_total) ? $request->original_total : $total; //dont' upload prod
        $authorized_anet_transaction->description       = "Heartland payment: " . $user_id;
        $authorized_anet_transaction->card_type         = $card_type;
        $authorized_anet_transaction->ref_id            = $anet_trans_id;
        $authorized_anet_transaction->anet_trans_id     = $anet_trans_id;
        $authorized_anet_transaction->method            = isset($request->paymentMethods) ? $request->paymentMethods :  "card";
        $authorized_anet_transaction->payment_last_four = isset($request->card_last_four) ? $request->card_last_four : '0';
        $authorized_anet_transaction->expiration        = isset($request->expiration_date) ? $request->expiration_date : NULL;
        $authorized_anet_transaction->response_message  = "Processed";
        $authorized_anet_transaction->response_code     = isset($cardResponse->responseCode) ? $cardResponse->responseCode : NULL;
        $authorized_anet_transaction->auth_code         = isset($cardResponse->transactionReference->authCode) ? $cardResponse->transactionReference->authCode : NULL;
        $authorized_anet_transaction->card_brand_transaction_id = isset($cardResponse->cardBrandTransactionId) ? $cardResponse->cardBrandTransactionId : NULL;
        $authorized_anet_transaction->maskedCardNumber  = isset($request->maskedCardNumber) ? $request->maskedCardNumber : NULL;
        //  $authorized_anet_transaction->name = $name;
        $authorized_anet_transaction->save();
        return $authorized_anet_transaction;
    }


    public static function heartlandPaymentRefund($reservation_id, $facility, $refundAmount = 0)
    {
        // call heartland config
        self::heartlandConfig($facility);
        $paymentEnv = isset($facility->FacilityPaymentDetails->heartland_payment_env) ? $facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');

        $reservation = Reservation::with('transaction', 'facility.FacilityPaymentDetails')->where("id", $reservation_id)->whereNotNull('anet_transaction_id')->first();
        if (isset($reservation) && !empty($reservation)) {
            $refund_amount = $reservation->total;
            if ($reservation->facility->facilityConfiguration->pre_post_charge_reservation)
                $refund_amount = $reservation->charged_amount;

            $tran_id = $reservation->transaction->anet_trans_id;
            //$amount = $reservation->total;            
            $amount = ($paymentEnv == 'test') ? '3.00' : $refund_amount;
            if ($refundAmount > 0) {
                $amount = $refundAmount;
            }

            $response = Transaction::fromId($tran_id)
                ->refund($amount)
                ->withCurrency("USD")
                ->execute();
            return $response;
        } else {
            return false;
        }
    }

    public static function heartlandTicketPaymentRefund($grandTotal, $checkinData)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
        $arr['amount'] = $grandTotal;
        $arr['tran_id'] = $checkinData->transaction->anet_trans_id;
        $log->info("Refund Request Amount: " . json_encode($grandTotal));
        $log->info("Refund Request Transaction ID: " . json_encode($checkinData->transaction->anet_trans_id));
        $log->info("Refund Request : " . json_encode($arr));
        $log->info("Refund Request Ticket Data: " . json_encode($checkinData));
        // call heartland config
        self::heartlandConfig($checkinData->facility);

        if (isset($checkinData) && !empty($checkinData)) {
            $tran_id = $checkinData->transaction->anet_trans_id;
            $amount  = $grandTotal;

            $response = Transaction::fromId($tran_id)
                ->refund($amount)
                ->withCurrency("USD")
                ->execute();
            $log->info("Refund Response Data: " . json_encode($response));
            return $response;
        } else {
            return false;
        }
    }

    // save card VP Device
    public static function makePreAuthPaymentHeartland($request, $facility)
    {
        try {
            $logFactory = new LoggerFactory();
            $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
            // call heartland config
            self::heartlandConfig($facility);

            $paymentEnv = isset($facility->FacilityPaymentDetails->heartland_payment_env) ? $facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');
            //$amount = $reservation->total;            
            $amount = ($paymentEnv == 'test') ? '3.00' : $request->Amount;

            $card = new CreditCardData();
            $card->token = $request->token;

            $address = new Address();
            $address->postalCode = isset($request->zip_code) ? $request->zip_code : NULL;

            $response = $card->authorize($amount)
                ->withCurrency("USD")
                ->withAddress($address);
            if ($request->user_consent == '1') {
                $response = $response->withRequestMultiUseToken(true);
            }
            $response = $response->execute();

            return $response;
            //code...
        } catch (ApiException $e) {
            // Catch Global Payments API exception : 25-03-2025 : Vijay
            // Unexpected Gateway Response: 1 - System error Means Payment Gateway Down                            
            // Extract the response details
            $responseCode = $e->responseCode ?? 'N/A';  // Default to 'N/A' if not set
            $responseMessage = $e->responseMessage ?? 'Unknown error';
            if ($responseCode == '1') {
                $log->info("Payment failed. Due to HL Payment Gateway Down --" . json_encode($e->getMessage()));
                throw new ApiGenericException('Payment failed, Due to HL Payment Gateway Down');
            }
            throw new ApiGenericException('Payment failed. Please correct your card details or try again with a different card..');
        } catch (\Throwable $th) {
            throw new ApiGenericException('Payment failed. Please correct your card details or try again with a different card..');
        }
    }

    public static function chargePreauthCardPayment($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);

        try {
            $response = Transaction::fromId($request->transactionId)
                ->capture($request->Amount)
                ->execute();
            return $response;
        } catch (GatewayException $e) {
            // Check for the specific error code or message
            if (strpos($e->getMessage(), 'Transaction rejected because the referenced original transaction is invalid') !== false) {
                // Handle the specific case where the transaction is already part of a batch
                return $e->getMessage();
            } else {
                // Handle any other GatewayException
                return response()->json([
                    'error' => 'An error occurred while processing the transaction.',
                    'details' => $e->getMessage(),
                ], 500);
            }
        } catch (\Exception $e) {
            // Handle any other exceptions that may occur
            return 'An unexpected error occurred.';
        }
    }

    // public static function makeHeartlandPaymentTransaction($request,$paymentResponse, $user_id)
    // {
    //     $params = [];
    //     $total = $request->total;

    //     $heartlandTransaction = new DatacapTransaction();
    //     $heartlandTransaction->user_id = $user_id;
    //     $heartlandTransaction->partner_id = $request->partner_id;
    //     $heartlandTransaction->ip_address = \Request::ip();
    //     $heartlandTransaction->total = $request->total;
    //     $heartlandTransaction->response_message = $paymentResponse->responseMessage;
    //     $heartlandTransaction->ref_id = $paymentResponse->referenceNumber;
    //     $heartlandTransaction->trans_id = $paymentResponse->transactionReference->transactionId;
    //     $heartlandTransaction->name = $request->name;
    //     $heartlandTransaction->card_last_four = $request->card_last_four;
    //     $heartlandTransaction->card_type = $paymentResponse->cardType;
    //     $heartlandTransaction->card_name = $paymentResponse->cardType;
    //     $heartlandTransaction->expiry = $request->expiration;
    //     $heartlandTransaction->token = isset($paymentResponse->token) ? $paymentResponse->token:'';
    //     $heartlandTransaction->result_reason = $paymentResponse->responseMessage;
    //     $heartlandTransaction->currency_used = "USD";
    //     $heartlandTransaction->AuthCode = isset($paymentResponse->transactionReference->authCode) ? $paymentResponse->transactionReference->authCode:'';
    //     $heartlandTransaction->card_brand_transaction_id = isset($paymentResponse->cardBrandTransactionId) ? $paymentResponse->cardBrandTransactionId:'';
    //     if(isset($request->reservation_id) and !empty($request->reservation_id)){
    //         $heartlandTransaction->reservation_id = $request->reservation_id;
    //     }
    //     $heartlandTransaction->save();
    //     if (!$heartlandTransaction) {
    //         throw new ApiGenericException("Record Not Added");
    //     }
    //     return $heartlandTransaction;

    // }

    public static function makeHeartlandPaymentTransaction($request, $paymentResponse, $user_id)
    {
        // Check if a transaction with the given reservation_id already exists
        $heartlandTransaction = null;
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $heartlandTransaction = DatacapTransaction::where('reservation_id', $request->reservation_id)->first();
        }

        // If no existing transaction is found, create a new one
        if (!$heartlandTransaction) {
            $heartlandTransaction = new DatacapTransaction();
        }

        //PIMS-12396 | dev: sagar | added for handle total amount with promocode
        $totalAmount = $request->total;
        if (!empty($request->discount_amount) && $request->discount_amount > 0) {
            $discountAmount = $totalAmount - $request->discount_amount;
            $totalAmount = ($totalAmount != $discountAmount) ? $discountAmount : $totalAmount;
        }

        //to resolve conflict of same key in reservation: Lokesh -11-Oct-2024
        $name = isset($request->card_name) && !empty($request->card_name) ?  $request->card_name : $request->name;

        // Set the transaction details
        $heartlandTransaction->user_id                      = $user_id;
        $heartlandTransaction->partner_id                   = $request->partner_id;
        $heartlandTransaction->ip_address                   = \Request::ip();
        $heartlandTransaction->total                        = $totalAmount;
        $heartlandTransaction->response_message             = $paymentResponse->responseMessage;
        $heartlandTransaction->ref_id                       = $paymentResponse->referenceNumber;
        $heartlandTransaction->trans_id                     = $paymentResponse->transactionReference->transactionId;
        $heartlandTransaction->name                         = $name;
        $heartlandTransaction->card_last_four               = isset($request->card_last_four) ? $request->card_last_four : '';
        $heartlandTransaction->card_type                    = $paymentResponse->cardType;
        $heartlandTransaction->card_name                    = $paymentResponse->cardType;
        $heartlandTransaction->expiry                       = isset($request->expiration) ? $request->expiration : '';
        $heartlandTransaction->token                        = isset($request->PreToken) ? $request->PreToken : (isset($paymentResponse->token) ? $paymentResponse->token : NULL);
        $heartlandTransaction->result_reason                = $paymentResponse->responseMessage;
        $heartlandTransaction->currency_used                = "USD";
        $heartlandTransaction->AuthCode                     = isset($paymentResponse->transactionReference->authCode) ? $paymentResponse->transactionReference->authCode : '';
        $heartlandTransaction->card_brand_transaction_id    = isset($paymentResponse->cardBrandTransactionId) ? $paymentResponse->cardBrandTransactionId : '';

        // Set or update the reservation_id if provided
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $heartlandTransaction->reservation_id           = $request->reservation_id;
        }

        // Save the transaction
        $heartlandTransaction->save();

        if (!$heartlandTransaction) {
            throw new ApiGenericException("Record Not Added");
        }

        return $heartlandTransaction;
    }


    public static function heartlandPaymentByCrediCardEvent($facility, $request)
    {
        $params = [];
        $params['validationID'] = $facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $request->total;
        $params['cardNumber'] = $request->card_number;
        $params['expirationDate'] = str_replace('/', '', $request->expiration_date);
        $params['securityCode'] = $request->security_code;
        $params['type'] = "EftAuthorization";
        $params['OptionFlags'] = "G";
    }

    public static function generateHeartlandMultiUseToken($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);

        $card_month = substr($request->expiration_date, 0, 2);
        $card_year = substr($request->expiration_date, -2);

        $card = new CreditCardData();
        $card->number = $request->card_number;
        $card->expMonth = $card_month;
        $card->expYear = '20' . $card_year;
        $card->cvn = $request->security_code;
        //dd($card);
        $address = new Address();
        $address->postalCode = $request->zipcode;

        $response = $card->verify()
            ->withCurrency("USD")
            ->withAddress($address)
            ->withRequestMultiUseToken(true)
            ->execute();

        return $response;
    }

    public static function heartlandPaymentByTokenIM30($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);
        // dd($request->Amount,$request->token);
        $card = new CreditCardData();
        $card->token = $request->token;
        $response = $card->charge($request->Amount)
            ->withCurrency("USD");
        $response = $response->execute();
        return $response;
    }

    public static function heartlandCustomPaymentRefund($amount, $tran_id, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);
        $response = Transaction::fromId($tran_id)
            ->refund($amount)
            ->withCurrency("USD")
            ->execute();
        return $response;
    }

    public static function heartlandApplePayPaymentByToken($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);

        $card = new CreditCardData();
        $card->mobileType = MobilePaymentMethodType::APPLEPAY;
        $card->paymentSource = PaymentDataSourceType::APPLEPAYWEB;
        $card->token = $request->token;

        $response = $card->charge($request->amount)
            ->withCurrency('USD')
            ->withInvoiceNumber($request->invoice_number)
            ->withAllowDuplicates(true)
            ->execute();
        return $response;
    }

    public static function heartlandGooglePayPaymentByToken($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);

        $card = new CreditCardData();
        $card->token = $request->token;
        $card->mobileType = MobilePaymentMethodType::GOOGLEPAY;
        $card->paymentSource = PaymentDataSourceType::GOOGLEPAYWEB;

        $response = $card->charge($request->amount)
            ->withCurrency('USD')
            ->withAllowDuplicates(true)
            ->execute();
        return $response;
    }

    public static function heartlandApplePaySession($request, $facilityPaymentDetail)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-apple-pay-call');

        $log->info("Request Data: " . json_encode($request->all()));
        // call heartland config
        // self::heartlandConfig($facility);
        $data = [];
        $data['merchantIdentifier'] = $request->merchantIdentifier;
        $data["displayName"]        = $request->displayName;
        $data["initiative"]         = $request->initiative;
        $data["initiativeContext"]  = $request->initiativeContext;

        $certificatePath = config('parkengage.APPLE_PAY_FILE_PATH') . $request->slug . '/' . $facilityPaymentDetail->hl_apple_pay_cert;
        $keyPath = config('parkengage.APPLE_PAY_FILE_PATH') . $request->slug . '/' . $facilityPaymentDetail->hl_apple_pay_key;
        // $keyPath = config('parkengage.APPLE_PAY_FILE_PATH') . "merchant_identity_key.pem";
        // $certificatePath = config('parkengage.APPLE_PAY_FILE_PATH'). "merchant_identity_cert.pem";
        // $keyPath = config('parkengage.APPLE_PAY_FILE_PATH') . "merchant_identity_key.pem";
        if (!file_exists($certificatePath) || !file_exists($keyPath)) {
            throw new ApiGenericException('Payment Failed!, Something went wrong.');
        }


        $vars = json_encode($data);

        $headers = [
            'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        $url = $request->validationURL;
        $log->info("url Data: " . $url);
        $log->info("Header Data: " . json_encode($headers));

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSLCERT, $certificatePath);
        curl_setopt($curl, CURLOPT_SSLKEY, $keyPath);
        curl_setopt($curl, CURLOPT_KEYPASSWD, config('parkengage.APPLE_PAY_PASS'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);

        $log->info("Response Data: " . json_encode($response));
        curl_close($curl);

        return response()->json($response);


        // $log->info("HL curl response " . json_encode($response));
        return $response;
    }

    public static function saveWalletPayTransaction($request)
    {
        $walletPay = json_decode($request->walletPay, true);
        $authorized_anet_transaction = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent = '1';
        $authorized_anet_transaction->user_id = $request->user_id;
        $authorized_anet_transaction->total = $walletPay['Amount'];
        $authorized_anet_transaction->description = "Permit Wallet Pay : " . $request->paymentMethods;
        $authorized_anet_transaction->response_message = $walletPay['Message'];
        $authorized_anet_transaction->status_message = $walletPay['Status'];
        $authorized_anet_transaction->auth_code = $walletPay['AuthCode'];
        $authorized_anet_transaction->card_type = $walletPay['Brand'];
        $authorized_anet_transaction->ref_id = $walletPay['RefNo'];
        $authorized_anet_transaction->invoice_number = $walletPay['InvoiceNo'];
        $authorized_anet_transaction->anet_trans_id = $walletPay['RefNo'];
        $authorized_anet_transaction->payment_last_four = substr($walletPay['Account'], -4);
        $authorized_anet_transaction->method = $request->paymentMethods;
        $authorized_anet_transaction->reader_used = $walletPay['ResponseOrigin'];
        $authorized_anet_transaction->anet_trans_hash = $walletPay['RefNo'];
        $authorized_anet_transaction->anet_trans_id = $walletPay['RefNo'];
        $authorized_anet_transaction->status_code = $walletPay['Status'];
        $authorized_anet_transaction->status_type = $walletPay['TranCode'];
        $authorized_anet_transaction->save();
        return $authorized_anet_transaction;
    }


    public static function googlePayCharge($request, $facility)
    {
        // call heartland config
        self::heartlandConfig($facility);

        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-googlepay-call');
        //$log->info("Request Data 1: " . $vars);
        $log->info("Request Data google Pay: " . json_encode($request->all()));
        $paymentEnv = isset($facility->FacilityPaymentDetails->heartland_payment_env) ? $facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');
        $amount = ($paymentEnv == 'test' && $request->amount > 3) ? '3.00' : $request->amount;

        $card = new CreditCardData();
        $card->token = $request->token;
        //  $card->mobileType = MobilePaymentMethodType::GOOGLEPAY;
        $card->paymentSource = PaymentDataSourceType::GOOGLEPAYWEB;

        try {

            $response = $card->charge($amount)
                ->withCurrency('USD')
                ->withAllowDuplicates(true)
                ->execute();

            $log->info("Response Data Heartland Google Pay: " . json_encode($response));
        } catch (ApiException $e) {
            // Catch Global Payments API exception : 25-03-2025 : Vijay
            // Unexpected Gateway Response: 1 - System error Means Payment Gateway Down                            
            // Extract the response details
            $responseCode = $e->responseCode ?? 'N/A';  // Default to 'N/A' if not set
            $responseMessage = $e->responseMessage ?? 'Unknown error';
            if ($responseCode == '1') {
                $log->info("Payment failed. Due to HL Payment Gateway --" . json_encode($e->getMessage()));
                throw new ApiGenericException('Payment failed, Due to HL Payment Gateway Down');
            }
            throw new ApiGenericException('Payment failed,' . $e->getMessage());
            throw new ApiGenericException('Payment failed. Please correct your card details or try again with a different card..');
        } catch (Exception $e) {
            $log->info("Response Data Heartland Error: " . json_encode($e->getMessage()));
            throw new ApiGenericException('Payment Failed, Someting went wrong.');
            dd($e->getMessage());
        }
        return $response;
    }

    public static function applePayCharge($request, $facility)
    {
        // call config for heartland 
        self::heartlandConfig($facility);

        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-call');
        //$log->info("Request Data 1: " . $vars);
        $log->info("Request Data Apple Pay: " . json_encode($request->all()));

        $paymentEnv = isset($facility->FacilityPaymentDetails->heartland_payment_env) ? $facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');
        $amount = ($paymentEnv == 'test' && $request->amount > 3) ? '3.00' : $request->amount;


        $card = new CreditCardData();
        $card->mobileType = MobilePaymentMethodType::APPLEPAY;
        $card->paymentSource = PaymentDataSourceType::APPLEPAYWEB;
        $card->token = $request->token;

        try {
            $response = $card->charge($amount)
                ->withCurrency('USD')
                ->withInvoiceNumber($request->invoice_number)
                ->withAllowDuplicates(true)
                ->execute();
            return $response;
        } catch (ApiException $e) {
            // Catch Global Payments API exception : 25-03-2025 : Vijay
            // Unexpected Gateway Response: 1 - System error Means Payment Gateway Down                            
            // Extract the response details
            $responseCode = $e->responseCode ?? 'N/A';  // Default to 'N/A' if not set
            $responseMessage = $e->responseMessage ?? 'Unknown error';
            if ($responseCode == '1') {
                $log->info("Payment failed. Due to HL Payment Gateway --" . json_encode($e->getMessage()));
                throw new ApiGenericException('Payment failed, Due to HL Payment Gateway Down');
            }
            throw new ApiGenericException('Payment failed,' . $e->getMessage());
            throw new ApiGenericException('Payment failed. Please correct your card details or try again with a different card..');
        } catch (\Throwable $e) {
            $log->info("Response Data Heartland Error: " . json_encode($e->getMessage()));
            throw new ApiGenericException('Payment Failed, Someting went wrong.');
            dd($e->getMessage());
        }
    }

    public static function getPaymentList($request)
    {
        if (empty($request->facility_id)) {
            throw new ApiGenericException('Please select facility');
        }
        $facility = Facility::find($request->facility_id);
        self::heartlandConfig($facility);

        // get transaction id details 

        $dayStr = 'P' . $request->total_days . 'D';
        $dateFormat = 'Y-m-d\TH:i:s.00\Z';
        $dateMinus10 = new \DateTime();
        $dateMinus10->sub(new \DateInterval($dayStr));
        $dateMinus10Utc = gmdate($dateFormat, $dateMinus10->format('U'));
        $nowUtc = gmdate($dateFormat);

        if (isset($request->transactionId) && !empty($request->transactionId)) {
            $response = ReportingService::findTransactions()
                ->withTransactionId($request->transactionId)
                ->execute();
        } else {

            $response = ReportingService::findTransactions()
                ->withStartDate($dateMinus10Utc)
                ->withEndDate($nowUtc)
                ->execute();
        }

        return $response;
    }


    public static function makeVoidPaymentChargeHeartLand($tran_id, $facility)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
        // call heartland config
        self::heartlandConfig($facility);
        $response = Transaction::fromId($tran_id)
            ->void()
            ->execute();

        $log->info("Refund api response Heartland -- " . print_r($response, true));
        return $response;
    }

    public static function saveVoidTransactions($paymentReleaseResponse, $ticket_details, $originalTranId = null, $partialReleaseAmount = 0)
    {
        try {
            $logFactory = new LoggerFactory();
            $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
            $paymentReleaseResponse = json_decode($paymentReleaseResponse);
            $log->info("PreAuth Payment Release Response Object Heartland -- " . print_r($paymentReleaseResponse, true));

            $user_id = $ticket_details->user_id;
            $total = $partialReleaseAmount ?? 3.21;

            $brand = $paymentReleaseResponse->cardType ?? null;
            $card_last_four = $paymentReleaseResponse->cardLast4 ?? null;

            $refundTransaction = new RefundTransaction();

            if ($paymentReleaseResponse->responseCode === "00" && $paymentReleaseResponse->responseMessage === "Success") {
                $refundTransaction->sent                    = '1';
                $refundTransaction->user_id                 = $user_id;
                $refundTransaction->ip_address              = \Request::ip();
                $refundTransaction->total                   = $paymentReleaseResponse->authorizedAmount ?? $total;

                $refundTransaction->description             = "Heartland Void Sale Done for User: " . $user_id;
                $refundTransaction->card_type               = $brand ?? null;
                $refundTransaction->ref_id                  = $paymentReleaseResponse->transactionReference->transactionId ?? null;
                $refundTransaction->method                  = "void";
                $refundTransaction->payment_last_four       = $card_last_four ?? 0;
                $refundTransaction->auth_code               = $paymentReleaseResponse->transactionReference->authCode ?? null;
                $refundTransaction->response_code           = $paymentReleaseResponse->responseCode ?? null;
                $refundTransaction->response_message        = $paymentReleaseResponse->responseMessage ?? null;
                $refundTransaction->invoice_number          = $paymentReleaseResponse->transactionReference->orderId ?? null;
                $refundTransaction->anet_trans_id           = $paymentReleaseResponse->transactionReference->transactionId ?? null;
                $refundTransaction->status_message          = $paymentReleaseResponse->responseMessage ?? null;
                $refundTransaction->reference_key           = $ticket_details->ticket_number ?? null;
                $refundTransaction->created_at              = date("Y-m-d H:i:s");
                $refundTransaction->original_transaction_id = isset($originalTranId) ? $originalTranId : NULL;
                $refundTransaction->payment_gateway         = 'heartLand';
                $refundTransaction->save();

                $log->info("PreAuth Payment Release Response Save -- " . json_encode($refundTransaction));
                return $refundTransaction;
            } else {
                $log->info("PreAuth Payment Release Response Failed");
                return false;
            }
        } catch (\Exception $e) {
            $log->error("Error in saveVoidTransactions: " . $e->getMessage());
            throw $e;
        }
    }

    public static function updateCardExpiryHeartLand($request, $facility)
    {
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/heartland')->createLogger('heartland-api-call');
        // call heartland config
        self::heartlandConfig($facility);

        $card = new CreditCardData();
        $card->token = $request->token;
        $card->expMonth = $request->expMonth;
        $card->expYear = $request->expYear;

        $response = $card->verify()
            ->withCurrency("USD")
            ->execute();



        $log->info("Api response Card expiry Update Heartland -- " . print_r($response, true));
        return $response;
    }
}
