<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\Ticket;
use Illuminate\Support\Facades\Auth;
use Twilio\Rest\Client;
use Twilio\Exceptions\RestException;
use App\Models\OauthClient;
use App\Models\ParkEngage\CustomerPortalPermission;
use App\Models\User;

class CommonFunctions
{

    public static function sendSmsAfterReservation($reservation_id, $msg, $log)
    {
        $log->info("SMS Send Start");
        $reservation = Reservation::with('user')->find($reservation_id);
        if (!$reservation) {
            $log->info(" No Reservation found During SMS Send");
            throw new ApiGenericException('No reservation found.');
        }

        //send sms to user
        $accountSid = env('TWILIO_ACCOUNT_SID');
        $authToken  = env('TWILIO_AUTH_TOKEN');

        try {
            $client = new Client($accountSid, $authToken);
            // Use the client to do fun stuff like send text messages!
            $response = $client->messages->create(
                // the number you'd like to send the message to
                $reservation->user->phone,
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => env('TWILIO_PHONE'),
                    'body' => $msg
                )
            );
            $log->info("SMS Send to Mobile: " . $reservation->user->phone . " Succcesfully");
            return $response;
        } catch (\Exception $e) {
            $log->info("Exception In SMS Send, Mobile: " . $reservation->user->phone . " " . $e->getMessage() . ' File ' . $e->getFile() . ' Line ' . $e->getLine());
        }
    }

    public static function sendSms($phone, $message)
    {
        $accountSid = env('TWILIO_ACCOUNT_SID');
        $authToken  = env('TWILIO_AUTH_TOKEN');
        $client = new Client($accountSid, $authToken);
        try {
            $client->messages->create(
                // the number you'd like to send the message to
                $phone,
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => env('TWILIO_PHONE'),
                    // the body of the text message you'd like to send
                    'body' => $message
                )
            );
        } catch (\Exception $e) {
            return "Error: " . $e->getMessage();
        }
    }

    public static function makeRequest($url, $method, $headers = array(), $data = array())
    {

        $curl = curl_init();

        // If it's a GET request, append the data as query params to the URL
        if (strtoupper($method) === 'GET' && !empty($data)) {
            $url .= '?' . http_build_query($data);
        }

        // Set the URL
        curl_setopt($curl, CURLOPT_URL, $url);

        // Set request method
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, strtoupper($method));

        // If sending data with PUT or POST
        if ($method == 'PUT' || $method == 'POST') {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }

        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        // Return the transfer as a string instead of outputting it directly
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

        // Set additional headers
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $response = curl_exec($curl);
        $responseInfo = curl_getinfo($curl);

        if ($response == null) {
            $response = $responseInfo;
        } else {
            $response = json_decode($response, true);
        }

        // Close cURL session
        curl_close($curl);

        // Return response
        return $response;
    }

    public static function checkTicketNumber()
    {
        $ticket = 'PE' . rand(100, 999) . rand(100, 999);
        $isExist = Ticket::where('ticket_number', $ticket)->first();
        if ($isExist) {
            return $ticket = self::checkTicketNumber();
        }
        return $ticket;
    }

    public static function getFacilityByAuthUser()
    {
        $user = Auth::user();
        $user_type = $user->user_type;
        $partner_id = "";
        switch ($user_type) {
            case '3':
                $partner_id = $user->id;
                break;

            case '4':
                $partner_id = $user->created_by;
                break;

            case '12':
                $partner_id = $user->created_by;
                break;
            default:
                break;
        }
        $facility_ids = Facility::where('owner_id', $partner_id)->pluck('id');
        return  $facility_ids;
    }

    public static function printQuery($user)
    {
        $query = str_replace(array('?'), array('\'%s\''), $user->toSql());
        $query = vsprintf($query, $user->getBindings());
        dump($query);
    }

    public static function setDecryptedCard($request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $name = trim($cardData[0]);
        $name = explode(' ', $name);
        $firstName = $name[0];
        $lastName = " ";
        $zipCode = isset($cardData[4]) ? $cardData[4] : '';
        if (isset($name[1])) {
            $lastName = $name[1];
        }
        $request->request->add(
            [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );
        return $request;
    }


    // Function to calculate distance between two lat-long points
    public static function isWithinRadius($sourceLat, $sourceLong, $destinationLat, $destinationLong, $radiusInMeters = 0)
    {
        $earthRadiusKm = 6371; // Radius of Earth in kilometers

        // Convert degrees to radians
        $sourceLatRad = deg2rad($sourceLat);
        $sourceLongRad = deg2rad($sourceLong);
        $destinationLatRad = deg2rad($destinationLat);
        $destinationLongRad = deg2rad($destinationLong);

        // Haversine formula to calculate the distance
        $latDiff = $destinationLatRad - $sourceLatRad;
        $longDiff = $destinationLongRad - $sourceLongRad;

        $a = sin($latDiff / 2) * sin($latDiff / 2) +
            cos($sourceLatRad) * cos($destinationLatRad) *
            sin($longDiff / 2) * sin($longDiff / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        $distanceInKm = $earthRadiusKm * $c; // Distance in kilometers

        // Convert the distance from kilometers to meters
        $distanceInMeters = $distanceInKm * 1000;

        // Check if distance is within the given radius (in meters)
        return $distanceInMeters <= $radiusInMeters;
    }

    public static function getPartnerId($clientSecret, $allData = false)
    {
        $OauthClient = OauthClient::where(['secret' => $clientSecret])->first();
        if ($allData) {
            return $OauthClient;
        }
        if ($OauthClient) {
            return $OauthClient->partner_id;
        }
        return false;
    }

    public static function partnerCheck($request)
    {
        if ($request->header('X-ClientSecret')) {
            $clientSecret = $request->header('X-ClientSecret');
            return self::getPartnerId($clientSecret, true);
        } else if ($request->is_admin) {
            return OauthClient::where(['partner_id' => $request->partner_id])->first();
        }
        throw new ApiGenericException('Autherization Required', 401);
    }

    public static function getRestrictedPermissions($partner_id, $type)
    {
        $CustomerPortalPermission = CustomerPortalPermission::where(["partner_id" => $partner_id, "type" => $type])->whereNull('rm_id')->orderBy('list_order', 'ASC')->get();
        if (count($CustomerPortalPermission) > 0) {
            return $CustomerPortalPermission;
        }
        return false;
    }

    public static function getVehicleLimit($partner_id, $facilityConfig, $log)
    {
        $vehicleCountLimit = 0;
        $permissions = self::getRestrictedPermissions($partner_id, '3');
        $log->info("Partner Id: $partner_id and getRestrictedPermissions: " . json_encode($permissions));
        if ($permissions) {
            foreach ($permissions as $permission) {
                if ($permission->display_name == 'Enable Vehicle Limit') {
                    if (isset($facilityConfig->max_vehicle) && $facilityConfig->max_vehicle > 0)
                        $vehicleCountLimit = $facilityConfig->max_vehicle;
                    else {
                        $vehicleCountLimit = (int) User::where('id', $partner_id)->value('max_vehicle');
                    }
                }
            }
        }
        return $vehicleCountLimit;
    }
}
