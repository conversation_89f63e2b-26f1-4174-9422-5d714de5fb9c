<?php
namespace App\Console\Commands\CityParkingInc;

use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\UserValidateMaster;
use App\Models\ParkEngage\UserValidateMasterHistory;
/**
 * Emails reservation stub to user
 */
class ClerkCappingRenew extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clerk:capping-renew {partnerId?}';
    protected $log;
 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly clerk capping renew.';

	const PARTNER_ID = '363361';
    	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/clerk/')->createLogger('clerk-capping');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{ 
            $facilities = Facility::with(['FacilityPaymentDetails'])->where('owner_id', self::PARTNER_ID)->where('active', 1)->first();
		
            $this->setCustomTimezone($facilities->id);	
            $partnerIdArg = $this->argument('partnerId');
            if($partnerIdArg){
                $partnerId = $this->argument('partnerId');
            }else{
                $partnerId = self::PARTNER_ID;
            }
			
            $monthlyCapping = UserValidateMaster::where('partner_id',$partnerId)->get();
		    
            if (!$monthlyCapping) {
                $this->log->info("Monthly Capping Not Found.");
            }
		    $this->log->info("Permit Found" . json_encode($monthlyCapping));
                   
		    $count = 0;
		    foreach($monthlyCapping as $key=>$val){
                $this->createClerkCappingHistory($val);
                $capping = UserValidateMaster::find($val->id); 
                $capping->monthly_remaining_amount = $val->monthly_maximum_amount;
                $capping->month = '';
                $capping->year = '';
                $capping->max_remaining_hour = $val->max_hour;
                $capping->max_remaining_dollar = $val->max_dollar;
                $capping->full_amount_remaining = $val->full_amount_max;
                $capping->save(); // Save the updated data				
		    }
			$msg = "Total Count of Permit Renew Reminder: ".$count;
			$this->log->info($msg);
			return $msg;
        }catch(Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();			
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
    }

    public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		if (($facility) && ($facility->timezone != '')) {
			date_default_timezone_set($facility->timezone);
		} else {
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}
	}

    public function createClerkCappingHistory($cappingData)
	{
	  	if($cappingData){
            $renewDate = date('Y-m-d h:i:s');
			$cappingDataNew = new UserValidateMasterHistory();
                
			$cappingDataNew->capping_id 		        = $cappingData->id;
			$cappingDataNew->user_id 				    = $cappingData->user_id;
			$cappingDataNew->partner_id 			    = $cappingData->partner_id;                
			$cappingDataNew->monthly_maximum_amount 	= $cappingData->monthly_maximum_amount;
			$cappingDataNew->monthly_remaining_amount 	= $cappingData->monthly_remaining_amount;
			$cappingDataNew->paid_type 					= $cappingData->paid_type;                
			$cappingDataNew->month 					    = $cappingData->month;
			$cappingDataNew->year 					    = $cappingData->year;
            $cappingDataNew->created_at 			    = $renewDate;
			$cappingDataNew->updated_at 			    = $renewDate;
			$cappingDataNew->max_hour 			        = $cappingData->max_hour;
			$cappingDataNew->max_remaining_hour 		= $cappingData->max_remaining_hour;
			$cappingDataNew->max_dollar 			    = $cappingData->max_dollar;
			$cappingDataNew->max_remaining_dollar 		= $cappingData->max_remaining_dollar;
			$cappingDataNew->full_amount_max            = $cappingData->full_amount_max;
			$cappingDataNew->full_amount_remaining 		= $cappingData->full_amount_remaining;
			$cappingDataNew->status 			        = $cappingData->status;

            $cappingDataNew->save();                       
        }
        return true;
	}

}
