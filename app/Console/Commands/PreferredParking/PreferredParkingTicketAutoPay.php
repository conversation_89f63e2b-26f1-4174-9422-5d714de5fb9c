<?php

namespace App\Console\Commands\PreferredParking;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\TicketExtend;
use App\Classes\DatacapPaymentGateway;

/**
 * Emails reservation stub to user
 */
class PreferredParkingTicketAutoPay extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'preferredparking:ticket-autopay';
	protected $log;

	/**
	 * The console command description.
	 *
	 * @var string
	 */

	const PARTNER_ID = '91860';
	const IS_GATED_FACILITY = '0';

	public function __construct(LoggerFactory $logFactory)
	{
		parent::__construct();
		$this->log = (new LoggerFactory)->setPath('logs/PreferredParking/')->createLogger('autopay-datacap');
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$facilities = Facility::with(['FacilityPaymentDetails'])->where('owner_id', self::PARTNER_ID)->where('is_gated_facility', self::IS_GATED_FACILITY)->where('active', 1)->get();
		
		if (!$facilities) {
			$this->log->info("Facility Data Not Found --");
		}
		if ($facilities) {
			foreach ($facilities as $val) {
				$this->setCustomTimezone($val->id);
				$current_time = date('Y-m-d H:i:s');
				$this->log->info("Current Time Capture Data --" . $current_time);
				$this->log->info("Facility ID --" . $val->id);
				
				$ticket = Ticket::where('facility_id', $val->id)->whereNull('anet_transaction_id')
					->where(function ($query) use ($current_time) {
						$query->where('estimated_checkout', '<', $current_time)
							->orWhere('stop_parking_time', '<', $current_time);
					})
					->where('paid_type', '!=', '0')->where('is_closed', '0')->whereNull('reservation_id')->orderby('id', 'desc')->limit(20)->get();
				//dd($ticket[0]->id);	
				if ($ticket) {
					foreach ($ticket as $details) {

						// Apple Pay Google Payment cases that need to checkout from CRON
						if ($details->anet_transaction_id !== null) {
							$details->is_closed             = 1;
							$details->is_checkout           = 1;
							$details->closed_date           = $current_time;
							$details->payment_date          = $details->transaction->created_at;
							$details->save();
							$this->log->info("Session closed for Ticket ID -- " . $details->id);
							continue;
						}

						// Ticket Refunded from admin in case of Payment not done we close these tickets : PIMS -13251
						if ($details->refund_type == '3' && $details->refund_status == 'Refunded') {
							$details->is_closed             = 1;
							$details->is_checkout           = 1;
							$details->closed_date           = $current_time;
							$details->save();
							$this->log->info("Admin Release Ticket Closed from admin Ticket ID -- " . $details->id);
							continue;
						}

						// get textend details 
						$grandTotal = $details->grand_total;
						$discount_amount = isset($details->discount_amount) ? $details->discount_amount : '0.00';
						if ($details->is_extended == '1') {
							// get total amount  
							$ticketExtends = TicketExtend::where('ticket_number', $details->ticket_number)->orderby('id', 'asc')->get();
							foreach ($ticketExtends as $key => $value) {
								$grandTotal += $value->grand_total;
								$discount_amount += $value->discount_amount;
							}							
							$this->log->info("Ticket Extend Data Found -- " . $grandTotal);
							$this->log->info("Extend Ticket Discount Amount : -- " . $discount_amount);
						}
						$url = '';
						$device_type = '';
						if ($val->FacilityPaymentDetails) {
							if ($details->device_type == 'IM30') {
								$mid = $val->FacilityPaymentDetails->datacap_mid;
								//$mid = $val->FacilityPaymentDetails->datacap_ecommerce_mid;
								//$url = $val->FacilityPaymentDetails->datacap_script_url;
								$url = $val->FacilityPaymentDetails->datacap_preauth_url;
							} else {
								$mid = $val->FacilityPaymentDetails->datacap_ecommerce_mid;
								$url = $val->FacilityPaymentDetails->datacap_preauth_url;
							}
							$device_type = $details->device_type;
						} else {
							$this->log->info("Payment Settings not Found for this Facility -- " . json_encode($val));
							return 1;
						}

						$cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $details->id)->where('is_payment_complete', '0')->where('transaction_retry', '<', '3')->whereNotNull('ticket_id')->first();
						
						$this->log->info("Payment PreAuth Data --" . $cardCheck);
						$paid_amount = isset($details->paid_amount) ? $details->paid_amount : '0.00';
						$final_amount = $grandTotal - $discount_amount - $paid_amount;
					//	$final_amount = number_format($final_amount, 2);
						
						$amount = $final_amount;

						if ($cardCheck) {
							$data['Token'] = $cardCheck->token;
							$tran_id = $cardCheck->ref_id;

							$this->log->info("Payment Discount Amount --" . $discount_amount);
							$this->log->info("Payment Paid Amount --" . $paid_amount);
							$this->log->info("Payment Final Amount --" . $final_amount);
							$this->log->info("Payment Amount --" . $amount);
							$this->log->info("MID --" . $mid);
							$this->log->info("Transaction ID --" . $tran_id);
							$this->log->info("URL --" . $url);
							$this->log->info("Device type --" . $device_type);
							$this->log->info("Request Data --" . json_encode($data));
							if ($amount > 0) {
								try {
									$data['Amount'] = $amount;
									
									if ($details->is_extended == '1') {
										// get total amount  
										$ticketExtendsTime = TicketExtend::where('ticket_number', $details->ticket_number)->orderby('id', 'desc')->first();

										//if ($ticketExtendsTime->checkout_time < $current_time) {
										if ($ticketExtendsTime->checkout_time) {	
											//$paymentResponse = $this->makePaymentDataCap($data, $mid, $tran_id, $url,$device_type);
											$paymentResponse = DatacapPaymentGateway::makeTicketPaymentChargeDataCap($data, $mid, $tran_id, $url,$device_type);
                                                    
											$paymentRecord = json_decode($paymentResponse, TRUE);
										} else {
											$this->log->info("Ticket Continue --- :  Check extend Ticket  Cehckout time : {$ticketExtendsTime->checkout_time} -- Current Time {$current_time} ");
											continue;
										}
									} else {
										//$paymentResponse = $this->makePaymentDataCap($data, $mid, $tran_id, $url,$device_type);
										$paymentResponse = DatacapPaymentGateway::makeTicketPaymentChargeDataCap($data, $mid, $tran_id, $url,$device_type);
                                            
										$paymentRecord = json_decode($paymentResponse, TRUE);
									}

									$this->log->info("Payment Response -- " . $paymentResponse);
									$this->log->info("Facility ID -- " . json_encode($val->id));

									if ($paymentRecord['Status'] == 'Approved') {
										$brand = str_replace('/', '', $paymentRecord['Brand']);
										$authorized_anet_transaction = new AuthorizeNetTransaction();
										$authorized_anet_transaction->sent = '1';
										$authorized_anet_transaction->user_id = $details->user_id;
										$authorized_anet_transaction->ip_address = $cardCheck->ip_address;
										$authorized_anet_transaction->total = $final_amount;
										$authorized_anet_transaction->description = "Preferred Parking Auto Payment Done User : " . $details->user_id;
										$authorized_anet_transaction->card_type = $brand;
										$authorized_anet_transaction->ref_id = $paymentRecord["RefNo"];
										$authorized_anet_transaction->method = "card";
										$authorized_anet_transaction->payment_last_four = isset($details->card_last_four) ? $details->card_last_four : '0';
										$authorized_anet_transaction->auth_code = $paymentRecord["AuthCode"];
										$authorized_anet_transaction->response_code = $paymentRecord["ReturnCode"];
										$authorized_anet_transaction->response_message = "Processed";
										$authorized_anet_transaction->invoice_number = $paymentRecord["InvoiceNo"];
										$authorized_anet_transaction->anet_trans_id = $paymentRecord["InvoiceNo"];
										$authorized_anet_transaction->status_message = $paymentRecord["Status"];
										$authorized_anet_transaction->name = $brand;
										$authorized_anet_transaction->expiration = $cardCheck->expiry;

										$authorized_anet_transaction->save();
										$this->log->info("Payment Transaction Data  --" . $authorized_anet_transaction);
										$details->anet_transaction_id = $authorized_anet_transaction->id;
										$details->payment_date = $current_time;
										$details->is_closed = 1;
										$details->is_checkout = 1;
										$details->closed_date = $current_time;
										$details->payment_token = $paymentRecord["Token"];
										$details->save();
										$cardCheck->is_payment_complete = '1';
										$cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
										$cardCheck->save();
									} else {
										$cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
										$cardCheck->save();
									}
								} catch (Exception $e) {
									$msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
									$this->log->error($msg);
								}
							} else {
								$details->is_closed = 1;
								$details->is_checkout = 1;
								$details->closed_date = $current_time;
								$details->save();
							}
						} else if ($amount == '0.00') {
							$details->is_closed = 1;
							$details->is_checkout = 1;
							$details->closed_date = $current_time;
							$details->save();
						}
					}
				}
			}
		}
		return 1;
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		if (($facility) && ($facility->timezone != '')) {
			date_default_timezone_set($facility->timezone);
		} else {
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}
	}
	/*
	public function makePaymentDataCap($data, $mid, $tran_id, $url,$device_type)
	{
		$vars = json_encode($data);
		$this->log->info("Payment Request Data --" . $vars);
		$headers = [
			'Authorization: ' . $mid,
			'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
			'Content-Type: application/json',
			'Accept: application/json',
			'Accept-Language: en-US,en;q=0.5',
			'Cache-Control: no-cache'
		];
		$this->log->info("Payment Request Header --" . json_encode($headers));
		//$url = "https://pay.dcap.com/v1/credit/sale/";
		
		$curl = curl_init();
		
		if($device_type == 'IM30'){
			$this->log->info("Payment Request URL --" . $url);
			curl_setopt($curl, CURLOPT_URL, $url);
			curl_setopt($curl, CURLOPT_POST, 1);
		}else{
			$this->log->info("Payment Request URL --" . $url.$tran_id);
			curl_setopt($curl, CURLOPT_URL,$url.$tran_id);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
		}
			
		curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
		$response = curl_exec($curl);
		curl_close($curl);
		$this->log->info("Payment Data --" . json_encode($response));
		return $response;
	}
	*/
}
