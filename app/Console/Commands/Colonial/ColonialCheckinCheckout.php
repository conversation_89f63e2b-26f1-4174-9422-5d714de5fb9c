<?php

namespace App\Console\Commands\Colonial;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Models\Ticket;

class ColonialCheckinCheckout extends Command
{

    protected $signature = 'colonial:checkin-checkout';

    protected $description = 'close all the open ticket which not closed after event';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/cron-checkin-checkout')->createLogger('colonial-checkin-checkout');
    }

    public function handle()
    {
        $facility_id = 158;
        $this->log->info("checkout start");
        try {
            $today = date("Y-m-d");
            $ticketData = Ticket::whereDate('checkin_time', '<', $today)->where(['facility_id' => $facility_id, 'is_checkout' => '0'])->get();
            //->whereNotNull("anet_transaction_id")->WhereNotNull("reservation_id")
            if (count($ticketData) > 0) {
                $this->log->info("Before checkout email about to send");
                //user mail
                foreach ($ticketData as $val) {
                    if ($val->anet_transaction_id != '' || $val->reservation_id != '') {
                        $status = Ticket::where(['ticket_number' => $val->ticket_number, 'facility_id' => $facility_id])->update(['is_checkout' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => date("Y-m-d H:i:s")]);
                        $this->log->info("Checkout update for Ticket No " . $val->ticket_number);
                    }
                }
            }
        } catch (Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            Mail::send(
                "mapco.alert-error-checkout",
                ['data' => $msg],
                function ($message) use ($msg) {
                    $message->to(['<EMAIL>'])->subject("Error : Doc 79 Cron Alert Checkout");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Queue ended");
        }
    }
}
