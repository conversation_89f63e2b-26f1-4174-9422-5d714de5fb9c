<?php

namespace App\Console\Commands\Manlo;

use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class PermitAutoRenewManlo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'manlo:permit-renew';
    protected $log;
 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';

	const PARTNER_ID = '169163';
	const FACILITY_ID = '275'; 
	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/manlo/')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{ 
            	
            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));
            
            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
      
            $monthlyRequest = PermitRequest::with(['facility.FacilityPaymentDetails','user','PermitVehicle'])->where('partner_id',self::PARTNER_ID)->whereDate('desired_end_date', '<=', $permit_end_date)->whereDate('desired_start_date', '>=', $permit_start_date)->whereNull('cancelled_at')->whereNull('deleted_at')->where('user_consent',1)->whereNotNull('anet_transaction_id')->whereNull('business_id')->orderBy('id','desc')->where('permit_rate_id','!=',302)->limit(1)->get();
		    //dd($permit_start_date,$permit_end_date,self::PARTNER_ID,$monthlyRequest[0]->id,$monthlyRequest[0]->user_id); 
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
		    $this->log->info("Permit Found" . json_encode($monthlyRequest));
            // Create Permit Request History
            $count = 0;
		    foreach($monthlyRequest as $key=>$val){
                $permitRate = PermitRate::where('id',$val->permit_rate_id)->first();    
                			
                $this->log->info("Permit Rate Found" . json_encode($permitRate));
                if($permitRate->rate=='0.00'){
                    $this->createPermitRequestHistoryNew($val);
                    $maxDays=date('t');
                    $val->no_of_days          = $maxDays;    
                    $val->permit_rate         = $permitRate->rate; 
                    $val->desired_start_date  = $desired_start_date;
                    $val->desired_end_date    = $desired_end_date;
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
					if($facility_brand_setting){
						$val->facility_logo_id = $facility_brand_setting->id;
					}else{
						$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
						$val->logo_id  = $brand_setting->id;
					}
					$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
					$view = "usm.permit-renew-free";
					Mail::send(
                        $view, 
                        ['data' => $val, 'brand_setting' => $brand_setting], 
                        function ($message) use($val) {
                            $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to ". $val->user->email);                   
                }else if($permitRate->rate >"0.00"){
					
                    if($permitRate){
						
                        if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                            $paymentProfile = PlanetPaymentProfile::where('user_id',$val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $permitRate->rate;
                            if($paymentProfile){
                                $val->session_id = $paymentProfile->token;
                                $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                                $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                                if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                    $this->log->info("planet Payment Failed Response");
                                    // Failure mail send to user
                                    #DD PIMS-11368	                 
                                    if(!isset($paymentByToken)){
                                        $paymentByToken = NULL;
                                    }
                                 
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    $expiry    = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if($facility_brand_setting){
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    }else{
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id',$val->partner_id)->first();
									$partner_name = "Manlo Parking Services";
                                    if($val->transaction_retry > '1'){
                                        $view='usm.permit-cancel-reminder';  
                                    }
									$view="usm.permit-renew-fail";  
                                    Mail::send(
                                        $view, 
                                        ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], 
                                        function ($message) use($val) {
                                           $message->to([$val->user->email])->subject("Important: Action Required for Your Permit Payment");
                                           $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);
                                }else{
                                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));  
                              
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $maxDays=date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $val->permit_rate         = $permitRate->rate; 
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    //user mail
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if($facility_brand_setting){
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    }else{
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id',$val->partner_id)->first();
									$partner_name = "Manlo Parking Services";
									$view = "usm.permit-renew";
                                    Mail::send(
                                        $view, 
                                        ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], 
                                        function ($message) use($val) {
                                            $message->to($val->user->email)->subject("Payment made successfully against Permit #". $val->account_number);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);
                                }
                            }else{
                                $this->log->info("Payment Profile not found");
                            }                            
                        }else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
							$amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $permitRate->rate;
							$ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
							$url = $val->facility->FacilityPaymentDetails->datacap_script_url;
                        
							$paymentProfile = DatacapPaymentProfile::where('user_id',$val->user_id)->first();
							$this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
							//dd($amount,$ecommerce_mid,$url,$paymentProfile);
							if($paymentProfile){
								$data['Token'] = $paymentProfile->token;
								if ($amount > 0) {
									//$amount = number_format($amount, 2);
									$data['Amount'] = $amount;
									$data['Token'] = $paymentProfile->token;
									$data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
									$data["CardHolderID"] = "Allow_V2";
									$this->log->info("Payment Request Data --" . json_encode($data)."--". json_encode($ecommerce_mid). "--" . json_encode($url));
									$paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
									if($paymentResponse["Status"] =="Error"){
										$this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        #DD PIMS-11368	                 
                                        if(!isset($paymentResponse)){
                                            $paymentResponse = NULL;
                                        }
                                 		if($paymentResponse["Message"] =="Open Testing Account Number Not Found for this Merchant ID"){
											$this->log->info("Invalid payment information. Please verify and try again or use another card .");
										}else{
											$this->log->info("Invalid payment information. Please verify and try again or use another card.");
										}
									}else if($paymentResponse["Status"] == "Declined"){
                                        #DD PIMS-11368	                 
                                        if(!isset($paymentResponse)){
                                            $paymentResponse = NULL;
                                        }
                                 		// Failure mail send to user
										$val->card_last_four = $paymentProfile->card_last_four;
										$val->card_name = $paymentProfile->card_name;
										$expiry    = $paymentProfile->expiry;
										if($expiry){
											$expiry_data = str_split($expiry, 2);
											$val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
										}else{
											$val->card_expiry = "-";
										}
										$facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
										if($facility_brand_setting){
											$val->facility_logo_id = $facility_brand_setting->id;
										}else{
											$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
											$val->logo_id  = $brand_setting->id;
										}
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
										$view="usm.permit-renew-fail"; 
										Mail::send(
											$view, 
                                            ['data' => $val, 'brand_setting' => $brand_setting], 
                                            function ($message) use($val) {
                                            $message->to([$val->user->email])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
												$message->from(config('parkengage.default_sender_email'));
											}
										);
										$this->log->info("Mail sent to ". $val->user->email);   
									}
									if ($paymentResponse['Status'] == 'Approved') {
										$this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
										$request = new Request([
											'total'   => $amount,
											'card_last_four' => $paymentProfile->card_last_four,
											'expiration' => $paymentProfile->expiry
										]);
										$this->log->info("Save Transaction Data Request --" . json_encode($request)."--".json_encode($val->user_id));
										$authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request,$paymentResponse, $val->user_id,'');
										$this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));  
                                      	$maxDays=date('t');
										$val->no_of_days          = $maxDays;    
										$val->anet_transaction_id = $authorized_anet_transaction->id;
										$val->permit_rate         = $permitRate->rate; 
										$val->desired_start_date  = $desired_start_date;
										$val->desired_end_date    = $desired_end_date;
										$val->save();
										//user mail
										$val->card_last_four = $paymentProfile->card_last_four;
										$val->card_name = $paymentProfile->card_name;
										$expiry    = $paymentProfile->expiry;
										if($expiry){
											$expiry_data = str_split($expiry, 2);
											$val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
										}else{
											$val->card_expiry = "-";
										}
										$facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
										if($facility_brand_setting){
											$val->facility_logo_id = $facility_brand_setting->id;
										}else{
											$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
											$val->logo_id  = $brand_setting->id;
										}
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
										$view = "usm.permit-renew";
										Mail::send(
											$view, 
                                            ['data' => $val, 'brand_setting' => $brand_setting], 
                                            function ($message) use($val) {
												$message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
												$message->from(config('parkengage.default_sender_email'));
											}
										);
										$this->log->info("Mail sent to ". $val->user->email);                       
                                    }
                                }
                            }else{
                                $this->log->info("Payment Profile not found");
                            } 
                        }else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                            
							$paymentProfile = HeartlandPaymentProfile::where('user_id',$val->user_id)->first();
                            
                            $this->log->info("Payment Profile Data --" . $val->account_number . "--" . json_encode($paymentProfile));
							$processing_fee = $val->facility->permit_processing_fee;
							$permit_rate = $permitRate->rate;
							$final_amount = $processing_fee + $permit_rate;

                            $permit_services = PermitRequestServiceMapping::where('permit_request_id',$val->id)->pluck('permit_service_id');
                    
                            $services  = PermitServices::with([
                            'permit_service_criteria_mappings' => function($query) use ($permit_services) {
                                $query->whereIn('permit_service_id',$permit_services)
                                ->with('criteria');
                            }
                            ])
                            ->select('permit_services.*')
                            ->whereIn('id',$permit_services)
                            ->orderBy('permit_services.id', 'asc') 
                            ->get();
                            if ($services) {
                                
                                foreach ($services as $permitService) {
                                    $final_amount += (float) $permitService->permit_service_rate;
                                }
                            }
                        
                            if(count($services) > 0){
                                $services = $this->formatPermitServiceCriteria($services);
                            }

                            $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;
                        
							if($paymentProfile){
								$request = new Request([
									'Amount'   => $amount,
									'total'   => $amount,
									'token' => $paymentProfile->token,
									'zipcode' => $paymentProfile->zipcode,
									'card_last_four' => $paymentProfile->card_last_four,
									'expiration_date' => $paymentProfile->expiry,
                                    'original_total' => $permitRate->permit_final_amount
								]);


                                #end add parking time in email
                                $permit_validity = '';
                                $permitRateDescHour = array();
                                $permitRateDetails	= PermitRate::find($val->permit_rate_id);
                                if($permitRateDetails){
                                    $permitRateDescHour = PermitRateDescription::find($permitRateDetails->permit_rate_description_id);
                                    if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")){
                                        $permit_validity = '1 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")){
                                        $permit_validity = '2 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")){
                                        $permit_validity = 'Quarterly';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")){
                                        $permit_validity = '4 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")){
                                        $permit_validity = '5 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")){
                                        $permit_validity = 'Half Yearly';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")){
                                        $permit_validity = '7 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")){
                                        $permit_validity = '8 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")){
                                        $permit_validity = '9 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")){
                                        $permit_validity = '10 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")){
                                        $permit_validity = '11 Month';
                                    }else if(isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")){
                                        $permit_validity = '1 Year';
                                    }else{
                                        $permit_validity = '1 Month';
                                    }                               
                                }
								$paymentResponse = '---';	
								try{
									$paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility); 									
								}catch (Exception $e) {
									$this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                	if (str_contains($e->getMessage(), 'duplicate')) { 
                                    }else{
                                        $val->transaction_retry   = $val->transaction_retry + 1;
                                        $val->save();
                                        // Failure mail send to user
                                        $expiry    = $paymentProfile->expiry;
                                        if($expiry){
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                        }else{
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if($facility_brand_setting){
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        }else{
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $day_of_month = date('d');
                                        if($day_of_month =='05'){
                                            $view='usm.permit-cancel-reminder';  
                                            //call skiData Api to cancel resuable ticket
                                            $subject = "Important Notice: Permit Cancellation";
                                            
                                            $val->status = '0';
                                            $val->user_consent = '0';
                                            $val->cancelled_at = $permitRenewDate;
                                            $val->save();                                            
                                        }else{
                                            $view="usm.permit-renew-fail"; 
                                            $subject = "Important: Action Required for Your Permit Payment";
                                        }
                                        $partnerDetails = User::where('id',$val->partner_id)->first();
                                        $partner_name = "Manlo Parking Services";
                                        $val->final_amount = $final_amount;
                                        Mail::send(
                                            $view, ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'services' => $services,'permitRateDescHour' => $permitRateDescHour,'permit_validity'=>$permit_validity,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], function ($message) use($val,$subject) {
                                                //$message->to([$val->user->email])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                                $message->to([$val->user->email])->subject($subject);
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to ". $val->user->email);
                                    }                                    
								}
							
								$this->log->info("Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));
                            
								if (isset($paymentResponse->responseMessage) && $paymentResponse->responseMessage == 'APPROVAL') {
                                    $this->createPermitRequestHistoryNew($val);
                                    $user_id = $val->user_id;
									$authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request,$paymentResponse, $user_id);
									$this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
									$maxDays=date('t');
									$val->no_of_days          = $maxDays;
									$val->anet_transaction_id = $authorized_anet_transaction->id;
									$val->permit_rate         = $permitRate->rate; 
									$val->desired_start_date  = $desired_start_date;
									$val->desired_end_date    = $desired_end_date;
									$val->permit_final_amount = $final_amount;
									$val->processing_fee      = $processing_fee;
                                    $val->promocode           = NULL;
                                    $val->discount_amount     = '0.00';
                                    $val->transaction_retry   = $val->transaction_retry + 1;
									$val->save();

                                	//user mail
									
									$expiry    = $paymentProfile->expiry;
									if($expiry){
										$expiry_data = str_split($expiry, 2);
										$paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
									}else{
										$paymentProfile->card_expiry = "-";
									}
									$facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
									if($facility_brand_setting){
										$paymentProfile->facility_logo_id = $facility_brand_setting->id;
									}else{
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
										$paymentProfile->logo_id  = $brand_setting->id;
									}
									$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
									$partnerDetails = User::where('id',$val->partner_id)->first();
									$partner_name = "Manlo Parking Services";
									$view = "usm.permit-renew"; 
									Mail::send(
										$view, 
                                        ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'services' => $services,'permitRateDescHour' => $permitRateDescHour,'permit_validity'=>$permit_validity,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], 
                                        function ($message) use($val) {
										    $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #". $val->account_number);
											$message->from(config('parkengage.default_sender_email'));
                                        }
									);
									$this->log->info("Mail sent to ". $val->user->email);
                                }else{
                                    #DD PIMS-11368	                 
                                    if(!isset($paymentResponse)){
                                        $paymentResponse = NULL;
                                    }
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    // Failure mail send to user
                                    $expiry    = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        $paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if($facility_brand_setting){
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    }else{
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $day_of_month = date('d');
                                    if($day_of_month =='05'){
                                        $view='usm.permit-cancel-reminder';  
                                        //call skiData Api to cancel resuable ticket
                                        $subject = "Important Notice: Permit Cancellation";

                                        $val->status = '0';
                                        $val->user_consent = '0';
                                        $val->cancelled_at = $permitRenewDate;
                                        $val->save();                                            
                                    }else{
                                        $view="usm.permit-renew-fail"; 
                                        $subject = "Important: Action Required for Your Permit Payment";
                                    }
                                    
                                    $partnerDetails = User::where('id',$val->partner_id)->first();
                                    $partner_name = "Manlo Parking Services";
                                    $val->final_amount = $final_amount;
                                
                                    Mail::send(
                                        $view, 
                                        ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'services' => $services,'permitRateDescHour' => $permitRateDescHour,'permit_validity'=>$permit_validity,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], 
                                        function ($message) use($val,$subject) {
                                            $message->to([$val->user->email])->subject($subject);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);
                            }
                        }
                    }
                }
                }else{
                    $this->log->info("Permit Rate Not Found");
                }
			    $count++;				
		    }
			$msg = "Total Count of Permit Renew: ".$count;
			$this->log->info($msg);
			return $msg;
        }catch(Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
			
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
    }

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        
		$i=0;
		foreach ($services as $service) {
			if(count($service->permit_service_criteria_mappings) > 0){
				$formatted = [];
				foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
					$item = $permit_service_criteria_mapping->criteria;
					$days = explode(',', $item->days);
					sort($days); // Sort days to match the sequence of $allDays
					if ($days == $allDays) {
						$dayNamesStr = 'All Days';
					} else {
						$dayNames = array_map(function($day) use ($daysMap) {
							return $daysMap[$day];
						}, $days);
						$dayNamesStr = implode(',', $dayNames);
					}
	
					$entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
					$entry_time_end = date('h:iA', strtotime($item->entry_time_end));
					$exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
			
					// Adjust exit time if it's greater than 24 hours
					$exit_time = explode(":",$item->exit_time_end);
					if($exit_time[0]>23){
						$next_hr = $exit_time[0] - 24;
						$item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
						$exit_time_overflow = ' (next day)';
					}else{
						$exit_time_overflow = '';
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
					}
	
			
					$formatted[] = [
						'days' => $dayNamesStr,
						'entry_time_begin' => $entry_time_begin,
						'entry_time_end' => $entry_time_end,
						'exit_time_begin' => $exit_time_begin,
						'exit_time_end' => $exit_time_end . $exit_time_overflow,
					];
				}
				$services[$i]->criteria=$formatted;
			}
			$i++;
		}

        return $services;
    }

    #add parking time in email
    private function formatPermitRateCriteria($criteria)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        $formatted = [];

        foreach ($criteria as $item) {
            $days = explode(',', $item->days);
            sort($days); // Sort days to match the sequence of $allDays
            if ($days == $allDays) {
                $dayNamesStr = 'All Days';
            } else {
                $dayNames = array_map(function($day) use ($daysMap) {
                    return $daysMap[$day];
                }, $days);
                $dayNamesStr = implode(',', $dayNames);
            }

            $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
            $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
            $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
    
            // Adjust exit time if it's greater than 24 hours
            $exit_time = explode(":",$item->exit_time_end);
            if($exit_time[0]>23){
                $next_hr = $exit_time[0] - 24;
                $item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                $exit_time_overflow = ' (next day)';
            }else{
                $exit_time_overflow = '';
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            }

    
            $formatted[] = [
                'days' => $dayNamesStr,
                'entry_time_begin' => $entry_time_begin,
                'entry_time_end' => $entry_time_end,
                'exit_time_begin' => $exit_time_begin,
                'exit_time_end' => $exit_time_end . $exit_time_overflow,
            ];
        }

        return $formatted;
    }

	public function createPermitRequestHistoryNew($monthlyRequest)
	{
	  	if($monthlyRequest){
            $permitRenewDate = date('Y-m-d h:i:s');
			$permitData = new PermitRequestRenewHistory();
                
			$permitData->permit_request_id 		= $monthlyRequest->id;
			$permitData->user_id 				= $monthlyRequest->user_id;
			$permitData->facility_id 			= $monthlyRequest->facility_id;                
			$permitData->anet_transaction_id 	= $monthlyRequest->anet_transaction_id;
			$permitData->tracking_code 			= $monthlyRequest->tracking_code;
			$permitData->email 					= $monthlyRequest->email;                
			$permitData->name 					= $monthlyRequest->name;
			$permitData->phone 					= $monthlyRequest->phone;
			$permitData->permit_rate 			= $monthlyRequest->permit_rate;
			$permitData->permit_rate_id 		= $monthlyRequest->permit_rate_id;
			$permitData->approved_on 			= $monthlyRequest->approved_on;
			$permitData->account_number 		= $monthlyRequest->account_number;
			$permitData->monthly_duration_value = $monthlyRequest->monthly_duration_value;
			$permitData->no_of_days 			= $monthlyRequest->no_of_days;
			$permitData->partner_id 			= $monthlyRequest->partner_id;
			$permitData->license_number 		= $monthlyRequest->license_number;
			$permitData->mer_reference 			= $monthlyRequest->mer_reference;
			$permitData->image_front 			= $monthlyRequest->image_front;
			$permitData->image_back 			= $monthlyRequest->image_back;
			$permitData->user_consent 			= $monthlyRequest->user_consent;
			$permitData->vehicle_id 			= $monthlyRequest->vehicle_id;
			$permitData->is_admin 				= $monthlyRequest->is_admin;
			$permitData->ex_month 				= $monthlyRequest->ex_month;
			$permitData->ex_year 				= $monthlyRequest->ex_year;
			$permitData->payment_gateway 		= $monthlyRequest->payment_gateway;
			$permitData->permit_type 			= $monthlyRequest->permit_type;
			$permitData->is_payment_authrize 	= $monthlyRequest->is_payment_authrize;
			$permitData->session_id 			= $monthlyRequest->session_id;
			$permitData->permit_type_name 		= $monthlyRequest->permit_type_name;
			$permitData->skidata_id 			= $monthlyRequest->skidata_id;
			$permitData->skidata_value 			= $monthlyRequest->skidata_value;
			$permitData->acknowledge 			= $monthlyRequest->acknowledge;
			$permitData->facility_zone_id 		= $monthlyRequest->facility_zone_id;
			$permitData->desired_start_date 	= $monthlyRequest->desired_start_date;
			$permitData->desired_end_date 		= $monthlyRequest->desired_end_date;
			$permitData->cancelled_at 			= $monthlyRequest->cancelled_at;
			$permitData->created_at 			= $permitRenewDate;
			$permitData->updated_at 			= $permitRenewDate;
			$permitData->deleted_at 			= $monthlyRequest->deleted_at;
                
            $permitData->hid_card_number        = $monthlyRequest->hid_card_number;
            $permitData->account_name           = $monthlyRequest->account_name;
            $permitData->permit_final_amount    = $monthlyRequest->permit_final_amount;
            $permitData->user_remark            = $monthlyRequest->user_remark;
            $permitData->user_type_id           = $monthlyRequest->user_type_id;
            $permitData->is_antipass_enabled    = $monthlyRequest->is_antipass_enabled;
            $permitData->admin_user_id          = $monthlyRequest->admin_user_id;
            $permitData->discount_amount        = $monthlyRequest->discount_amount;
            $permitData->promocode              = $monthlyRequest->promocode;
            $permitData->negotiated_amount      = $monthlyRequest->negotiated_amount;
            $permitData->processing_fee         = $monthlyRequest->processing_fee; 
            $permitData->refund_amount          = $monthlyRequest->refund_amount;
			$permitData->refund_type            = $monthlyRequest->refund_type;
			$permitData->refund_remarks         = $monthlyRequest->refund_remarks;
			$permitData->refund_date            = $monthlyRequest->refund_date;
			$permitData->refund_by              = $monthlyRequest->refund_by;
			$permitData->refund_transaction_id  = $monthlyRequest->refund_transaction_id;
			$permitData->refund_status          = $monthlyRequest->refund_status;

            $permitData->save();
                       
        }
        return true;
	}
}
