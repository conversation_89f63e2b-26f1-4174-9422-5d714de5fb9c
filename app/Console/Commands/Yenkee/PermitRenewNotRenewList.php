<?php

namespace App\Console\Commands\Yenkee;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\PermitRequest;
use App\Models\User;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class PermitRenewNotRenewList extends Command
{
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permit:renew-not-renew-notify-partner';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send the list of permits to partners who have renewed and not renewed permits on the 5th day of the current month after the auto-renewal of permits.";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/Yenkee/')->createLogger('renew_notrenew');

    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info("Called Handle function.");
            $facilityId = [155];
            $partnerId = [26380];

            $facilites = Facility::whereIn('owner_id',$partnerId)->get();
            foreach ($facilites as $facility) {
                // Set time zone
                QueryBuilder::setCustomTimezone($facility->id);

                $configuredDays = FacilityConfiguration::where('facility_id',$facility->id)->whereIn('partner_id',$partnerId)->first();

                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $facility->id)->first();
    
                if($facility_brand_setting){
                    $facility->facility_logo_id = $facility_brand_setting->id;
                    $this->log->info("facility_logo_id:" .$facility->facility_logo_id);
                    
                }else{
                    $brand_setting = BrandSetting::whereIn('user_id', $partnerId)->first(); 
                    $facility->logo_id  = $brand_setting->id;
                    $this->log->info("Partner logo id:" .$facility->log_id);
                    $color = $brand_setting->color;
                    $this->log->info('brand Setting color: ' . $color);
                }
                
                $partnerEmail = User::select('email')->whereIn('id',$partnerId)->first();

                // currentDate 
                $currentDate = Carbon::now();
                $this->log->info("Current Time: ".$currentDate);

                $formattedCurrentDate = $currentDate->toDateString();
                // First day of the month
                $firstDayOfMonth = Carbon::now()->startOfMonth();
                $currentMonthStart = Carbon::now()->startOfMonth()->toDateString();
                $currentMonthEnd = Carbon::now()->endOfMonth()->toDateString();
                // 5th day
                $fifthDayOfMonth = $firstDayOfMonth->copy()->addDays($configuredDays['permit_renew_notrenew_d'] - 1)->toDateString();
                // dd($fifthDayOfMonth);

                $this->log->info("5th day of the month: ".$fifthDayOfMonth);

                // Get the first day of the previous month
                $previousMonthStart = Carbon::now()->subMonth()->startOfMonth()->toDateString();
                $this->log->info("Previous month start date: ".$previousMonthStart);
                
                // Get the last day of the previous month
                $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth()->toDateString();
                $this->log->info("Previous month end date: ".$previousMonthEnd);

                $permitsLists = PermitRequest::where('permit_rate','!=',0)
                        ->where('user_consent','=',1)  
                        ->where('status',1)
                        ->whereNull('deleted_at')
                        ->whereNull('cancelled_at')
                        ->whereIn('partner_id', $partnerId)
                        ->whereDate('desired_start_date','>=',$previousMonthStart)
                        ->whereDate('desired_end_date','<=',$previousMonthEnd)
                        ->get();
                
                if (!$permitsLists) {
                    $this->log->info("Permits Not Found.");   
                }
                $this->log->info("Permit Details: ".json_encode($permitsLists)."=====Permit Count:".count($permitsLists).">>>>>>>>>>> Formatted current date:" .$formattedCurrentDate. "== fifth day date::" .$fifthDayOfMonth);
                
                foreach ($permitsLists as $permitsList) 
                {
                    PermitRequest::where('id',$permitsList->id)
                                ->update([
                                    'status' => 0,
                                    'user_consent' => 0
                                ]);
                } 
                // Pending Record for Permit Renew
                $updatedPermits = PermitRequest::select(
                    'permit_requests.*'
                    )
                        ->where('permit_rate','!=',0)
                        ->where('user_consent','=',1)
                        ->where('status',1)
                        ->whereNull('deleted_at')
                        ->whereNull('cancelled_at')
                        ->whereIn('partner_id', $partnerId)
                        ->whereDate('desired_start_date','>=',$previousMonthStart)
                        ->whereDate('desired_end_date','<=',$previousMonthEnd)
                        ->get();
                   //  dd($previousMonthStart,$previousMonthEnd);
                   // Success Record for Permit Renew
                    $updatedPermitSucess = PermitRequest::select(
                        'permit_requests.*'
                        )
                            ->where('permit_rate','!=',0)
                            ->where('user_consent','=',1)
                            ->where('status',1)
                            ->whereNull('deleted_at')
                            ->whereNull('cancelled_at')
                            ->whereIn('partner_id', $partnerId)
                        //    ->whereDate('desired_start_date','>=',$currentMonthStart)
                            ->whereDate('desired_end_date','=',$currentMonthEnd)
                            ->whereDate('approved_on', '<',$currentMonthStart)
                            ->get();    
                // dd($formattedCurrentDate, '==', $fifthDayOfMonth);
               // dd($updatedPermitSucess,$partnerId,$currentMonthEnd);            
                if($formattedCurrentDate == $fifthDayOfMonth)
                {
                    $this->log->info("Under If condition.");

                    $excelSheetName = 'Auto Pay Status Report - ' . date('Y-m-d');
                    $this->log->info('Excel sheet name: ' . $excelSheetName);
                    
                    $data = $data1 = [];
                    Excel::create(
                        $excelSheetName,
                        function ($excel) use (
                            $color,
                            $permitsLists,
                            $facility,
                            $updatedPermits,
                            $updatedPermitSucess
                        ) {
                            //dd($updatedPermitSucess);
                            $excel->sheet('Auto Pay Status Report', function ($sheet) use (
                                $color,
                                $permitsLists,
                                $facility,
                                $updatedPermits,
                                $updatedPermitSucess
                            ) {
                                $sheet->setWidth(array(
                                    'A'     => 15,
                                    'B'     =>  35,
                                    'C'     =>  35,
                                    'D'     =>  35,
                                    'E'     =>  35,
                                    'F'     =>  35,
                                    'G'     =>  25
                                ));

                                $this->log->info('cityparking 99 .');

                                $sheet->getColumnDimension('A')->setWidth(10);
                                $sheet->getColumnDimension('B')->setWidth(36);
                                $sheet->getColumnDimension('C')->setWidth(36);
                                $sheet->getColumnDimension('D')->setWidth(25);
                                $sheet->getColumnDimension('E')->setWidth(20);
                                $sheet->getColumnDimension('F')->setWidth(20);
                                $sheet->getColumnDimension('G')->setWidth(25);

                                //end width for colom here
                                //set header for excel work
                                $sheet->mergeCells('A1:G1');
                                //$sheet->mergeCells('F1:J1');
                                $sheet->getRowDimension(1)->setRowHeight(60);
                                $sheet->setCellValue('A1', $facility->full_name.' Permit Payment Renewal User List');
                                $sheet->cell('A1:G1', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('20');
                                });

                                $cellValue = "Print Date - \n" .  date('Y-m-d h:i');
                                $sheet->mergeCells('A2:B2');
                                $sheet->setCellValue('A2', $cellValue);
                                $sheet->getStyle('A2:B2')->getAlignment()->setWrapText(true);
                                // Set the height of cell H2 (adjust as needed)
                                $sheet->getRowDimension(2)->setRowHeight(80);
                                $sheet->getRowDimension(3)->setRowHeight(50);

                                $sheet->cell('A2:B2', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#D6DCE4');
                                    $cell->setFontColor('#000000');
                                    $cell->setFontSize('16');
                                });

                                $location = "Location Name \r" . $facility->full_name;
                                $sheet->mergeCells('C2:D2');
                                $sheet->setCellValue('C2', $location);
                                $sheet->getStyle('C2:D2')->getAlignment()->setWrapText(true);

                                $sheet->cell('C2:D2', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#ffffff');
                                    $cell->setFontColor('#000000');
                                    $cell->setFontSize('18');
                                });

                                $sheet->mergeCells('E2:G2');
                                $locationId = "Location ID \n" . $facility->garage_code;
                                $sheet->setCellValue('E2', "$locationId");
                                $sheet->getStyle('E2:G2')->getAlignment()->setWrapText(true);

                                $sheet->cell('E2:G2', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#D6DCE4');
                                    $cell->setFontColor('#040D12');
                                    $cell->setFontSize('18');
                                });

                                $sheet->cell('A3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('B3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('C3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('D3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('E3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('F3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('G3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $this->log->info('cityparking 111 .');

                                $i = $j = 1;
                                $sheet->cell('A3:I3', function ($cell) use ($color) {
                                    $cell->setAlignment('left'); // Center horizontally
                                    $cell->setValignment('left');
                                });
                                $index = 4;
                                $this->log->info('permit details:'.$permitsLists);
                                //dd($updatedPermitSucess);
                                // foreach ($permitsLists as $key => $permitsList) {
                                foreach ($updatedPermits as $key => $updatedPermit) {

                                    $data[$i]['Sr No.'] = $i;
                                    $data[$i]['Permit number'] = $updatedPermit->account_number;
                                    $data[$i]['Name'] = $updatedPermit->name;
                                    $data[$i]['Email'] = $updatedPermit->email;
                                    $data[$i]['Phone'] = $updatedPermit->phone;
                                    $data[$i]['Status'] = 'Pending';
                                    $data[$i]['End Date'] = $updatedPermit->desired_end_date;
                                    
                                    $i++;

                                    //$dataArr[] = $data;
                                    $row = $key + $index;
                                    $sheet->cell('A' . $row . ':G' . $row, function ($cell) use ($color) {
                                        $cell->setAlignment('left'); // Center horizontally
                                        $cell->setValignment('left');
                                    });
                                }
                                //dd($data);
                                //dd($updatedPermitSucess);
                                foreach ($updatedPermitSucess as $key => $updatedPermitSuces) {

                                    $data1[$j]['Sr No.'] = $j;
                                    $data1[$j]['Permit number'] = $updatedPermitSuces->account_number;
                                    $data1[$j]['Name'] = $updatedPermitSuces->name;
                                    $data1[$j]['Email'] = $updatedPermitSuces->email;
                                    $data1[$j]['Phone'] = $updatedPermitSuces->phone;
                                    $data1[$j]['Status'] = 'Success';
                                    $data1[$j]['End Date'] = $updatedPermitSuces->desired_end_date;                                    
                                    $j++;
                                    /*
                                //    $dataArr[] = $data1;
                                    $row = $key + $index;
                                    
                                    $sheet->cell('A' . $row . ':G' . $row, function ($cell) use ($color) {
                                        $cell->setAlignment('left'); // Center horizontally
                                        $cell->setValignment('left');
                                    });
                                    */
                                }
                                //dd($data, $data1);
                                //dd($dataArr);
                                $k = 1;
                                $data_final = [];
                                $dataArr = array_merge($data, $data1);
                                if($dataArr){
                                    foreach ($dataArr as $key => $val) {
                                        $data_final[$k]['Sr No.'] = $k;
                                        $data_final[$k]['Permit number'] = $val['Permit number'];
                                        $data_final[$k]['Name'] = $val['Name'];
                                        $data_final[$k]['Email'] = $val['Email'];
                                        $data_final[$k]['Phone'] = $val['Phone'];
                                        $data_final[$k]['Status'] = $val['Status'];
                                        $data_final[$k]['End Date'] = $val['End Date'];                                        
                                        $k++;
                                        $row = $key + $index;
                                    
                                        $sheet->cell('A' . $row . ':G' . $row, function ($cell) use ($color) {
                                            $cell->setAlignment('left'); // Center horizontally
                                            $cell->setValignment('left');
                                        });
                                    }
                                }
                               // dd($dataArr,$data_final);
                                $sheet->fromArray($data_final, [], 'A3', true, true);
                            });
                        }
                    )->store('xls');
                
                    $data['report_name']    = 'permit_not_renew';
                    
                    storage_path('exports/' . $excelSheetName . '.xls');
                    $this->log->info("outside Excel sheet name : {{$excelSheetName}}");

                    Mail::send('yenkee.permit-renew-notify', ['data' => $facility, 'brand_setting' => $brand_setting, 'details' => $data], function ($message)  use ($excelSheetName,$partnerEmail) {
                        // $message->to($partnerEmail['email']); // Send email to partner
                        $message->to(config('parkengage.Yankees.PERMIT_PAYMENT_DUE_EMAILS'));
                        $message->subject('Auto Pay Status Report');
                        $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                        $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                        $this->log->info("Mail Sent success with failname :  {$path_to_file}");
                        if (file_exists($path_to_file)) {
                            // $this->log->info("Path Of file and name 333 {$path_to_file}");
                            $message->attach($path_to_file);
                        }
                    });
                }
            }
        } catch (\Throwable $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }
}
