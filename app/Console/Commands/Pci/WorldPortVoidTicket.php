<?php

namespace App\Console\Commands\Pci;

use Mail;

use Illuminate\Console\Command;
use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\TicketExtend;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Exceptions\NotFoundException;
use App\Models\User;
/**
 * Worldport Void ticket change
 */
class WorldPortVoidTicket extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "worldport:void-ticket {partnerId?}";
 
    /**
     * The console command description.
     *
     * @var string
     */

	const PARTNER_ID = '2980';
	// const PARTNER_ID = '19349';
	// const PARTNER_ID = [358642,363361]; //stagging
	// const FACILITY_ID = [311,378,339,397,424]; //stagging
    // partner id for usm 19349 in preprod
    
	//const FACILITY_ID = [125,142,156,153];
	const FACILITY_ID = [125,142,156];
    //const FACILITY_ID = [156];
	const IS_GATED_FACILITY = '0';
	const POLICY_ID = '15';
	const PAID_TYPE = '0';
	const AFFILIATE_BUSINESS_ID = '2';
	
	protected $currentTime;
	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/pci/')->createLogger('worldport-void-ticket');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // get 20 days older date from current date
        $checkin_date = date('Y-m-d', strtotime('-20 days'));
	 // $checkin_date = date('Y-m-d');
		//dd($checkin_date);
        $partnerIdArg = $this->argument('partnerId');
        
        if($partnerIdArg){
            $facilities = Facility::where('owner_id',$partnerIdArg)
            ->where('active',1)
            ->get();
            foreach ($facilities as $facility) {
                $facilityIds[] = $facility->id;
            }
            $partnerId = $this->argument('partnerId');
        }else{
            $facilityIds = self::FACILITY_ID;
            $partnerId = self::PARTNER_ID;
        }
		$facilityIds = self::FACILITY_ID;
        
        $brand_setting = BrandSetting::where('user_id', $partnerId)->first(); 
        $facility_brand_setting = FacilityBrandSetting::whereIn('facility_id', $facilityIds)->first();
        $facility = Facility::where('owner_id',$partnerIdArg)->where('active',1)->first();
        if($facility_brand_setting){
            $facility->facility_logo_id = $facility_brand_setting->id;
            $this->log->info("facility_logo_id:" .$facility->facility_logo_id);
        }else{
            $brand_setting = BrandSetting::where('user_id', $partnerId)->first(); 
            $facility->logo_id  = $brand_setting->id;
            $this->log->info("Partner logo id:" .$facility->log_id);
        }
        $this->log->info("1111");
		
        $ticket = Ticket::with(['facility','user'])->whereIn('facility_id', $facilityIds)
                        ->where('is_checkin','1')
                        ->where('is_checkout','0')
                        ->where('partner_id',$partnerId)
                        ->whereDate('checkin_time', '<', $checkin_date)
                        ->where('paid_type','9')
                        ->whereNull('checkout_time')
                        ->whereNull('deleted_at')
                        ->whereNull('promocode')
					    ->whereNull('anet_transaction_id')
						->orderby('id', 'desc')
                        ->get();
						//->toSql();
                        $this->log->info("1111--". count($ticket));
                        //whereIn('facility_id', $facilityIds)->
						// ->paginate(8000)
				//dd($ticket,$facilityIds,$partnerId,$checkin_date);		
			// ->whereIn('facility_id', $facilityIds)
            $third_party_ticket = Ticket::where('is_checkin',"0")->where('is_checkout',"0")
						->where('partner_id',$partnerId)->whereDate('checkin_time', '<', $checkin_date)->whereIn('facility_id', $facilityIds)->whereNull('deleted_at')->orderBy('id', 'desc')->where('is_closed',0)->whereNull('checkout_time')->paginate(5000);					
                            $this->log->info("2222--". count($third_party_ticket));
			/* usm code
			$third_party_ticket = Ticket::where('is_checkin',"0")->where('is_checkout',"0")
						->where('partner_id',$partnerId)->whereDate('checkin_time', '<', $checkin_date)->whereNull('deleted_at')->orderBy('id', 'desc')->where('is_closed',0)->paginate(5000);					
                            $this->log->info("2222--". count($third_party_ticket));	
			*/
        //dd(count($third_party_ticket),count($ticket));   
		
		if(count($ticket)>0){
            foreach($ticket as $key=>$val){
				
				$this->setCustomTimezone($val->facility_id);
                $msg = 'Checked out on '. date('d') .'st '.date('M').' by the scheduled process';
				
                //  $calculatedPayableAmount = $this->calculatePayablePrice($val);
                
				if ($val->is_checkout == '0' && $val->checkout_time == '') {
					//$payable_amount = $calculatedPayableAmount['amount'];
                    $payable_amount = '0.00';
				} else {
					$payable_amount = '0.00';
				}	
                
				$current_time               = date('Y-m-d H:i:s');
				// $val->payable_amount        = $payable_amount;
				$val->checkout_time         = $current_time;
				// $val->paid_by               =  $partnerId;
				$val->total                 =  $payable_amount;
				// $val->paid_amount           = $payable_amount;
				// $val->paid_type             = self::PAID_TYPE;
				// $val->paid_remark           = 'Ticket closed by scheduled process';
				// $val->paid_date             =  $current_time;
				// $val->policy_id             = self::POLICY_ID;
				$val->checkout_remark       = $msg;
				$val->grand_total           = '0.00';
				// $val->affiliate_business_id = self::AFFILIATE_BUSINESS_ID;
				$val->is_checkout           = '1'; 
                $val->length                =  '0';
				$val->save();
                
            }
			
            // For Excel Prepration Ticket
            $this->log->info('Under if condition');
			
            $color = $brand_setting->color;
            $this->log->info('brand Setting color: ' . $color);
            
            //$excelSheetName = 'Worldport - Catalina Closed Ticket List -' . date('Y-m-d');
            $excelSheetName = 'Closed Ticket List -' . date('Y-m-d');
            $this->log->info('Excel sheet name: ' . $excelSheetName);

            $data = [];
			$partnerEmail = User::where('id',$partnerId)->first();
			
            Excel::create(
                $excelSheetName,
                function ($excel) use (
                    $color,
                    $ticket,
                    $facility
                ) {
                    $excel->sheet('Closed Tickets', function ($sheet) use (
                        $color,
                        $ticket,
                        $facility
                    ) {
                        $sheet->setWidth(array(
                            'A'     => 15,
                            'B'     =>  35,
                            'C'     =>  35,
                            'D'     =>  35,
                            'E'     =>  35,
                            'F'     =>  35,
                            'G'     =>  25
                        ));

                        $this->log->info('cityparking 99 .');

                        $sheet->getColumnDimension('A')->setWidth(15);
                        $sheet->getColumnDimension('B')->setWidth(36);
                        $sheet->getColumnDimension('C')->setWidth(36);
                        $sheet->getColumnDimension('D')->setWidth(30);
                        $sheet->getColumnDimension('E')->setWidth(30);
                        $sheet->getColumnDimension('F')->setWidth(30);
                        $sheet->getColumnDimension('G')->setWidth(25);

                        //end width for colom here
                        //set header for excel work
                        $sheet->mergeCells('A1:G1');
                        //$sheet->mergeCells('F1:J1');
                        $sheet->getRowDimension(1)->setRowHeight(60);
                        //$sheet->setCellValue('A1', 'Worldport - Catalina Closed Ticket List');
                        $sheet->setCellValue('A1', 'Closed Ticket List');
                        $sheet->cell('A1:G1', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('20');
                        });

                        $cellValue = "Print Date - \n" .  date('Y-m-d h:i');
                        $sheet->mergeCells('A2:B2');
                        $sheet->setCellValue('A2', $cellValue);
                        $sheet->getStyle('A2:B2')->getAlignment()->setWrapText(true);
                        // Set the height of cell H2 (adjust as needed)
                        $sheet->getRowDimension(2)->setRowHeight(80);
                        $sheet->getRowDimension(3)->setRowHeight(50);

                        $sheet->cell('A2:B2', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D6DCE4');
                            $cell->setFontColor('#000000');
                            $cell->setFontSize('16');
                        });

                        $sheet->getStyle('C2:D2')->getAlignment()->setWrapText(true);

                        $sheet->cell('C2:D2', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#ffffff');
                            $cell->setFontColor('#000000');
                            $cell->setFontSize('18');
                        });

                        $sheet->mergeCells('E2:G2');
                        $sheet->getStyle('E2:F2')->getAlignment()->setWrapText(true);

                        $sheet->cell('E2:G2', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D6DCE4');
                            $cell->setFontColor('#040D12');
                            $cell->setFontSize('18');
                        });

                        $sheet->cell('A3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        $sheet->cell('B3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        $sheet->cell('C3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        $sheet->cell('D3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        $sheet->cell('E3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        $sheet->cell('F3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        $sheet->cell('G3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('18');
                        });
                        
                        $i = 1;
                        $sheet->cell('A3:I3', function ($cell) use ($color) {
                            $cell->setAlignment('left'); // Center horizontally
                            $cell->setValignment('left');
                        });
                        $index = 4;
                        $this->log->info('Ticket Details:'.$ticket);

                        foreach ($ticket as $key => $valList) {
							//dd($valList->user->name,$valList->user->email);
                            $data['Sr No.'] = $i;
                            $data['Facility Name'] = $valList->facility->full_name;
                            $data['Ticket Number'] = $valList->ticket_number;
                            $data['Check-in Time'] = date('m/d/Y H:i:s',strtotime($valList->checkin_time));
                            $data['Name'] = isset($valList->user->name) ? $valList->user->name:'';
                            $data['Email'] = isset($valList->user->email) ? $valList->user->email:'';
                            $data['Phone'] = isset($valList->user->phone) ? $valList->user->phone: '';
                            
                            $i++;

                            $dataArr[] = $data;
                            $row = $key + $index;
                            $sheet->cell('A' . $row . ':G' . $row, function ($cell) use ($color) {
                                $cell->setAlignment('left'); // Center horizontally
                                $cell->setValignment('left');
                            });
                        }
                        $sheet->fromArray($dataArr, [], 'A3', true, true);
                    });
                }
            )->store('xls');

            $data['themeColor']     = $color;
            $data['report_name']    = 'ticket_close_list';
            
            storage_path('exports/' . $excelSheetName . '.xls');
            $this->log->info("outside Excel sheet name : {{$excelSheetName}}");
            $email_msg = 'Closed Tickets List - 1st '.date('M');    
            Mail::send('kstreet.ticket-close-notify', ['data' => $facility, 'brand_setting' => $brand_setting, 'details' => $data], function ($message)  use ($excelSheetName,$partnerEmail,$email_msg) {
                // $message->to('<EMAIL>'); // test email
                // $message->to($partnerEmail['email']); // Send email to partner
                $message->to(config('parkengage.Yankees.TICKET_CLOSE_LIST_EMAILS'));
                $message->subject($email_msg);
                $message->from(config('parkengage.default_sender_email'), config('parkengage.Yankees.NOTIFICATIONS'));
                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                $this->log->info("Mail Sent success with failname :  {$path_to_file}");
                if (file_exists($path_to_file)) {
                    // $this->log->info("Path Of file and name 333 {$path_to_file}");
                    $message->attach($path_to_file);
                }
            });
			
        }
		
		
		// third Party tickets 
		if($third_party_ticket){
            foreach($third_party_ticket as $ke=>$va){
                 // dd($va->ticket_number);
				 // $this->setCustomTimezone($va->facility_id);
                  $current_time              = date('Y-m-d H:i:s');
                  $va->checkout_time         = $current_time;
                  $val->total                = '0.00';
                  $val->grand_total          = '0.00';
                //   $va->paid_by               =  self::PARTNER_ID;
                //  $va->paid_by               =  $partnerId;
                //  $va->paid_amount           = $va->grand_total;
                //  $va->paid_type             = self::PAID_TYPE;
                //  $va->paid_remark           = 'Ticket voided and closed by scheduled process';
                //  $va->paid_date             =  $current_time;
                //  $va->policy_id             = self::POLICY_ID;
                //  $va->checkout_remark       = $msg;
                  $va->checkout_remark       = 'Ticket closed by scheduled process';
                //  $va->affiliate_business_id = self::AFFILIATE_BUSINESS_ID;
				  $va->is_closed             =  '1';
                  $va->length                =  '0';
				  $va->save();
				 
            }
        }
		
		return 1;
    }
	
	public function setCustomTimezone($facility_id)
	{
	  	$facility = Facility::find($facility_id);
	  	$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
	  	$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
	  	if (($facility) && ($facility->timezone != '')) {
			date_default_timezone_set($facility->timezone);
			$this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
	  	} else {
		if ($partnerTimezone) {
		  	if ($partnerTimezone->timezone != '') {
				date_default_timezone_set($partnerTimezone->timezone);
				$this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
		  	}
		}
	  }
	}

	protected function calculatePayablePrice($val)
    {
		$isMember = 0;
		$tickeAmount = [];
        $tickeAmount['amount'] = $tickeAmount['length'] = 0;
        
        $this->setCustomTimezone($val->facility_id);
        $ticketObj = Ticket::find($val->id);
        
		if ($this->checkEventCaseBeforePayableCalculate($ticketObj)) {
            return $tickeAmount;
        }

        if (!empty($ticketObj->payment_date)) {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->payment_date);
            $rateDiffInHour = $val->getCheckOutCurrentTime(true, $ticketObj->payment_date);
        } else {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true);
        }


        if ($ticketObj->facility->rate_duration_in_hours > 0 && $ticketObj->facility->rate_per_hour > 0 && $ticketObj->facility->rate_free_minutes > 0 && $ticketObj->facility->rate_daily_max_amount > 0) {
            if (!empty($ticketObj->payment_date)) {
                $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, true, null, false, false, '0', $isMember);
            } else {
                $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            }
            if ($ticketObj->is_checkin == '1' && $ticketObj->is_checkout == '0' && $ticketObj->checkout_time != '') {
                //zeag ticket
                $rate = [];
                $rate['price'] = $ticketObj->parking_amount;
            }
        } else {
            if (!empty($ticketObj->payment_date)) {
                $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true,  false, true, false, 0, $isMember);
            } else {
                $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
            }
        }
        $taxFee = $ticketObj->facility->getTaxRate($rate);
        if (!empty($ticketObj->payment_date)) {
            $processingFee = '0.00';
        } else {
            $processingFee = $ticketObj->facility->getProcessingFee(0);
        }
        $parkingAmount = $rate['price'];
        $tickeAmount['amount'] = $parkingAmount > 0 ? $parkingAmount + $taxFee + $processingFee : '0.00';
        $tickeAmount['length'] = $rateDiffInHour;
        // dd($tickeAmount);
        return $tickeAmount;
    }

	// Vijay : 26-04-2024
    protected function checkEventCaseBeforePayableCalculate($ticket)
    {
        // Case 1 : estimated checkout time > current time then user in in event
        // Case 2 : estimated checkout time < current time event close but user not exist or checked out (Still in Garage)
        // White listed uses but no event is is present + case 1 
        // White listed uses but no event is is present + case 2 
        if(empty($ticket->estimated_checkout)){
            return false;
        }
        $eventCheckoutimeStatus = Carbon::parse($ticket->estimated_checkout)->gt($this->currentTime);
        if (!empty($ticket->event_id) && $eventCheckoutimeStatus) {
            return true;
        } else if (!empty($ticket->event_id) && $eventCheckoutimeStatus == false) {
            return false;
        } else if (empty($ticket->event_id) && $eventCheckoutimeStatus) {
            return true;
        } else if (empty($ticket->event_id) && $eventCheckoutimeStatus == false) {
            return false;
        }
    }
	
}
