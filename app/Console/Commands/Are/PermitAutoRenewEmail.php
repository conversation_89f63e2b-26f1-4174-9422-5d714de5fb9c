<?php

namespace App\Console\Commands\Are;

use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\PermitRate;
use App\Classes\DatacapPaymentGateway;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Http\Helpers\QueryBuilder;


/**
 * Emails reservation stub to user
 */
class PermitAutoRenewEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'diamond:permit-renew';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';
    protected $log;

    const PARTNER_ID = '3156';
    const FACILITY_ID = '126';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are/')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {   
        try {
            QueryBuilder::setCustomTimezone(self::FACILITY_ID);

            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));

            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);

            $monthlyRequest = PermitRequest::with(['facility.FacilityPaymentDetails', 'user', 'PermitVehicle'])
                ->where('partner_id', self::PARTNER_ID)->where('facility_id', self::FACILITY_ID)
                ->whereDate('desired_end_date', '<=', $permit_end_date)
                ->whereDate('desired_start_date', '>=', $permit_start_date)
                ->whereNull('cancelled_at')->whereNull('deleted_at')->where('user_consent', 1)
                ->whereNotNull('anet_transaction_id')
				->whereIn('account_number', [********,********,********,********,********,********])
               // ->whereNotIn('account_number', [********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********])
				->limit(100)->get();

            //dd($monthlyRequest->count(),$monthlyRequest[0]->account_number, $permit_start_date, $permit_end_date,$desired_start_date,$desired_end_date);

            if (!$monthlyRequest) {
                //  throw new NotFoundException('Monthly Request Not Found.');
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));

            $count = 0;
            foreach ($monthlyRequest as $key => $val) {
                #PIMS-11336 DD
                if($val->user->payment_mode=='0'){
                    continue;
                }
                $permitRate = PermitRate::where('id', $val->permit_rate_id)->first();
                $this->log->info("Permit Rate Found" . json_encode($permitRate));
                if ($permitRate->rate == '0.00') {  //dd('stop free permit');
                    $this->createPermitRequestHistoryNew($val);
                    $maxDays = date('t');
                    $val->no_of_days          = $maxDays;
                    $val->permit_rate         = $permitRate->rate;
                    $val->desired_start_date  = $desired_start_date;
                    $val->desired_end_date    = $desired_end_date;
                    $val->transaction_retry   = ++$val->transaction_retry;
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                    if ($facility_brand_setting) {
                        $val->facility_logo_id = $facility_brand_setting->id;
                    } else {
                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                        $val->logo_id  = $brand_setting->id;
                    }
                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                    if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                        $view = 'are.permit-renew-free';
                    } else {
                        $view = 'are-diamond.permit-renew';
                    }
                    Mail::send(
                        $view, ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use ($val) {
                            $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                            //   $message->to($val->user->email)->subject("Payment made successfully against Permit #". $val->account_number);
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to " . $val->user->email);
                } else if ($permitRate->rate > "0.00") {
                    if ($permitRate) {

                        if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') { dd('stop planet');
                            $paymentProfile = PlanetPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $permitRate->rate;
                            if ($paymentProfile) {
                                $val->session_id = $paymentProfile->token;
                                $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                                $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                                if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                    $val->transaction_retry   = ++$val->transaction_retry;
                                    $val->save();
                                    $this->log->info("planet Payment Failed Response :" . json_encode($paymentByToken));
                                    // Failure mail send to user
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                        $view = 'are.permit-renew-fail';
                                    } else {
                                        $view = 'are-diamond.permit-renew-fail';
                                    }
                                    Mail::send(
                                        $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate], function ($message) use ($val) {
                                            // $message->to([$val->user->email,config('parkengage.WAILUKU.USER_1')])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                            //  $message->to([$val->user->email])->subject("Payment Failed against your Permit #". $val->account_number);
                                            $message->to([$val->user->email])->subject("Important: Action Required for Your Permit Payment");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $val->permit_rate         = $permitRate->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    //user mail
                                    //dd($paymentProfile);
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                        $view = 'are.permit-renew';
                                    } else {
                                        $view = 'are-diamond.permit-renew';
                                    }
                                    Mail::send(
                                        $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate], function ($message) use ($val) {
                                            $message->to($val->user->email)->subject("Payment made successfully against Permit #" . $val->account_number);
                                            //$message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') { dd('stop datacap');
                            $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $permitRate->rate;
                            $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $val->facility->FacilityPaymentDetails->datacap_script_url;

                            $paymentProfile = DatacapPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            //dd($amount,$ecommerce_mid,$url,$paymentProfile);
                            if ($paymentProfile) {
                                $data['Token'] = $paymentProfile->token;
                                if ($amount > 0) {
                                   // $amount = number_format($amount, 2);
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $paymentProfile->token;
                                    $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $this->log->info("Payment Request Data --" . json_encode($data) . "--" . json_encode($ecommerce_mid) . "--" . json_encode($url));
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card ." . json_encode($paymentResponse));
                                        } else {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card." . json_encode($paymentResponse));
                                        }
                                    } else if ($paymentResponse["Status"] == "Declined") {
                                        // Failure mail send to user
                                        $val->card_last_four = $paymentProfile->card_last_four;
                                        $val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            //dd($expiry,$expiry_data);
                                            $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $val->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $val->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $val->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                            $view = 'are.permit-renew-fail';
                                        } else {
                                            $view = 'are-diamond.permit-renew-fail';
                                        }
                                        Mail::send(
                                            $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate], function ($message) use ($val) {
                                                //        $message->to([$val->user->email,config('parkengage.WAILUKU.USER_1')])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                                $message->to([$val->user->email])->subject("Payment Failed against your Permit #" . $val->account_number);
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                    if ($paymentResponse['Status'] == 'Approved') {
                                        $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                        $request = new Request([
                                            'total'   => $amount,
                                            'card_last_four' => $paymentProfile->card_last_four,
                                            'expiration' => $paymentProfile->expiry
                                        ]);
                                        $this->log->info("Save Transaction Data Request --" . json_encode($request) . "--" . json_encode($val->user_id));
                                        $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $val->user_id, '');
                                        $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));
                                        $maxDays = date('t');
                                        $val->no_of_days          = $maxDays;
                                        $val->anet_transaction_id = $authorized_anet_transaction->id;
                                        $val->permit_rate         = $permitRate->rate;
                                        $val->desired_start_date  = $desired_start_date;
                                        $val->desired_end_date    = $desired_end_date;
                                        $val->save();
                                        //user mail
                                        //dd($paymentProfile);
                                        $val->card_last_four = $paymentProfile->card_last_four;
                                        $val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            //dd($expiry,$expiry_data);
                                            $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $val->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $val->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $val->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                            $view = 'are.permit-renew';
                                        } else {
                                            $view = 'are-diamond.permit-renew';
                                        }
                                        Mail::send(
                                            $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate], function ($message) use ($val) {
                                                $message->to($val->user->email)->subject("Payment made successfully against Permit #" . $val->account_number);
                                                $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {

                            $paymentProfile = HeartlandPaymentProfile::where('user_id', $val->user_id)->where('is_default', 1)->first();
                            //dd($val->user_id,$val->id,$val->email,$paymentProfile);	
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $permitRate->rate;
                            //$amount = number_format($amount, 2);
                           // dd($val->user_id,$val->id,$val->email,$val->account_number,$val->permit_rate,$paymentProfile->token,$amount);
								/*
								$expiry    = $paymentProfile->expiry;
                                if($expiry){
                                    $expiry_data = str_split($expiry, 2);
                                    $paymentProfile->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                }else{
                                    $paymentProfile->card_expiry = "-";
                                }
                                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                if($facility_brand_setting){
                                    $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                }else{
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
                                    $paymentProfile->logo_id  = $brand_setting->id;
                                }
                                $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                if($val->facility_id==config('parkengage.WAILUKU_FACILITY')){
                                    $view='are.permit-renew';  
                                }else{
                                    $view='are-diamond.permit-renew';  
                                }
                                $partnerDetails = User::where('id',$val->partner_id)->first();
                                $partner_name = "Diamond Parking at (************* or email <NAME_EMAIL>";
                                $val->desired_start_date = '2024-11-01';
                                Mail::send(
                                    $view, ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], function ($message) use($val,$PermitRenewMonth,$PermitRenewYear) {
                                        //$message->to($val->user->email)->subject("Payment Successful against Permit #". $val->account_number." for the month of <".$PermitRenewMonth.", ".$PermitRenewYear.">");
                                        //  $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                        $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #". $val->account_number);
                                        $message->from(config('parkengage.default_sender_email'));
                                    }
                                );
                            //    $this->log->info("Mail sent to ". $val->user->email);
								dd('stop Mail Sent');
								*/
							if ($paymentProfile) {
                                $request = new Request([
                                    'Amount'   => $amount,
                                    'total'   => $amount,
                                    'token' => $paymentProfile->token,
                                    'zipcode' => $paymentProfile->zipcode,
                                    'card_last_four' => $paymentProfile->card_last_four,
                                    'expiration_date' => $paymentProfile->expiry,
                                ]);
                                $this->log->info("Payment Request Log Data --" . json_encode($request->all()));
                                try {
                                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility);
						
                                } catch (Exception $e) {
                                    $this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                    if (str_contains($e->getMessage(), 'duplicate')) {

                                    } else {
                                        $val->transaction_retry   = $val->transaction_retry + 1;
                                        $val->save();
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        /*
										if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                            $view = 'are.permit-renew-fail';
                                        } else {
                                            $view = 'are-diamond.permit-renew-fail';
                                        }
										
                                        if($val->transaction_retry >= '1'){
                                            $view='usm.permit-cancel-reminder';  
                                            //call skiData Api to cancel resuable ticket
                                            $subject = "Important Notice: Permit Cancellation";
                                            if ($val->skidata_id != '') {
                                                if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                                    $this->cancelSkiData($val->id);
                                                }
                                                $val->status = '0';
                                                $val->user_consent = '0';
                                                $val->cancelled_at = $permitRenewDate;
                                                $val->save();    
                                            }
                                        }else{
                                            if($val->facility_id==config('parkengage.WAILUKU_FACILITY')){
                                                $view='are.permit-renew-fail';  
                                            }else{
                                                $view='are-diamond.permit-renew-fail';  
                                            }	
                                            $subject = "Important: Action Required for Your Permit Payment";
                                        }
										*/

                                        $view = 'usm.permit-cancel-reminder';
                                        //call skiData Api to cancel resuable ticket
                                        $subject = "Important Notice: Permit Cancellation";
                                        if ($val->skidata_id != '') {
                                            if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                           //     $this->cancelSkiData($val->id);
                                            }
                                        }
                                        $this->log->info("Mail subject " . $subject);

                                      //  $subject = "Important: Action Required for Your Permit Payment";
                                        $partnerDetails = User::where('id', $val->partner_id)->first();
                                        $partner_name = "Diamond Parking at (************* or email <NAME_EMAIL>";
                                        /*
										Mail::send(
                                            $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear, $subject) {
                                                //$message->to($val->user->email)->subject("Payment Failed against Permit #". $val->account_number." for the month of <".$PermitRenewMonth.", ".$PermitRenewYear.">");
                                                //  $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #". $val->account_number);
                                                //   $message->to([$val->user->email])->subject("Important: Action Required for Your Permit Payment");
                                                $message->to([$val->user->email])->subject($subject);
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
										*/
                                    }
                                }
								
                                $this->log->info("Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));

                                if (isset($paymentResponse) && $paymentResponse->responseMessage == 'APPROVAL') {
									$this->log->info("Heartland Payment Approve Response to Permit Id #:" .  $val->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode. "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId ."-- HL Gateway Response" . json_encode($paymentResponse));
			
                                    $this->createPermitRequestHistoryNew($val);
                                    $user_id = $val->user_id;
                                    $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                                    $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $authorized_anet_transaction->id;
                                    $val->permit_rate         = $permitRate->rate;
                                    $val->permit_final_amount = $permitRate->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->transaction_retry   = $val->transaction_retry + 1;
									$val->renew_type     	  = '0';
                                    $val->save();
                                    //user mail
									
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                        $view = 'are.permit-renew';
                                    } else {
                                        $view = 'are-diamond.permit-renew';
                                    }
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = "Diamond Parking at (************* or email <NAME_EMAIL>";
                                    
									Mail::send(
                                        $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear) {
                                            //$message->to($val->user->email)->subject("Payment Successful against Permit #". $val->account_number." for the month of <".$PermitRenewMonth.", ".$PermitRenewYear.">");
                                            //  $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
									
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
									$this->log->info("Heartland Payment Failed Response to Permit Id #:" .  $val->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode. "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId ."-- HL Gateway Response" . json_encode($paymentResponse));
									/*
									
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    
									if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
										$view = 'are.permit-renew-fail';
									} else {
										$view = 'are-diamond.permit-renew-fail';
									}
									
									*/
                                    $view = 'usm.permit-cancel-reminder';
                                    //call skiData Api to cancel resuable ticket
                                    $subject = "Important Notice: Permit Cancellation";
								 
                                    if ($val->skidata_id != '') {
                                        if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                                            $this->cancelSkiData($val->id);
                                        }
                                            $val->status = '0';
                                            $val->user_consent = '0';
                                            $val->cancelled_at = $permitRenewDate;
                                            $val->save();    
                                    }
									
                                    $this->log->info("Mail subject " . $subject);
									
                                    
									//$subject = "Important: Action Required for Your Permit Payment";
									
									$facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = "Diamond Parking at (************* or email <NAME_EMAIL>";
									
                                    Mail::send(
                                        $view, ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear, $subject) {
                                            $message->to([$val->user->email])->subject($subject);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
									
                                    $this->log->info("Mail sent to " . $val->user->email);									
                                }
                            } else {
                                $this->log->info("Payment Profile Not Found" . $val->account_number);
                            }
                        }
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
        
    }

    public function createPermitRequestHistoryNew($monthlyRequest)
    {
        if ($monthlyRequest) {
            $permitRenewDate = date('Y-m-d h:i:s');
            $permitData = new PermitRequestRenewHistory();

            $permitData->permit_request_id = $monthlyRequest->id;
            $permitData->user_id = $monthlyRequest->user_id;
            $permitData->facility_id = $monthlyRequest->facility_id;
            $permitData->anet_transaction_id = $monthlyRequest->anet_transaction_id;
            $permitData->tracking_code = $monthlyRequest->tracking_code;
            $permitData->email = $monthlyRequest->email;
            $permitData->name = $monthlyRequest->name;
            $permitData->phone = $monthlyRequest->phone;
            $permitData->permit_rate = $monthlyRequest->permit_rate;
            $permitData->permit_rate_id = $monthlyRequest->permit_rate_id;
            $permitData->approved_on = $monthlyRequest->approved_on;
            $permitData->account_number = $monthlyRequest->account_number;
            $permitData->monthly_duration_value = $monthlyRequest->monthly_duration_value;
            $permitData->no_of_days = $monthlyRequest->no_of_days;
            $permitData->partner_id = $monthlyRequest->partner_id;
            $permitData->license_number = $monthlyRequest->license_number;
            $permitData->mer_reference = $monthlyRequest->mer_reference;
            $permitData->image_front = $monthlyRequest->image_front;
            $permitData->image_back = $monthlyRequest->image_back;
            $permitData->user_consent = $monthlyRequest->user_consent;
            $permitData->vehicle_id = $monthlyRequest->vehicle_id;
            $permitData->is_admin = $monthlyRequest->is_admin;
            $permitData->ex_month = $monthlyRequest->ex_month;
            $permitData->ex_year = $monthlyRequest->ex_year;
            $permitData->payment_gateway = $monthlyRequest->payment_gateway;
            $permitData->permit_type = $monthlyRequest->permit_type;
            $permitData->is_payment_authrize = $monthlyRequest->is_payment_authrize;
            $permitData->session_id = $monthlyRequest->session_id;
            $permitData->permit_type_name = $monthlyRequest->permit_type_name;
            $permitData->skidata_id = $monthlyRequest->skidata_id;
            $permitData->skidata_value = $monthlyRequest->skidata_value;
            $permitData->acknowledge = $monthlyRequest->acknowledge;
            $permitData->facility_zone_id = $monthlyRequest->facility_zone_id;
            $permitData->desired_start_date = $monthlyRequest->desired_start_date;
            $permitData->desired_end_date = $monthlyRequest->desired_end_date;
            $permitData->cancelled_at = $monthlyRequest->cancelled_at;
            $permitData->created_at = $permitRenewDate;
            $permitData->updated_at = $permitRenewDate;
            $permitData->deleted_at = $monthlyRequest->deleted_at;

            $permitData->hid_card_number = $monthlyRequest->hid_card_number;
            $permitData->account_name = $monthlyRequest->account_name;
            $permitData->permit_final_amount = $monthlyRequest->permit_final_amount;
            $permitData->user_remark = $monthlyRequest->user_remark;
            $permitData->user_type_id = $monthlyRequest->user_type_id;
            $permitData->is_antipass_enabled = $monthlyRequest->is_antipass_enabled;
            $permitData->admin_user_id = $monthlyRequest->admin_user_id;
            $permitData->discount_amount = $monthlyRequest->discount_amount;
            $permitData->promocode = $monthlyRequest->promocode;
            $permitData->negotiated_amount      = $monthlyRequest->negotiated_amount;
            $permitData->processing_fee         = $monthlyRequest->processing_fee;
            $permitData->refund_amount          = $monthlyRequest->refund_amount;
            $permitData->refund_type            = $monthlyRequest->refund_type;
            $permitData->refund_remarks         = $monthlyRequest->refund_remarks;
            $permitData->refund_date            = $monthlyRequest->refund_date;
            $permitData->refund_by              = $monthlyRequest->refund_by;
            $permitData->refund_transaction_id  = $monthlyRequest->refund_transaction_id;
            $permitData->refund_status          = $monthlyRequest->refund_status;

            $permitData->save();
                       
        }
        return true;
    }

    public function cancelSkiData($permit_id)
    {
       dd('stop');
        try {

            $this->log->info("Get Permit Request id for cancel SkiData Autorenew" . json_encode($permit_id));
            $permit = PermitRequest::with(['facility'])->where("id", $permit_id)->first();
            //$guarage_code = $permit->facility->garage_code;
            $guarage_code = config('parkengage.SKIDATA_GUARAGE');
            $skidata_id =  $permit->skidata_id;
            $skiDataAuth = config('parkengage.SKIDATA_USERNAME') . ":" . config('parkengage.SKIDATA_PASSWORD');
            $dataAuthEncrption = base64_encode($skiDataAuth);
            $headers = [
                'Authorization: Basic ' . $dataAuthEncrption,
                'Content-Type: application/json',
            ];
            $curl = curl_init();
            $url = config('parkengage.SKIDATA_CREATE_URL') . $guarage_code . '/' . $skidata_id;
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $response = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($response);
            $this->log->info("SkIData success completed.");
            return $response;
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
        }
    }

}
