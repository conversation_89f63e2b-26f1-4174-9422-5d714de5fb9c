<?php

namespace App\Console\Commands\SweepReport;

use App\Classes\CommonFunctions;
use App\Jobs\SweepReport\SweepReport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\FacilityPaymentDetail;
use Illuminate\Support\Facades\Mail;
use App\Models\OauthClient;
use Carbon\Carbon;

/**
 * Emails reservation stub to user
 */
class PartnerWiseSweepReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // protected $signature = 'sweep-report:partner-wise {--requestData= : Array-like input, e.g. [] or ["key":"value"]}';
    protected $signature = 'sweep-report:partner-wise {--requestData=}';


    /**
     * The console command description.
     *
     * @var string
     */


    const QUEUE_NAME_SWEEP_REPORT = 'partner-wise-sweep-report';
    protected $log;
    protected $requestData;
    protected $carbon;


    public function __construct()
    {
        parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/sweep-report')->createLogger('sweep-report');
        $this->carbon = new Carbon();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $current_time = date('Y-m-d H:i:s');
        $this->log->info("Sweep report start at " . $current_time);



        // validate data
        if (!$this->requestData = $this->validateRequest()) {
            $this->log->error("Invalid Request");
            return false;
        }

        dispatch((new SweepReport($this->requestData))->onQueue(self::QUEUE_NAME_SWEEP_REPORT));

        $this->log->info("Sweep report end");


       return 'Job runned success';


        // // Get Accounts:
        // $accounts = $this->getAccountNames();
        // $this->log->info("Accounts Details: " . json_encode($accounts));

        // // Get Baches
        // $batchesData = $this->getBatches($accounts);
        // $this->log->info("Baches Data: " . json_encode($accounts));


        // // Generate Excel and Send Mail
        // $filePath = $this->generateExcel($batchesData);
        // $this->log->info("File Path: " . json_encode($filePath));


    }

    private function validateRequest()
    {
        $requestDataRaw = $this->option('requestData');

        // Try to decode the JSON
        $requestData = json_decode($requestDataRaw, true);

        // Check for JSON decoding errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log->warning("Invalid JSON passed to --requestData: " . json_last_error_msg());
            return false;
        }

        // Optional debug output:
        // dd($requestDataRaw, $requestData);

        // Ensure it's an array
        if (!is_array($requestData) || empty($requestData)) {
            $this->log->warning("Decoded requestData is empty or not an array.");
            return false;
        }

        // Everything is good — return the decoded array
        return $requestData;
    }

    // private function getAccountNames()
    // {
    //     return FacilityPaymentDetail::select('heartland_website_id')
    //         ->whereIn('facility_id', $this->requestData['facility_ids'])->get();
    // }

    // private function getBatches($accounts)
    // {
    //     $this->log->info("getBatches Start " . json_encode($accounts));

    //     $batchesData = [];

    //     $beginDate         = $this->requestData['beginDate'];
    //     $endDate           = $this->requestData['endDate'];
    //     $beginRecordOffset = $this->requestData['beginRecordOffset'];
    //     $limit             = $this->requestData['limit'];

    //     foreach ($accounts as $key => $account) {
    //         $url = "https://reportsapi.propay.com/v1/accounts/{$account->heartland_website_id}/transfers"
    //             . "?beginDate={$beginDate}&endDate={$endDate}&beginRecordOffset={$beginRecordOffset}&limit={$limit}";

    //         $method = "GET";
    //         $headers = [
    //             'Authorization: Basic MDZjZmZlZDg1Zjc0ZTQwOGE3YjM4NDIwZWZkMjY5OmU0MDhhN2Iz',
    //             'Content-Type: application/json'
    //         ];


    //         $response = CommonFunctions::makeRequest($url, $method, $headers);


    //         $this->log->info("Response data: " . json_encode($response) . "getBatches for " . ++$key);


    //         // Check if the response is valid and an array before merging
    //         if (is_array($response)) {
    //             $bachIdData = $this->getBachIdData($response, $account);
    //             $batchesData = array_merge($batchesData, $bachIdData);
    //         } else {
    //             $this->log->warning('No record found');
    //         }

    //         // Optional: If you want to send mail per account
    //         // $this->sendMail($response);
    //     }

    //     // Optional: Send combined mail
    //     // $this->sendMail($batchesData);
    //     $this->log->info("getBatches End");
    //     return $batchesData;
    // }

    // private function getBachIdData($batchesData, $account)
    // {
    //     $this->log->info("getBachIdData Start for batchIdData: " . json_encode($batchesData) . " and Account for: " . $account->heartland_website_id);

    //     $tranctionsData = [];

    //     // dd($batchesData['transferResults']);

    //     $beginDate         = $this->requestData['beginDate'];
    //     $endDate           = $this->requestData['endDate'];
    //     $beginRecordOffset = $this->requestData['beginRecordOffset'];
    //     $limit             = $this->requestData['limit'];

    //     foreach ($batchesData['transferResults'] as $transfer) {

    //         $url = "https://reportsapi.propay.com/v1/accounts/$account->heartland_website_id/transfers/" . $transfer['batchId'] . "?beginRecordOffset=0&limit=10";

    //         $method = "GET";
    //         $headers = [
    //             'Authorization: Basic MDZjZmZlZDg1Zjc0ZTQwOGE3YjM4NDIwZWZkMjY5OmU0MDhhN2Iz',
    //             'Content-Type: application/json'
    //         ];

    //         // $this->log->info("Batch data Url: " . $url);


    //         $response = CommonFunctions::makeRequest($url, $method, $headers);
    //         // $this->log->info("Batch data response: " . json_encode($response));


    //         // Check if the response is valid and an array before merging
    //         // dd(is_array($response));
    //         if (is_array($response)) {
    //             $tranctionsData = array_merge($tranctionsData, $response['transferDetailResults']);
    //         }
    //     }
    //     $this->log->info("Final Batch data response: " . json_encode($tranctionsData));

    //     $this->log->info("getBachIdData End");

    //     return $tranctionsData;
    // }

    // private function generateExcel($getBachIdData)
    // {
    //     // dd($getBachIdData);
    //     // Generate file name and path
    //     $fileName = 'partner_wise_sweep_report_' . $this->carbon->now()->format('Y-m-d');

    //     // Create Excel file and store it temporarily
    //     $excel = Excel::create(
    //         $fileName,
    //         function ($excel) use ($getBachIdData) {
    //             $excel->sheet('Sweep report patner wise', function ($sheet) use ($getBachIdData) {
    //                 $this->log->info('Excel Data :' . json_encode($getBachIdData));

    //                 foreach ($getBachIdData as $key => $valList) {
    //                     $data['Sr No.']                     = ++$key;
    //                     $data['gatewayTransactionId']       = $valList['gatewayTransactionId'] ?? '-';
    //                     $data['transactionDate']            = isset($valList['transactionDate']) ? $this->utcToNewYork($valList['transactionDate']) : '-';
    //                     $data['fundDate']                   = isset($valList['fundDate']) ? $this->utcToNewYork($valList['fundDate']) : '-';
    //                     $data['transNum']                   = $valList['transNum']  ?? '-';
    //                     $data['transType']                  = $valList['transType']  ?? '-';
    //                     $data['payerName']                  = $valList['payerName']  ?? '-';
    //                     $data['authorizationAmount']        = $valList['authorizationAmount']  ?? '-';
    //                     $data['authorizationCurrency']      = $valList['authorizationCurrency']  ?? '-';
    //                     $data['grossAmount']                = $valList['grossAmount']  ?? '-';
    //                     $data['discFee']                    = $valList['discFee']  ?? '-';
    //                     $data['perTransFee']                = $valList['perTransFee']  ?? '-';
    //                     $data['credit']                     = $valList['credit']  ?? '-';
    //                     $data['totalFee']                   = $valList['totalFee']  ?? '-';
    //                     $data['netAmount']                  = $valList['netAmount']  ?? '-';
    //                     $data['settlementCurrency']         = $valList['settlementCurrency']  ?? '-';
    //                     $data['comment1']                   = $valList['comment1']  ?? '-';
    //                     $data['comment2']                   = $valList['comment2']  ?? '-';

    //                     $dataArr[] = $data;
    //                 }
    //                 // dd($dataArr);
    //                 $sheet->fromArray($dataArr, [], 'A1', true, true);
    //             });
    //         }
    //     );

    //     $excel->store('xls', storage_path('exports'));

    //     // Build the full path
    //     $filePath = storage_path("exports/{$fileName}.xls");

    //     // $this->sendMail([], $fileName, $filePath);

    //     return $this->sendMail($getBachIdData, $fileName, $filePath);
    // }

    // public function utcToNewYork($dateTime)
    // {
    //     if (empty($dateTime)) {
    //         return null;
    //     }

    //     // Ensure the input is clean and validate the format
    //     $dateTime = trim($dateTime);
    //     return date('Y-m-d H:i:s', strtotime($dateTime));
    // }

    // private function sendMail($data, $fileName, $filePath)
    // {
    //     $this->log->info("Send mail start");

    //     $excelData = [
    //         'data' => $data,
    //         'timestamp' => $this->carbon->now()->toDateTimeString(),
    //     ];

    //     $recipients = config("parkengage.notify.profac");

    //     // dd($fileName, $filePath, $recipients, count($excelData), config('parkengage.default_sender_email'));


    //     try {
    //         Mail::send('sweep-report.sweep-report', $excelData, function ($message) use ($recipients, $filePath, $fileName) {
    //             // $message->to("<EMAIL>")
    //             $message->to($recipients)
    //                 ->subject("Sweep Report")
    //                 ->from(config('parkengage.default_sender_email'))
    //                 ->attach($filePath);
    //         });

    //         $this->log->info("Notifications sent with Excel attachment: $fileName");
    //     } catch (\Exception $e) {
    //         $this->log->error("Failed to send email with Excel: {$e->getMessage()}");
    //     }
    // }


    // private function setCustomTimezone($facility)
    // {
    //     // $facility = Facility::whereIn('id', [);
    //     if ($facility && !empty($facility->timezone)) {
    //         date_default_timezone_set($facility->timezone);
    //         return;
    //     }

    //     $partnerTimezone = OauthClient::where('partner_id', $facility->owner_id)
    //         ->with('userPaymentGatewayDetail')
    //         ->first()
    //         ->userPaymentGatewayDetail ?? null;

    //     if ($partnerTimezone && !empty($partnerTimezone->timezone)) {
    //         date_default_timezone_set($partnerTimezone->timezone);
    //     }
    // }
}
