<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\ParkEngage\ParkingDeviceKeepAlive;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;


class NotifyKeepAlive extends Command
{

    protected $signature = 'notify:keep-alive';

    protected $description = 'Send notification of serial number.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/notify-keep-alive')->createLogger('notify-keep-alive');
    }


    public function handle()
    {
        try{
        $minutes = 15;
       //$serial_number = $this->argument('serial_number');
       $this->log->info("Notify keep alive cron start");
        $parkingDevices = ParkingDevice::with(["facility", "gate"])->where('is_active', '1')->get();
       foreach($parkingDevices as $parkingDevice){
        $this->setCustomTimezone($parkingDevice->facility_id);
        $keepAliveDevice = ParkingDeviceKeepAlive::where("serial_number", $parkingDevice->serial_number)->orderBy('id', "ASC")->first();
        if($keepAliveDevice){
            $lastUpdated = Carbon::createFromFormat('Y-m-d H:i:s', $keepAliveDevice->updated_at);
            $currentTime = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
            $diff_in_mins = $lastUpdated->diffInRealMinutes($currentTime);    
            if($diff_in_mins <= $minutes){
                continue;
            }
            $this->log->info("4");
            /*if($keepAliveDevice->email_counter == 3){
                continue;
            }*/

            if(isset($parkingDevice->gate->gate_name)){
                $data['facility_name'] = $parkingDevice->facility->full_name;
                $data['serial_number'] = $parkingDevice->serial_number;
                $data['updated_at'] = $keepAliveDevice->updated_at;
                $data['gate_name'] = $parkingDevice->gate->gate_name;
                $data['gate_type'] = $parkingDevice->gate->gate_type;
                try{
                    Mail::send(
                        "parkengage.notify-keep-alive", ['data' => $data], function ($message) use($data) {
                            $message->to(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'])->subject("Alert! device " . $data["serial_number"] . " is not alive.");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    //$keepAliveDevice->email_counter = $keepAliveDevice->email_counter + 1;
                    //$keepAliveDevice->save();
                }catch (\Exception $e) {
                    $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                    $this->log->error($msg);
                    /*Mail::send(
                        "mapco.alert-error", ['data' => $msg], function ($message) use($msg) {
                            $message->to(['<EMAIL>','<EMAIL>'])->subject("Error : Mapco Checkin Alert ");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );*/
                    $this->log->info("error log created.");
                }
            }
            
        }        
       }       
    }catch (\Exception $e) {
        $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
        $this->log->error($msg);
        /*Mail::send(
            "mapco.alert-error", ['data' => $msg], function ($message) use($msg) {
                $message->to(['<EMAIL>','<EMAIL>'])->subject("Error : Mapco Checkin Alert ");
                $message->from(config('parkengage.default_sender_email'));
            }
        );*/
        $this->log->info("error log created.");
    }
    //$this->log->info("Email sent to ". $data->user->email .'user id : '. $data->user->id);

       $this->log->info("cron completed");
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        if($facility){
            $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
            $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
            if ($partnerTimezone) {
                if ($facility->timezone != '') {
                    config(['app.timezone' => $facility->timezone]);
                    date_default_timezone_set($facility->timezone);
                } else if ($partnerTimezone->timezone != '') {
                    config(['app.timezone' => $partnerTimezone->timezone]);
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }
}
