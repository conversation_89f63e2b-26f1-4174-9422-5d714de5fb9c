<?php

namespace App\Http\Helpers;

use App\Models\Facility;
use App\Models\OauthClient;
use Carbon\Carbon;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\OverstayTicket;
use App\Models\User;
use App\Models\ParkEngage\PartnerConfiguration;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\Ticket;
use App\Models\ParkEngage\TicketTemporaryDetail;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\Reservation;
use App\Models\AuthorizeNetTransaction;
use App\Models\AllTransactions;
use App\Models\AllFailedTransactions;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\State;
use App\Models\PromotionDay;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ApiGenericException;
use App\Models\CustomText;
use App\Models\ParkEngage\CustomerPortalPermission;
use DateTime;
use Illuminate\Support\Facades\DB;

class QueryBuilder
{
    const RESERVATION_THRESHOLD_TYPE = 2;
    const FULL_DAY_HOUR_VAL = 24;
    /**
     * Append search query terms to an existing query
     *
     * @param  QueryBuilder $query  Existing laravel eloquent query, e.g. User::query();
     * @param  string       $term   Term to search for
     * @param  string       $fields Database columns to search on
     * @return [type]         [description]
     */
    public static function buildSearchQuery($query, $term, $fields)
    {
        foreach ($fields as $index => $field) {
            if (!$index) {
                $query->where($field, 'like', "%$term%");
                continue;
            }

            $query->orWhere($field, 'like', "%$term%");
        }

        return $query;
    }

    public static function buildSearchQueryForPartner($query, $term, $fields)
    {
        $query->where(function ($myQuery) use ($fields, $term) {
            foreach ($fields as $index => $field) {
                if (!$index) {
                    $myQuery->where($field, 'like', "%$term%");
                    continue;
                }

                $myQuery->orWhere($field, 'like', "%$term%");
            }
        });

        return $query;
    }

    public static function currencyFormat($value)
    {
        return sprintf("%.2f", $value);
    }

    public static function safeMax($value)
    {
        return max(0, $value);
    }

    public static function checkEqualValue($value1, $value2, $precision = 2)
    {
        return round((float) $value1, $precision) == round((float) $value2, $precision);
    }

    public static function checkValidMobileLength($string)
    {
        if (!empty($string)) {
            if (strlen(substr($string, -10)) == 10) {
                return true;
            }
        }
        return false;
    }

    public static function appendCountryCode($phone = '')
    {
        try {
            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
            if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                $countryCode = "+91";
            } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                $countryCode = "+1";
            } else {
                $countryCode = "+1";
            }
            if (!empty($phone)) {
                return $countryCode . $phone;
            }
            return $countryCode;
        } catch (\Exception $e) {
            $countryCode = "+1";
            return $countryCode;
        }
    }

    public static function formatPhoneNumber($phoneNumber, $is_countryCode = true)
    {

        // Remove any non-digit characters
        $cleaned = preg_replace('/\D/', '', $phoneNumber);

        // Check if the number has 10 digits
        if (strlen($cleaned) === 10) {
            $formattedNumber = sprintf(
                '(%s) %s-%s',
                substr($cleaned, 0, 3),
                substr($cleaned, 3, 3),
                substr($cleaned, 6)
            );

            // If country code is required, prepend it
            if ($is_countryCode) {
                $formattedNumber = self::appendCountryCode() . ' ' . $formattedNumber;
            }

            return $formattedNumber;
        }

        // If the number doesn't have 10 digits and a country code is required, prepend the country code
        if ($is_countryCode) {
            $phoneNumber = self::appendCountryCode() . ' ' . $phoneNumber;
        }

        // Return the original number if it doesn't have 10 digits
        return $phoneNumber;
    }


    public static function getConvertedLength($length)
    {
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;

        if ($diffInMinutes > 0) {
            $diffInMints =  ($diffInMinutes * 100) / 60;
            $diffInHours = $diffInHours . '.' . $diffInMints;
        } else {
            $diffInHours = $length;
        }

        return $diffInHours;
    }

    public static function checkMints(string $mints)
    {
        if (strlen($mints) == 2) {
            $calulatedMints = (int) $mints;
        } else if (in_array($mints, ['1', '2', '3', '4', '5'])) {
            $calulatedMints = $mints * 10;
        } else {
            $calulatedMints = $mints;
        }
        return $calulatedMints;
    }

    public static function getLengthInMints(float $length)
    {
        if ($length <= 0) {
            return $length;
        }
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;
        if ($diffInMinutes > 0) {
            if ($diffInHours > 0) {
                $diffInMints =  ($diffInHours * 60) + self::checkMints($diffInMinutes);
            } else {
                $diffInMints = $length * 100;
            }
        } else {
            $diffInMints = $diffInHours * 60;
        }
        return $diffInMints;
    }

    public static function getLengthInHours($length)
    {
        $diffInHours = 0;
        if ($length >= 60) {
            $lengthInhours = ($length / 60);
            $explode = explode(".", $lengthInhours);
            $diffInHours = isset($explode[0]) ? $explode[0] : intval($lengthInhours);
            $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;

            if ($diffInMinutes > 0) {
                $getRemaingmints = $length - ($diffInHours * 60);
                $mints = $getRemaingmints / 100;
                $diffInHours = $diffInHours + $mints;
            }
            return floatval($diffInHours);
        } else {
            if ($length < 10) {
                $diffInHours =  '0.0' . $length;
            } else {
                $diffInHours =  '0.' . $length;
            }
        }
        return floatval($diffInHours);
    }

    // created to formate the reservation length in day,hours,min 13-09-2023 by
    public static function getFormatedDurationByLength($length)
    {
        // $length = $reservation->length . "";
        $length = explode('.', $length);
        $hours = $length[0];
        $minute = 0;

        if (isset($length[1])) {
            $minute = $length[1];
        }
        $days = round($hours / 24);
        $duration = '';
        $s = ($hours > 1) ? 's ' : '';
        if ($hours > 0) {
            $duration = $hours . ' Hr' . $s;
        }
        if ($hours > 24) {
            $days = floor($hours / 24);
            $hours = $hours % 24;
            if ($days == 1) {
                $duration = $days . " Day ";
            } else {
                $duration = $days . " Days ";
            }
            if ($hours > 0) {
                $duration = $duration . "" . $hours . ' Hr' . $s;
            }
        }
        if ($minute > 0) {
            $duration = $duration . $minute . " Min";
        }
        return $duration;
    }

    public static function getDeviceTypeID($deviceType)
    {
        if ($deviceType == 'android') {
            $deviceType = '6';
        } elseif ($deviceType == 'IOS') {
            $deviceType = '7';
        } else {
            $deviceType = '0';
        }
        return $deviceType;
    }

    public static function getFormatedLengthForPrice($length)
    {
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;


        if ($diffInMinutes < 60 && $diffInHours == '0') {
            // if ($diffInMinutes < 10) {
            //     $diffInHours =  '0.' . $diffInMinutes;
            // } else {
            // }
            $diffInHours =  '0.' . $diffInMinutes;
            // $diffInHours = $diffInMinutes / 100;
        } else if ($diffInHours > 0) {
            if ($diffInMinutes < 10) {
                $diffInMinutesInNew =  '0.0' . $diffInMinutes;
            } else {
                $diffInMinutesInNew =  '0.' . $diffInMinutes;
            }
            // $diffInMinutesInNew = $diffInMinutes / 100;
            $diffInHours = $diffInHours + $diffInMinutesInNew;
        }

        return $diffInHours;
    }

    public static function addMintsInCurrentDatetime($lengthInMints)
    {
        // Carbon::now()->addMinutes($lengthInMints)->format
        return  Carbon::parse('now')->addMinutes($lengthInMints)->format('Y-m-d H:i:s');
    }

    public static function addMintsInDatetime(Carbon $time, $lengthInMints, $type = 'mints')
    {
        if ($type == 'mints') {
            $checkoutTime = Carbon::parse($time)->addMinutes($lengthInMints)->format('Y-m-d H:i:s');
        } else {
            // Hours
            $lengthInMints = $lengthInMints * 60;
            $checkoutTime = Carbon::parse($time)->addMinutes($lengthInMints)->format('Y-m-d H:i:s');
        }

        return $checkoutTime;
    }

    public static function getNowTime()
    {
        return Carbon::parse('now')->format('Y-m-d H:i:s');
    }

    public static function getArrivalTime()
    {
        return Carbon::parse(self::getNowTime())->format('Y-m-d H:i:s');
    }

    public function getUngatedDinffInHours($lengthInMints = false, $overstayCheckingTime = false)
    {
        if ($lengthInMints) {
            if ($overstayCheckingTime == false) {
                $checkinTime = Carbon::parse($this->checkin_time);
            } else {
                $checkinTime = Carbon::parse($overstayCheckingTime);
            }
            $nowTime = Carbon::parse('now');
            $diffInHours = $checkinTime->diffInRealHours($nowTime);
            $diffInMinutes = $checkinTime->diffInRealMinutes($nowTime);
            $diffInSeconds = $checkinTime->diffInSeconds($nowTime);
            // dd($checkinTime, $nowTime, $diffInHours);

            // dd($diffInHours, $diffInMinutes, $diffInSeconds);
            if ($diffInMinutes < 60) {
                $diffInHours = $diffInMinutes / 100;
            }
            // dd($diffInHours);
            if ($diffInMinutes > 59) {
                if ($diffInHours > 0) {
                    $diffInMints = $diffInMinutes - ($diffInHours * 60);
                    $NewdiffInMints = $this->addZero($diffInMints);
                    $diffInHours = $diffInHours + $NewdiffInMints;
                } else {
                    $diffInHours = $this->addZero($diffInMinutes);
                }
                // dd($diffInMints, $diffInHours);
            }
            if ($diffInSeconds < 60) {
                $diffInHours = .01;
            }
            return $diffInHours;
            // return $this->getSubscriptionEndDate($this->checkin_time);
        }
        return Carbon::parse('now')->format('Y-m-d H:i:s');
    }

    public static function get_mimetype($filepath)
    {
        if (!preg_match('/\.[^\/\\\\]+$/', $filepath)) {
            return finfo_file(finfo_open(FILEINFO_MIME_TYPE), $filepath);
        }
        switch (strtolower(preg_replace('/^.*\./', '', $filepath))) {
            // START MS Office 2007 Docs
            case 'docx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            case 'docm':
                return 'application/vnd.ms-word.document.macroEnabled.12';
            case 'dotx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.template';
            case 'dotm':
                return 'application/vnd.ms-word.template.macroEnabled.12';
            case 'xlsx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            case 'xlsm':
                return 'application/vnd.ms-excel.sheet.macroEnabled.12';
            case 'xltx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.template';
            case 'xls':
                return 'application/vnd.ms-excel';
            case 'xltm':
                return 'application/vnd.ms-excel.template.macroEnabled.12';
            case 'xlsb':
                return 'application/vnd.ms-excel.sheet.binary.macroEnabled.12';
            case 'xlam':
                return 'application/vnd.ms-excel.addin.macroEnabled.12';
            case 'pptx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
            case 'pptm':
                return 'application/vnd.ms-powerpoint.presentation.macroEnabled.12';
            case 'ppsx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.slideshow';
            case 'ppsm':
                return 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12';
            case 'potx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.template';
            case 'potm':
                return 'application/vnd.ms-powerpoint.template.macroEnabled.12';
            case 'ppam':
                return 'application/vnd.ms-powerpoint.addin.macroEnabled.12';
            case 'sldx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.slide';
            case 'sldm':
                return 'application/vnd.ms-powerpoint.slide.macroEnabled.12';
            case 'one':
                return 'application/msonenote';
            case 'onetoc2':
                return 'application/msonenote';
            case 'onetmp':
                return 'application/msonenote';
            case 'onepkg':
                return 'application/msonenote';
            case 'thmx':
                return 'application/vnd.ms-officetheme';
                //END MS Office 2007 Docs
        }

        return finfo_file(finfo_open(FILEINFO_MIME_TYPE), $filepath);
    }

    public static function slugify($text, string $divider = '-')
    {
        // replace non letter or digits by divider
        $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

        // transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);

        // trim
        $text = trim($text, $divider);

        // remove duplicate divider
        $text = preg_replace('~-+~', $divider, $text);

        // lowercase
        $text = strtolower($text);

        if (empty($text)) {
            return 'n-a';
        }

        return $text;
    }

    // Vijay : 27-02-2024
    public static function clean($string)
    {


        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }
    public static function removeSpecialChar($string)
    {
        $string = str_replace('-', '', $string); // Replaces all spaces with hyphens.
        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

    // Vijay : 31-01-2024
    public static function getRate($facility, $arrivalTime, $diffInHours)
    {
        $isMember = 0;
        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
            \Log::info("getRate from Helper 111 ");
            $rate = $facility->rateForReservationByPassRateEngine($arrivalTime, $diffInHours, false, false, null, false, false, '0', $isMember);
        } else {
            \Log::info("getRate from Helper 222 ");
            $rate = $facility->rateForReservationOnMarker($arrivalTime, $diffInHours, false, false, false, true, false, 0, $isMember);
        }
        return $rate;
    }

    /* 
      @ Facility Type is gated or ungated 
      @ 1 => Gated , 0 => ungated  
    */
    public static function isOvernightOrNormal($ticket, $facility_type, $arrival_time, $length_of_stay)
    {
        // $exitTime = Carbon::parse('now');
        // $exitTime = Carbon::parse('2023-11-22 06:04:01');
        $overnightPaymentStatus = [];
        // $overnightPaymentStatus['status'] = false;
        $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => '43', 'active' => '1', 'partner_id' => $ticket->facility->owner_id])->first();
        if ($overnightFallingTime) {
            // Gated 
            if ($facility_type == '1') {
                $exitTime = Carbon::parse('now');
                $OverStratTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);
                $exitBetweenOvernight =  $exitTime->between($OverStratTime, $OverEndTime);
                $ticketPaymentDate = Carbon::parse($ticket->payment_date);
                $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);

                // check Overstay and payment for normal and overstay
                $extendOverstay = OverstayTicket::where(['ticket_id' => $ticket->id])->orderBy('id', 'DESC')->first();
            } else { // UnGated

                $exitTime = Carbon::parse(QueryBuilder::addMintsInDatetime(Carbon::parse($arrival_time), QueryBuilder::getLengthInMints($length_of_stay)));
                $OverStratTime = self::timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = self::timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);

                // Vijay : 28-05-2024 : USM Overnight 
                $isSameDay = $OverStratTime->isSameDay($OverEndTime);       // to check overnight is start day before and end in Next Day.  // expected false
                // $entryNextDay = $arrival_time->isSameDay($rateEndEntryTime);    // to check entry of user in next but overnight is started Day Before. // expected true

                // Vijay : 28-09-2024-To handle Usm Case but approch is genric.
                // user at next day then i will substract 1 Day to check today valid till.
                if ($isSameDay == false && $carbonNow->lte(self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnight_end_time))) {
                    $OverStratTime = self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnigth_start_time);       // Rate Exit Start time 
                    $OverEndTime = self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnight_end_time);             // Rate Exit End time                    
                    // dd($isSameDay, Carbon::parse('now'), $carbonNow, $OverStratTime, $OverEndTime, $carbonNow->lte(self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnight_end_time)));
                }

                /*if ($overnightFallingTime->rate_category_id == 362) {
                    $OverStratTime = self::timestampToCarbon(Carbon::parse($arrival_time), $overnightFallingTime->overnigth_start_time);
                    if (strtotime($overnightFallingTime->overnigth_start_time) > strtotime($overnightFallingTime->overnight_end_time)) {
                        $OverStratTime = self::timestampToCarbon(Carbon::parse($arrival_time), $overnightFallingTime->overnigth_start_time)->subDays(1);
                    }
                    $OverEndTime = self::timestampToCarbon(Carbon::parse($arrival_time), $overnightFallingTime->overnight_end_time);
                }*/

                $exitBetweenOvernight =  $exitTime->between($OverStratTime, $OverEndTime);

                // PIMS-11890 start date changes after discussion by vijay sir
                // $ticketPaymentDate = Carbon::parse($ticket->checkout_time);
                // $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                if (!empty($ticket->payment_date)) {
                    $ticketPaymentDate = Carbon::parse($ticket->payment_date);
                    $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                } else {
                    $paymentStatus = false;
                }


                // check Overstay and payment for normal and overstay
                $extendOverstay = TicketExtend::where(['ticket_id' => $ticket->id])->orderBy('id', 'DESC')->first();
                if ($extendOverstay) {
                    $ticketPaymentDate = Carbon::parse($extendOverstay->checkout_time);
                    $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                }
            }
            // dd($ticketPaymentDate, $exitBetweenOvernight, $paymentStatus);
            \Log::info("exitIslowerToEndTime exitIslowerToEndTime {$exitBetweenOvernight}");
            if ($extendOverstay) {
                //  either Reservation or Normal Drive up.
                $ticketPaymentDate = Carbon::parse($extendOverstay->checkout_time);
                $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                if ($exitBetweenOvernight && $paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else if ($exitBetweenOvernight) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = false;
                } else if ($paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = false;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else {
                    return false;
                }
            } else {
                // check in against Reservation 
                if ($ticket->reservation_id != '') {
                    if ($ticket->reservation->thirdparty_integration_id) { // third party pay first pay to come in overstay
                        // because fisrt need to pay
                        return false;
                    }
                }
                // checkin against reservation 
                if ($exitBetweenOvernight && $paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else if ($exitBetweenOvernight) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = false;
                } else if ($paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = false;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else {
                    return false;
                }
            }
        }
        return $overnightPaymentStatus;
        return false;
    }

    public static function timestampToCarbon(Carbon $initial, $time)
    {
        $times = explode(':', $time);

        $hour = $times[0] ?? 0;
        $minute = $times[1] ?? 0;
        $second = $times[2] ?? 0;

        return $initial->copy()->hour($hour)->minute($minute)->second($second);
    }

    /***
     *  Method to check time zone
     *  Alka::date::29 march 2024
     */
    public static function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    public static function getTableNameByNamespace($modelNamespace)
    {
        // Resolve the model class name from the given namespace
        $modelName = class_basename($modelNamespace);

        // Attempt to instantiate the model
        $model = new $modelNamespace();
        $table_name = $model->getTable();

        // Check if the model has a table name defined
        if (isset($table_name)) {
            return $table_name;
        }

        // If the table name is not defined, get it from the database schema
        // return $model->getConnection()->getTablePrefix() . $model->getConnection()->getTable($model->getTable());
    }

    public static function getFomatedEndTime($rate, $arrival_time, $type = 'exit')
    {
        if ($type == 'exit') {
            $rateExitTime = self::timestampToCarbon($arrival_time, $rate->exit_time_end);          // Rate Exit End time 
        } else if ($type == 'entry') {
            $rateExitTime = self::timestampToCarbon($arrival_time, $rate->entry_time_begin);          // Rate Exit End time 
        }
        return Carbon::parse($rateExitTime)->format('g:i A');
    }

    public static function convertNumberToDays($number)
    {
        //  $number = "1,2,5";
        $days = '';
        if ($number != '') {
            $number_arr = explode(',', $number);
            if (in_array('1', $number_arr)) {
                $days .= 'Sunday';
            }
            if (in_array('2', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Monday';
            }
            if (in_array('3', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Tuesday';
            }

            if (in_array('4', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Wednesday';
            }
            if (in_array('5', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Thursday';
            }
            if (in_array('6', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Friday';
            }
            if (in_array('7', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Saturday';
            }
        }
        // dd($number_arr,$days);
        return $days;
    }
    public static function convertToUTC($facility_id, $datetime)
    {
        $timezone = Facility::where(['id' => $facility_id])->value('timezone');
        if ($timezone != '') {
            $date = Carbon::createFromFormat('Y-m-d H:i:s', $datetime, $timezone);
            // Convert the datetime to UTC
            $dateInUTC = $date->setTimezone('UTC');

            // Output the datetime in UTC
            $n_date = $dateInUTC->toDateTimeString();

            return Carbon::parse($n_date)->format('m/d/Y H:i:s');
        } else {
            return Carbon::parse($datetime)->format('m/d/Y H:i:s');
        }
    }

    public static function ticketExtendDetails($ticketId)
    {
        $ticketObj = Ticket::find($ticketId);
        $baseLengthInMints = $lengthInMints = $discountAmount = $totalAmount =  $toatlAmountPaid = $totalParkingAmount = $processingFee = $taxFee  = 0;
        if ($ticketObj->count() > 0) {
            $isExtended = $ticketObj->ticketExtend;
            foreach ($isExtended as $key => $value) {
                $totalAmount            += $value->total;
                $toatlAmountPaid        += $value->grand_total;
                $discountAmount         += $value->discount_amount;
                $totalParkingAmount     += $value->grand_total - ($value->processing_fee + $value->tax_fee + $value->additional_fee + $value->surcharge_fee);
                $processingFee          += $value->processing_fee;
                $taxFee                 += $value->tax_fee;
                $lengthInMints          += self::getLengthInMints($value->length);
                $baseLengthInMints      += self::getLengthInMints($value->base_length != null ? $value->base_length : 0);
            }
        }

        $extendDetails['extend_total']              = $totalAmount;
        $extendDetails['extend_grand_total']        = $toatlAmountPaid;
        $extendDetails['extend_parking_amouont']    = $totalParkingAmount;
        $extendDetails['extend_discoountAmount']    = $discountAmount;
        $extendDetails['extend_processin_fee']      = $processingFee;
        $extendDetails['extend_tax_fee']            = $taxFee;
        $extendDetails['extend_length']             = $lengthInMints;
        $extendDetails['extend_base_length']        = $baseLengthInMints;
        return $extendDetails;
    }

    public static function newticketExtendDetails($ticketObj)
    {
        $extendDetails = [
            'extend_total'           => 0,
            'extend_grand_total'     => 0,
            'extend_discoountAmount' => 0,
            'extend_processin_fee'   => 0,
            'extend_tax_fee'         => 0,
            'extend_parking_amouont' => 0,
            'extend_length'          => 0,
            'extend_base_length'     => 0,
        ];

        if ($ticketObj && count($ticketObj->ticketExtend) > 0) {
            $extendDetails['extend_total']           = $ticketObj->ticketExtend->sum('total');
            $extendDetails['extend_grand_total']     = $ticketObj->ticketExtend->sum('grand_total');
            $extendDetails['extend_discoountAmount'] = $ticketObj->ticketExtend->sum('discount_amount');
            $extendDetails['extend_processin_fee']   = $ticketObj->ticketExtend->sum('processing_fee');
            $extendDetails['extend_tax_fee']         = $ticketObj->ticketExtend->sum('tax_fee');

            // Calculating values using map() since Laravel 5.2 does not support arrow functions
            $extendDetails['extend_parking_amouont'] = $ticketObj->ticketExtend->map(function ($value) {
                return $value->grand_total - ($value->processing_fee + $value->tax_fee);
            })->sum();

            $extendDetails['extend_length'] = $ticketObj->ticketExtend->map(function ($value) {
                return self::getLengthInMints($value->length);
            })->sum();

            $extendDetails['extend_base_length'] = $ticketObj->ticketExtend->map(function ($value) {
                return self::getLengthInMints(!is_null($value->base_length) ? $value->base_length : 0);
            })->sum();
        }

        return $extendDetails;
    }

    public static function ticketTemporaryDetails($ticket, $priceBreakUp, $length = null)
    {
        $existTempTicket = TicketTemporaryDetail::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->delete();
        $tempTicket                     = new TicketTemporaryDetail();
        $tempTicket->ticket_id          = $ticket->id;
        $tempTicket->ticket_number      = $ticket->ticket_number;
        $tempTicket->processing_fee     = $priceBreakUp['processing_fee'];
        $tempTicket->tax_fee            = $priceBreakUp['tax_rate'];
        $tempTicket->parking_amount     = $priceBreakUp['parking_amount'];
        $tempTicket->paid_amount        = $priceBreakUp['paid_amount'];
        $tempTicket->discount_amount    = $priceBreakUp['discount_amount'];
        $tempTicket->grand_total        = $priceBreakUp['payable_amount'];
        $tempTicket->total              = $priceBreakUp['total'];
        $tempTicket->length             = $length;
        $tempTicket->save();
    }

    public static function getTicketTemporaryDetails($ticket)
    {
        return TicketTemporaryDetail::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
    }

    public static function getPartnerConfig($key, $partner_id)
    {
        return PartnerConfiguration::where("key", $key)->where("partner_id", $partner_id)->first();
    }



    public static function ticketOverstayDetails($ticketId)
    {
        $ticketObj = Ticket::find($ticketId);
        $discountAmount = $totalAmount =  $toatlAmountPaid = $totalParkingAmount = $processingFee = $taxFee  = 0;
        if ($ticketObj->count() > 0) {
            $isOverstay = $ticketObj->overstay;
            foreach ($isOverstay as $key => $value) {
                $totalAmount            += $value->total;
                $toatlAmountPaid        += $value->grand_total;
                $discountAmount         += $value->discount_amount;
                $totalParkingAmount     += $value->grand_total - ($value->processing_fee + $value->tax_fee);
                $processingFee          += $value->processing_fee;
                $taxFee                 += $value->tax_fee;
            }
        }
        $overstayDetails['overstay_total']              = $totalAmount;
        $overstayDetails['overstay_grand_total']        = $toatlAmountPaid;
        $overstayDetails['overstay_parking_amount']    = $totalParkingAmount;
        $overstayDetails['overstay_discount_mount']    = $discountAmount;
        $overstayDetails['overstay_processing_fee']      = $processingFee;
        $overstayDetails['overstay_tax_fee']            = $taxFee;
        return $overstayDetails;
    }

    public static function setPrintReceipt($ticket, $is_checkin)
    {
        $data =  [];
        $key = 0;
        $data[$key]["key"] = "Check-In At:";
        $data[$key]["value"] = date("m/d/Y g:i A", strtotime($ticket->checkin_time));
        $key = $key + 1;
        if ($is_checkin == '1') {
            if ($ticket->permit_request_id > 0) {
                $permit = PermitRequest::find($ticket->permit_request_id);
                $data[$key]["key"] = "Permit Number:";
                $data[$key]["value"] = $permit->account_number;
                $key = $key + 1;
            }
            if ($ticket->reservation_id > 0) {
                $reservation = Reservation::find($ticket->reservation_id);
                $data[$key]["key"] = "Booking ID:";
                $data[$key]["value"] = $reservation->ticketech_code;
                $key = $key + 1;
            }
            $data[$key]["key"] = "Ticket ID:";
            $data[$key]["value"] = $ticket->ticket_number;
            $key = $key + 1;
            if ($ticket->license_plate != '') {
                $data[$key]["key"] = "LPN:";
                $data[$key]["value"] = $ticket->license_plate;
                $key = $key + 1;
            }
            if ($ticket->card_last_four != '') {
                $data[$key]["key"] = "Credit Card Used:";
                $data[$key]["value"] = $ticket->card_last_four;
                $key = $key + 1;
            }
        } else {
            $data[$key]["key"] = "Check-Out At:";
            $data[$key]["value"] = date("m/d/Y g:i A", strtotime($ticket->checkout_time));
            $key = $key + 1;

            if ($ticket->permit_request_id > 0) {
                $permit = PermitRequest::find($ticket->permit_request_id);
                $data[$key]["key"] = "Permit Number:";
                $data[$key]["value"] = $permit->account_number;
                $key = $key + 1;
            }
            if ($ticket->reservation_id > 0) {
                $reservation = Reservation::find($ticket->reservation_id);
                $data[$key]["key"] = "Booking ID:";
                $data[$key]["value"] = $reservation->ticketech_code;
                $key = $key + 1;
            }

            $data[$key]["key"] = "Ticket ID:";
            $data[$key]["value"] = $ticket->ticket_number;
            $key = $key + 1;

            if ($ticket->checkout_license_plate != '') {
                $data[$key]["key"] = "LPN:";
                $data[$key]["value"] = $ticket->license_plate;
                $key = $key + 1;
            }
            if ($ticket->card_last_four != '') {
                $data[$key]["key"] = "Credit Card Used:";
                $data[$key]["value"] = $ticket->card_last_four;
                $key = $key + 1;
            }
            if ($ticket->grand_total > 0) {
                $data[$key]["key"] = "Line";
                $data[$key]["value"] = "-----------------------------------------";
                $key = $key + 1;
            }

            $totalParkingAmount = $ticket->parking_amount;
            $totalPaidAmount = $ticket->grand_total;
            $totalProcessingFee = $ticket->processing_fee;
            $totalTaxFee = $ticket->tax_fee;
            $totalValidatedAmount = $ticket->paid_amount;
            $ticketOverstayDetails = QueryBuilder::ticketOverstayDetails($ticket->id);
            if ($ticketOverstayDetails) {
                $totalPaidAmount = $totalPaidAmount + $ticketOverstayDetails['overstay_grand_total'];
                $totalParkingAmount = $totalParkingAmount + $ticketOverstayDetails['overstay_parking_amount'];
                $totalProcessingFee = $ticket->processing_fee + $ticketOverstayDetails['overstay_processing_fee'];
                $totalTaxFee = $ticket->tax_fee + $ticketOverstayDetails['overstay_tax_fee'];
            }
            if ($totalParkingAmount > 0) {
                $data[$key]["key"] = "Parking Amount:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalParkingAmount);
                $key = $key + 1;
            }
            if ($totalProcessingFee > 0) {
                $data[$key]["key"] = "Processing Fee:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalProcessingFee);
                $key = $key + 1;
            }
            if ($totalTaxFee > 0) {
                $data[$key]["key"] = "Tax:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalTaxFee);
                $key = $key + 1;
            }
            if ($totalValidatedAmount > 0) {
                $data[$key]["key"] = "Validated Amount:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalValidatedAmount);
                $key = $key + 1;
            }
            if ($totalPaidAmount > 0) {
                $data[$key]["key"] = "Amount Paid:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalPaidAmount);
            }
        }
        return $data;
    }

    /* this function is use to check users license plate already exists
     */
    public static function isUserVehicleExists($licensePlate, $user_id, $partner_id)
    {
        $PermitVehicle = PermitVehicle::where(['license_plate_number' => $licensePlate, 'user_id' => $user_id, 'partner_id' => $partner_id])->first();
        if ($PermitVehicle) {
            return $PermitVehicle;
        } else {
            return false;
        }
    }

    public static function getFormatedHooTime($arrival_time, $time)
    {
        $rateExitTime = self::timestampToCarbon($arrival_time, $time);          // Rate Exit End time 

        return Carbon::parse($rateExitTime)->format('g:i A');
    }

    public static function setReferenceKey($anetId, $ticketRefrenceNumber)
    {
        $paymentRefrenceId = AuthorizeNetTransaction::find($anetId);
        if ($paymentRefrenceId) {
            $paymentRefrenceId->reference_key = $ticketRefrenceNumber;
            $paymentRefrenceId->save();
        }
    }

    #PIMS-11368 DD
    public static function setAllTransactions($jsonString, $gatewaytype = NULL, $reference_key = NULL, $action = NULL, $user_id = NULL)
    {
        $data = json_decode($jsonString, true);
        Log::info("Save Fail Transacrion");
        Log::info($data);
        // Log::info('Payment Status : ' . $data['Status']);
        $user = new AllTransactions();
        $user->total =  isset($data['total']) ? $data['total'] : '';

        if (isset($data['total'])) {
            $user->total = $data['total'];
        } else if (isset($data['balanceAmount'])) {
            $user->total = $data['balanceAmount'];
        } else if (isset($data['Response']['Params']['AmountUsed'])) {
            $user->total = $data['Response']['Params']['AmountUsed'];
        } else {
            $user->total = '';
        }
        if (isset($data['user_id'])) {
            $user->user_id = $data['user_id'];
        } else if (isset($user_id)) {
            $user->user_id = $user_id;
        } else {
            $user->user_id = '';
        }
        if (isset($data['description'])) {
            $user->description = $data['description'];
        } else if (isset($data['token'])) {
            $user->description = $data['token'];
        } else if (isset($data['Response']['Params']['TxID'])) {
            $user->description = $data['Response']['Params']['TxID'];
        } else {
            $user->description = '';
        }
        if (isset($data['response_code'])) {
            $user->response_code = $data['response_code'];
        } else if (isset($data['responseCode'])) {
            $user->response_code = $data['responseCode'];
        } else {
            $user->response_code = '';
        }
        if (isset($data['response_message'])) {
            $user->response_message = $data['response_message'];
        } else if (isset($data['responseMessage'])) {
            $user->response_message = $data['responseMessage'];
        } else if (isset($data['Response']['Params']['ResultReason'])) {
            $user->response_message = $data['Response']['Params']['ResultReason'];
        } else {
            $user->response_message = '';
        }
        if (isset($data['transactionReference']['authCode'])) {
            $user->auth_code = $data['transactionReference']['authCode'];
        } else if (isset($data['Response']['Params']['BankAuthCode'])) {
            $user->response_message = $data['Response']['Params']['BankAuthCode'];
        } else if (isset($data['auth_code'])) {
            $user->auth_code = $data['auth_code'];
        } else {
            $user->auth_code = '';
        }
        if (isset($data['ref_id'])) {
            $user->ref_id = $data['ref_id'];
        } else if (isset($data['referenceNumber'])) {
            $user->ref_id = $data['referenceNumber'];
        } else if (isset($data['Response']['Params']['RequesterTransRefNum'])) {
            $user->ref_id = $data['Response']['Params']['RequesterTransRefNum'];
        } else {
            $user->ref_id = '';
        }
        if (isset($data['status_message'])) {
            $user->status_message = $data['status_message'];
        } else if (isset($data['cvnResponseMessage'])) {
            $user->status_message = $data['cvnResponseMessage'];
        } else if (isset($data['Response']['Params']['TxStateText'])) {
            $user->status_message = $data['Response']['Params']['TxStateText'];
        } else {
            $user->status_message = '';
        }
        if (is_null($reference_key) && isset($data['reference_key'])) {
            $user->reference_key =  $data['reference_key'];
        } else {
            $user->reference_key =  $reference_key;
        }
        if (isset($data['created_at'])) {
            $user->created_at = $data['created_at'];
        }
        //$user->created_at = isset($data['created_at']) ? $data['created_at'] : '';
        $user->status_code = isset($data['status_code']) ? $data['status_code'] : '';
        $user->status_type = isset($data['status_type']) ? $data['status_type'] : '';

        $user->failed_transactionscol = isset($data['failed_transactionscol']) ? $data['failed_transactionscol'] : '';
        $user->response_json = $jsonString;
        $user->action = isset($action) ? $action : '';
        $user->gateway = isset($gatewaytype) ? $gatewaytype : '';

        // Update Details for Failed Case
        if ($gatewaytype == 'datacap' && $action == 'Failed') {
            if ($data['Status'] == 'Error') {
                $user->description      = isset($data['ResponseOrigin']) ? $data['ResponseOrigin'] : '';
                $user->response_code    = isset($data['ReturnCode']) ? $data['ReturnCode'] : '';
                $user->response_message = isset($data['Message']) ? $data['Message'] : '';
                $user->status_code      = isset($data['ReturnCode']) ? $data['ReturnCode'] : '';
                $user->status_type      = isset($data['Status']) ? $data['Status'] : '';
                $user->ref_id           = isset($data['RefNo']) ? $data['RefNo'] : '';
                $user->save();
            }
        }

        $user->save();
    }

    public static function setAllFailedTransactions($jsonString, $gatewaytype = NULL, $reference_key = NULL, $action = NULL, $user_id = NULL, $payment_request = NULL, $request = NULL)
    {
        $data = json_decode($jsonString, true);
        $user = new AllFailedTransactions();
        if (isset($data['total'])) {
            $user->total = $data['total'];
        } else if (isset($data['balanceAmount'])) {
            $user->total = $data['balanceAmount'];
        } else if (isset($data['Response']['Params']['AmountUsed'])) {
            $user->total = $data['Response']['Params']['AmountUsed'];
        } else {
            $user->total = '';
        }
        if (isset($data['user_id'])) {
            $user->user_id = $data['user_id'];
        } else if (isset($user_id)) {
            $user->user_id = $user_id;
        } else {
            $user->user_id = '';
        }
        if (isset($data['description'])) {
            $user->description = $data['description'];
        } else if (isset($data['token'])) {
            $user->description = $data['token'];
        } else if (isset($data['Response']['Params']['TxID'])) {
            $user->description = $data['Response']['Params']['TxID'];
        } else {
            $user->description = '';
        }
        if (isset($data['response_code'])) {
            $user->response_code = $data['response_code'];
        } else if (isset($data['responseCode'])) {
            $user->response_code = $data['responseCode'];
        } else {
            $user->response_code = '';
        }
        if (isset($data['response_message'])) {
            $user->response_message = $data['response_message'];
        } else if (isset($data['responseMessage'])) {
            $user->response_message = $data['responseMessage'];
        } else if (isset($data['Response']['Params']['ResultReason'])) {
            $user->response_message = $data['Response']['Params']['ResultReason'];
        } else {
            $user->response_message = '';
        }
        if (isset($data['transactionReference']['authCode'])) {
            $user->auth_code = $data['transactionReference']['authCode'];
        } else if (isset($data['Response']['Params']['BankAuthCode'])) {
            $user->response_message = $data['Response']['Params']['BankAuthCode'];
        } else if (isset($data['auth_code'])) {
            $user->auth_code = $data['auth_code'];
        } else {
            $user->auth_code = '';
        }
        if (isset($data['ref_id'])) {
            $user->ref_id = $data['ref_id'];
        } else if (isset($data['referenceNumber'])) {
            $user->ref_id = $data['referenceNumber'];
        } else if (isset($data['Response']['Params']['RequesterTransRefNum'])) {
            $user->ref_id = $data['Response']['Params']['RequesterTransRefNum'];
        } else {
            $user->ref_id = '';
        }
        if (isset($data['status_message'])) {
            $user->status_message = $data['status_message'];
        } else if (isset($data['cvnResponseMessage'])) {
            $user->status_message = $data['cvnResponseMessage'];
        } else if (isset($data['Response']['Params']['TxStateText'])) {
            $user->status_message = $data['Response']['Params']['TxStateText'];
        } else {
            $user->status_message = '';
        }
        if (is_null($reference_key) && isset($data['reference_key'])) {
            $user->reference_key =  $data['reference_key'];
        } else {
            $user->reference_key =  $reference_key;
        }
        if (isset($data['created_at'])) {
            $user->created_at = $data['created_at'];
        }
        if (isset($payment_request) && !empty($payment_request)) {
            $user->payment_request_json = $payment_request;
        }
        //$user->created_at = isset($data['created_at']) ? $data['created_at'] : '';
        $user->status_code = isset($data['status_code']) ? $data['status_code'] : '';
        $user->status_type = isset($data['status_type']) ? $data['status_type'] : '';

        $user->failed_transactionscol = isset($data['failed_transactionscol']) ? $data['failed_transactionscol'] : '';
        $user->response_json = $jsonString;
        $user->action = isset($action) ? $action : '';
        $user->gateway = isset($gatewaytype) ? $gatewaytype : '';
        $user->license_plate = isset($request->vehicleList) ? $request->vehicleList : '';

        $expiry = "";
        $card_last_four = "";


        if (isset($request->card_last_four)) {
            $card_last_four = $request->card_last_four;
        } elseif (isset($request->payment_last_four)) {
            $card_last_four = $request->payment_last_four;
        }

        if (isset($request->expiration_date)) {
            $expiry = $request->expiration_date;
        } elseif (isset($request->expiry)) {
            $expiry = $request->expiry;
        } elseif (isset($request->expiration)) {
            $expiry = $request->expiration;
        }

        $user->card_last_four    = isset($card_last_four) ? $card_last_four : '0';
        $user->expiry               = isset($expiry) ? $expiry : '0';
        $user->save();
    }

    public static function getTimeDifferenceInHHMM($startDateTime, $endDateTime)
    {
        // // Parse the date-time strings into Carbon instances // with seconds
        // $start = Carbon::parse($startDateTime);
        // $end = Carbon::parse($endDateTime);
        // Parse the date-time strings into Carbon instances
        $start = Carbon::parse($startDateTime)->startOfMinute(); // Ignore seconds
        $end = Carbon::parse($endDateTime)->startOfMinute();     // Ignore seconds

        // Calculate the difference in total minutes
        $totalMinutes = $end->diffInMinutes($start);

        // Convert minutes to hours and remaining minutes
        $hours = intdiv($totalMinutes, 60); // Get the number of hours
        $minutes = $totalMinutes % 60;     // Get the remaining minutes

        // Format the output as HH:MM
        return sprintf('%02d.%02d', $hours, $minutes);
    }

    public static function getDiffByColumn(Collection $mainCollection, Collection $compareCollection, $column)
    {
        // Extract the column values from the compare collection
        $compareValues = $compareCollection->pluck($column)->toArray();

        // Filter the main collection to exclude records with matching column values
        return $mainCollection->filter(function ($item) use ($compareValues, $column) {
            return !in_array($item[$column], $compareValues);
        });
    }

    public static function getRegisterUserProfileId($gatewayType, $cardLastFour, $cardExpiry, $userId, $partnerId)
    {
        switch ($gatewayType) {
            case '1':
                $cardDetails = PlanetPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'partner_id' => $partnerId])->whereNull('deleted_at')->latest()->first();
                break;
            case '2':
                $cardDetails = DatacapPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'partner_id' => $partnerId])->whereNull('deleted_at')->latest()->first();
                break;
            case '4':
                $cardDetails = HeartlandPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'partner_id' => $partnerId])->whereNull('deleted_at')->latest()->first();
                break;
            default:
                Log::error("No Card Found for GateWayType : {$gatewayType} User ID : {$userId} and Last Four : {$cardLastFour} : Exppiry : {$cardExpiry} ");
                return false;
                break;
        }
        return $cardDetails;
    }

    public static function calculateProratePriceOnPromotion($rate, $noOfDays)
    {

        $parkingAmount      = $rate['price'];
        $rateHours          = $rate['max_stay'];

        $pricePerHours      = $parkingAmount / $rateHours;
        $payableAmount      = $noOfDays * $pricePerHours;

        // dd('q2', $parkingAmount, $rateHours, $pricePerHours, $payableAmount);
        return sprintf("%.2f", $payableAmount);
    }

    public static function getPayableAndDiscountHours($request, $rate)
    {

        // calulate Payable Hours for Price
        $tillNowLengthInMints   = self::getLengthInMints($request->tillNowLength);
        $oneDayLengthInMInts    = self::getLengthInMints(self::FULL_DAY_HOUR_VAL);
        $promoUsedHours         = (int) $request->promoUsedHours;
        $requestedLengthDays    = $rate['max_stay'] >= self::FULL_DAY_HOUR_VAL ? ceil($rate['max_stay'] / self::FULL_DAY_HOUR_VAL) : 0;

        $remaningPromoHoursInaDays = 0;   // Remaing Promo Hours in 24 Hours (1 Day) 
        if ($request->perDayFreeHours > $request->promoUsedHours) {
            $remaningPromoHoursInaDays = (int) ($request->perDayFreeHours - $request->promoUsedHours);   // Remaing Promo Hours in 24 Hours (1 Day) 
        }

        // dd(ceil($rate['max_stay'] / self::FULL_DAY_HOUR_VAL));
        // dd($request->all(), $promoUsedHours, $remaningPromoHoursInaDays, $tillNowLengthInMints, $oneDayLengthInMInts, $requestedLengthDays, $rate['max_stay']);
        // if ($rate['max_stay'] > self::FULL_DAY_HOUR_VAL) {
        // dd($tillNowLengthInMints, $oneDayLengthInMInts, $tillNowLengthInMints > $oneDayLengthInMInts);
        if ($tillNowLengthInMints > $oneDayLengthInMInts) {
            if ($promoUsedHours >= $request->perDayFreeHours) {

                if ($request->length  <= $request->perDayFreeHours) {
                    $payableHours       =   0;
                    $dayHourInMints     =   $request->length;
                } else {
                    // dd($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL, $requestedLengthDays, $requestedLengthDays * $request->perDayFreeHours);
                    if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {  // More that 24 hour case 
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($rate['max_stay']) - self::getLengthInMints($requestedLengthDays * $request->perDayFreeHours));
                        $dayHourInMints     =   $requestedLengthDays * $request->perDayFreeHours;
                    } else {
                        // Request in Hours less than 24 hours
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($request->perDayFreeHours));
                        $dayHourInMints     =   $request->perDayFreeHours;
                    }
                }
            } else {
                if ($request->length  <= $request->perDayFreeHours) {
                    $payableHours       =   0;
                    $dayHourInMints     =   $request->length;
                } else {
                    // dd($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL, $requestedLengthDays, $requestedLengthDays * $request->perDayFreeHours);
                    if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {  // More that 24 hour case 
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($rate['max_stay']) - self::getLengthInMints($requestedLengthDays * $request->perDayFreeHours));
                        $dayHourInMints     =   $requestedLengthDays * $request->perDayFreeHours;
                    } else {
                        // Request in Hours less than 24 hours
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($request->perDayFreeHours));
                        $dayHourInMints     =   $request->perDayFreeHours;
                    }
                }
            }
            $result['payableHours']  = $payableHours;
            $result['discountHours'] =  $dayHourInMints;
            return $result;
        } else {
            if ($request->promoUsedHours < $request->perDayFreeHours && $request->length <= $remaningPromoHoursInaDays) {
                $payableHours       =   0;
                $dayHourInMints     =   $request->length;
            } else if ($request->length  <= $request->perDayFreeHours && $request->length > $remaningPromoHoursInaDays) {
                // $payableHours       =   $request->length - $remaningPromoHoursInaDays;
                $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($remaningPromoHoursInaDays));
                $dayHourInMints     =   $remaningPromoHoursInaDays;
            } else {
                // dd($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL, $requestedLengthDays, $requestedLengthDays * $request->perDayFreeHours);
                if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {  // More that 24 hour case 
                    $payableHours       =   self::getLengthInHours(self::getLengthInMints($rate['max_stay']) - self::getLengthInMints($requestedLengthDays * $request->perDayFreeHours));
                    $dayHourInMints     =   $requestedLengthDays * $request->perDayFreeHours;
                } else {
                    // Request in Hours less than 24 hours
                    $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($request->perDayFreeHours));
                    $dayHourInMints     =   $request->perDayFreeHours;
                }
            }
            $result['payableHours']  = $payableHours;
            $result['discountHours'] =  $dayHourInMints;
            return $result;
            $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->tillNowLength) - self::getLengthInMints(self::FULL_DAY_HOUR_VAL));
            $dayHourInMints     =   self::getLengthInMints(self::FULL_DAY_HOUR_VAL);
        }
        // cacluate applied discunt Hours
        // dd($request->all(), self::getLengthInMints($request->totalLength) + self::getLengthInMints($rate['max_stay']));
        $totalLength        = (self::getLengthInMints($request->totalLength) + self::getLengthInMints($rate['max_stay']));

        // dd($totalLength > $dayHourInMints);
        if ($totalLength > $dayHourInMints) {
            $nextdayHours       =   self::getLengthInHours($totalLength - $dayHourInMints);
        } else {
            $nextdayHours       = 0;
        }

        $result['payableHours']  = $payableHours;
        $result['discountHours'] =  $nextdayHours;

        return $result;
    }

    // PIMS - 
    public static function calculateHourlyPromotions($request, $appliedPromotion, $rate, $facility, $serviceType)
    {
        if ($serviceType == 'transient') {
            $diffInHours   = $request->length;
            $payableAmount = self::calculateProratePriceOnPromotion($rate, $diffInHours);
            return $payableAmount;
        }
    }
    public static function getDayWisePromocodeUsed($lengthInhours)
    {
        $lengthInhours = self::getLengthInMints($lengthInhours);
        if ($lengthInhours > 1440) {
            $noOfDayUsed =  ceil($lengthInhours / 1440);
            return $noOfDayUsed;
        } else if ($lengthInhours > 0) {
            return 1;
        }
        return false;
    }


    /*
    * Dev: Sagar
    * PIMS - 11744
    * checking promocode for daywise with time range
    */
    public static function checkDayWiseValidationForPromoCode($promotion)
    {
        $checkApplicableDays = PromotionDay::where('promotion_id', $promotion->id)->first();
        $applicableDays = $checkApplicableDays != null
            ? explode(',', $checkApplicableDays->days)
            : ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        $today = date('l');
        if (!in_array($today, $applicableDays)) {
            throw new ApiGenericException('The promocode is not valid for today.');
        }

        $currentTime = date('H:i:s');
        if (!is_null($checkApplicableDays) && !empty($checkApplicableDays)) {
            if ($currentTime < $checkApplicableDays->start_time || $currentTime > $checkApplicableDays->end_time) {
                throw new ApiGenericException('The promocode is not valid at this time.');
            }
        }
        return true;
    }

    public static function checkDayWiseUsageLimit($promotion, $usage_type, $totalUesedByUser, $userId, $promocode, $license_plate, $ticket_number)
    {
        $today = date('l');
        if (is_null($userId)) {
            $getUserDetail = Ticket::where('license_plate', $license_plate)->first();
            $userId = $getUserDetail->user_id;
        }

        if ($usage_type == 4) {
            // Fetch applicable days
            $checkApplicableDays = PromotionDay::where('promotion_id', $promotion->id)->first();
            $applicableDays = $checkApplicableDays
                ? explode(',', $checkApplicableDays->days)
                : ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

            // Check if today is within the applicable days
            if (!in_array($today, $applicableDays)) {
                throw new ApiGenericException('The promocode is not valid for today.');
            }

            // Fetch tickets for today
            $todayDate = Carbon::now()->toDateString();
            // $todayDate = '2024-12-30';

            $todayUsageCount = Ticket::where('user_id', $userId)
                ->where('promocode', $promocode)
                ->whereRaw("DATE(check_in_datetime) = ?", [$todayDate])
                ->count();

            // Check if the promo code has been used today
            if ($todayUsageCount >= $promotion->usage) {
                //discuss with vijay sir then extra check in case of extend ticket
                if (isset($ticket_number) && !empty($ticket_number)) {
                    return true;
                }
                throw new ApiGenericException('Maximum usage of this promocode has already been exceeded.');
            }

            // Allow usage
            return true;
        } else if ($usage_type == 3) {
            // Allow usage for unlimited
            return true;
        }

        return false;
    }


    public static function getPayableAndDiscountHoursFromLength($request, $promoResponse)
    {
        $dayCount = $lengthLeft = $fullDays = 0;
        if ($request->tillNowLength >= self::FULL_DAY_HOUR_VAL) {
            $dayCount       = ceil($request->tillNowLength / self::FULL_DAY_HOUR_VAL);

            if ($request->tillNowLength % self::FULL_DAY_HOUR_VAL === 0) {
                $lengthLeft     =  0;
                $fullDays       =  $dayCount;
            } else {
                $lengthLeft     =  $request->tillNowLength - (($dayCount - 1) * self::FULL_DAY_HOUR_VAL);
            }

            if ($lengthLeft > 0) {
                $fullDays = $dayCount - 1;
            }
            $payableHours = $fullDays * self::FULL_DAY_HOUR_VAL - ($fullDays * $promoResponse->discount_in_hours);
            $nextdayHours = ($fullDays * $promoResponse->discount_in_hours);
        } else {
        }

        $result['fullDays']         =  $fullDays;
        $result['lengthLeft']       =  $lengthLeft;
        $result['payableHours']     =  $payableHours;
        $result['discountHours']    =  $nextdayHours;

        return $result;
    }

    #pims-12580 05-02-2025

    public static function ungatedSmsCheckInCheckOut($facility)
    {
        // only send SMS for Reseponsive Form URL.
        // responsive url for Pave and Townsend
        $appUrl = env('CUSTOMERAPP_TOUCHLESS_APP_URL');
        $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("TRANSIENT_URL", $facility->owner_id);
        if ($dynamicReceiptUrl) {
            $appUrl = $dynamicReceiptUrl->value;
        }
        $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        $user = User::find($checkPaymentUrl->user_id);
        $innerSlug = '';
        if ($user->company_name != '') {
            $companyNameArray = explode(" ", $user->company_name);
            $companyName = strtolower($companyNameArray[0]);
            $innerSlug = $companyName;
        } else {
            $innerSlug = $slug;
        }
        $slug = isset($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : '';
        $resUrl = $appUrl . '/' . $slug . '/touchless-parking-' . $innerSlug;
        return $resUrl;
    }

    public static function sanitizePriceBreakUp($priceBreakUp)
    {
        // Ensure every value is non-negative
        foreach ($priceBreakUp as $key => $value) {
            if (is_numeric($value) && $value < 0) {
                $priceBreakUp[$key] = 0;
            }
        }

        return [
            'length'              => $priceBreakUp['length'] ?? 0,
            'total'               => $priceBreakUp['total'] ?? 0,
            'processing_fee'      => $priceBreakUp['processing_fee'] ?? 0,
            'tax_rate'            => $priceBreakUp['tax_rate'] ?? 0,
            'tax_fee'             => $priceBreakUp['tax_rate'] ?? 0, // Double-check if you meant tax_rate or tax_fee
            'parking_amount'      => $priceBreakUp['parking_amount'] ?? 0,
            'extend_amount'       => $priceBreakUp['extend_amount'] ?? 0,
            'paid_amount'         => $priceBreakUp['paid_amount'] ?? 0,
            'amount_paid'         => $priceBreakUp['amount_paid'] ?? 0,
            'payable_amount'      => $priceBreakUp['payable_amount'] ?? 0,
            'discount_amount'     => $priceBreakUp['discount_amount'] ?? 0,
            'is_price_applicable' => $priceBreakUp['is_price_applicable'] ?? 0,
            'additional_fee'      => $priceBreakUp['additional_fee'] ?? 0,
            'surcharge_fee'       => $priceBreakUp['surcharge_fee'] ?? 0
        ];
    }

    protected static function validDayOfTheWeek($rate)
    {
        $startDay = strtolower(date('l', strtotime(Carbon::parse('now'))));
        //$endDay = strtolower(date('l', strtotime($this->arrival_time) + $this->length_of_stay * 3600));
        return $rate->$startDay; //$rate->$endDay;

    }

    protected static function timestampToCarbonString(Carbon $initial, $time)
    {

        $times = explode(':', $time);

        $hour = (int) ($times[0] ?? 0);
        $minute = (int) ($times[1] ?? 0);

        // Clone initial time and set the given hour & minute
        $carbonTime = $initial->copy()->hour($hour)->minute($minute);

        // Check if time shifts to the next day
        $isNextDay = $carbonTime->day > $initial->day;

        // Format time as 12-hour AM/PM
        $formattedTime = $carbonTime->format('h:i A');

        // Append "Next Day" if applicable
        return $isNextDay ? "{$formattedTime} (Next Day)" : $formattedTime;
    }

    public static function formatRateOperationTime($rates)
    {
        $carbonNowTime = Carbon::parse('now');
        if ($rates->count() > 0) {

            $string = '';
            foreach ($rates as $key => $rate) {
                if (!self::validDayOfTheWeek($rate)) {    // to check Current day is applicale or not
                    continue; // Use return null instead of continue
                }
                $entryTime = self::timestampToCarbonString($carbonNowTime, $rate->entry_time_begin) . '-' . self::timestampToCarbonString($carbonNowTime, $rate->entry_time_end);
                $exitTime = self::timestampToCarbonString($carbonNowTime, $rate->exit_time_begin) . '-' . self::timestampToCarbonString($carbonNowTime, $rate->exit_time_end);
                $string .= "{$rate['description']} : \n\r Entry Time {$entryTime}, Exit Time {$exitTime} \n\r";
                // $string .= "Garage Entry Time {$entryTime}, Exit Time {$exitTime} \n";
            }
            return $string;
        }
        return false;
    }

    #DD pims-9183 bulk upload
    public static function validateAndFormatDate($dateString)
    {
        //$formats = ['F j, Y','F j,Y','F d, Y','F d,Y'];
        $formats = [
            'Y-m-d',
            'Y-m-d H:i:s',
            'Y-m-d H:i',
            'Y-m-d-H:i',
            'Y-m-d-H:i:s',
            'Y-m-dP',
            'Y-m-d\TH:iP',
        ];
        foreach ($formats as $format) {
            $d = DateTime::createFromFormat($format, $dateString);
            if ($d) {
                return $d->format('Y-m-d');
            }
        }

        return false;
    }


    public static function storeMasqueradeTrailingData($trailingData)
    {
        try {
            DB::table('masqurade_trailing_data')->insert([
                'masqurade_parent_id' => $trailingData['masqurade_parent_id'] ?? null,
                'event_type' => $trailingData['event_type'] ?? null,
                'event_id' => $trailingData['event_id'] ?? null,
                'event_action' => $trailingData['event_action'] ?? 'create',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Error storing Masquerade Trailing Data: ' . $e->getMessage());
            return false;
        }
    }

    // PIMS - 13805    
    public static function  partnerWiseCustomerPermissions($partnerId)
    {
        $customerPermissions =  CustomerPortalPermission::where(["partner_id" => $partnerId, 'type' => '3'])->orderBy('id', 'DESC')->get();
        if ($customerPermissions->count() > 0) {
            return $customerPermissions;
        }
        return false;
    }

    public static function getCustomMessage($slug, $facility_id, $partnerId = null)
    {
        $message = false;
        if (!empty($facility_id) && !empty($partnerId)) {
            $CustomText =  CustomText::where(["partner_id" => $partnerId, 'facility_id' => $facility_id, 'slug' => $slug])->first();
        } else if (!empty($facility_id)) {
            $CustomText =  CustomText::where(['facility_id' => $facility_id, 'slug' => $slug])->first();
        }

        if (!$CustomText) {
            $CustomText =  CustomText::where(["partner_id" => $partnerId, 'slug' => $slug])->first();
        }

        if (isset($CustomText->message)) {
            $message =  $CustomText->message;
        }
        return $message;
    }
}
