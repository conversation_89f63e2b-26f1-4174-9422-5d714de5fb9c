<?php

namespace App\Http\Helpers;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class MailHelper
{
    public static function sendEmail($to, $view, $data, $partnerId, $fileType = null)
    {
        Log::info('Check Data');
        Log::info('Partner ID : ' . $partnerId);
        $subject = $data['subject'];
        Log::info('Subject Line : ' . $subject);
        // dd($data);

        // Send the email
        // check for attachement
        $attachementArray = [];
        if (isset($data['filedata']['type']) && !empty($data['filedata']['type'])) {
            Log::info('check file content');
            Log::info($data['filedata']['type']);
            $attachementArray['type']           = $data['filedata']['type'];
            $attachementArray['content']        = $data['filedata']['content'];
            $attachementArray['filename']       = $data['filedata']['filename'];
            $attachementArray['format']         = $data['filedata']['format'];
        }
        Log::info('Email sent to user : ' . json_encode($to));
        Log::info('Print data before sent to mail');
        // Log::info($data);

        Mail::send($view, $data, function ($message) use ($to, $subject, $attachementArray, $partnerId) {
            Log::info('check attachementArray');

            $message->to($to);
            $message->subject($subject);

            if ($partnerId == '215900') {
                Log::info('Check Partner ID  : ' . $partnerId);
                $message->from('<EMAIL>', 'RevPASS');
            } else {
                Log::info('Check Partner ID  2 : ' . $partnerId);
                $message->from('<EMAIL>', 'ParkEngage');
            }

            if (isset($attachementArray['type']) && $attachementArray['type'] == 'runtime') {
                Log::info('File Format : ' . $attachementArray['format']);
                if ($attachementArray['format'] == 'pdf') {
                    $pdf        = $attachementArray['content'];
                    $fileName   = $attachementArray['filename'];
                    Log::info('File Name :  ' . $fileName);
                    $message->attachData($pdf, $fileName);
                }
            } else if (isset($attachementArray['type']) && $attachementArray['type'] == 'saved') {
                Log::info('check attachementArray ELSE CASE');
                $path_to_file = $attachementArray['filename'];
                if (file_exists($path_to_file)) {
                    Log::info("check attachementArray with saved data : {$path_to_file}");
                    $message->attach($path_to_file);
                }
            }
        });
    }

    public static function backup_05_02_2025sendEmail($to, $view, $data, $partnerId, $fileType = null)
    {
        // Load the appropriate mail configuration
        if ($partnerId == '215900') {
            Config::set('mail.driver', 'smtp');
            Config::set('mail.host', config('mail.mailers.revpass.host'));
            Config::set('mail.port', config('mail.mailers.revpass.port'));
            Config::set('mail.username', config('mail.mailers.revpass.username'));
            Config::set('mail.password', config('mail.mailers.revpass.password'));
            Config::set('mail.encryption', config('mail.mailers.revpass.encryption'));
            Config::set('mail.from.address', config('mail.mailers.revpass.from.address'));
            Config::set('mail.from.name', config('mail.mailers.revpass.from.name'));
        } else {
            Config::set('mail.driver', 'smtp');
            Config::set('mail.host', config('mail.mailers.parkengage.host'));
            Config::set('mail.port', config('mail.mailers.parkengage.port'));
            Config::set('mail.username', config('mail.mailers.parkengage.username'));
            Config::set('mail.password', config('mail.mailers.parkengage.password'));
            Config::set('mail.encryption', config('mail.mailers.parkengage.encryption'));
            Config::set('mail.from.address', config('mail.mailers.parkengage.from.address'));
            Config::set('mail.from.name', config('mail.mailers.parkengage.from.name'));
        }

        // Send the email
        Log::info('Check Data');
        $subject = $data['subject'];
        Log::info($subject);

        // check for attachement
        $attachementArray = [];
        if (isset($data['filedata']['type']) && !empty($data['filedata']['type'])) {
            Log::info('check file content');
            Log::info($data['filedata']['type']);
            $attachementArray['type']           = $data['filedata']['type'];
            $attachementArray['content']        = $data['filedata']['content'];
            $attachementArray['filename']       = $data['filedata']['filename'];
            $attachementArray['format']         = $data['filedata']['format'];
        }
        Log::info($to);
        Log::info('Print data before sent to mail');
        Log::info($data);
        Mail::send($view, $data, function ($message) use ($to, $subject, $attachementArray) {
            Log::info('check attachementArray');
            Log::info($attachementArray);
            $message->to($to);
            $message->subject($subject);


            if (isset($attachementArray['type']) && $attachementArray['type'] == 'runtime') {
                Log::info('File Format : ' . $attachementArray['format']);
                if ($attachementArray['format'] == 'pdf') {
                    $pdf        = $attachementArray['content'];
                    $fileName   = $attachementArray['filename'];
                    Log::info('File Name :  ' . $fileName);
                    $message->attachData($pdf, $fileName);
                }
            } else if (isset($attachementArray['type']) && $attachementArray['type'] == 'saved') {
                Log::info('check attachementArray ELSE CASE');
                $path_to_file = $attachementArray['filename'];
                if (file_exists($path_to_file)) {
                    Log::info("check attachementArray with saved data : {$path_to_file}");
                    $message->attach($path_to_file);
                }
            }
        });
    }
}
