<?php

namespace App\Http\Helpers;

use App\Models\Facility;
use App\Models\OauthClient;
use Carbon\Carbon;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\ParkEngage\OverstayTicket;
use App\Models\ParkEngage\PartnerConfiguration;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\PartnerPaymentGateway;
use App\Models\Ticket;
use App\Models\ParkEngage\TicketTemporaryDetail;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\Reservation;
use App\Models\AuthorizeNetTransaction;
use App\Models\AllTransactions;
use App\Models\AllFailedTransactions;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use Illuminate\Support\Facades\Log;


class GatewayHelper
{
    public static function getEcommerceMID($facility, $paymetGatewayType)
    {
        $ecomMid = '';
        if ($paymetGatewayType == '1') {
            $ecomMid = $facility->FacilityPaymentDetails->planet_merchant_id;
        } elseif ($paymetGatewayType == '2') {
            $ecomMid = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
        } elseif ($paymetGatewayType == '4') {
            $ecomMid = $facility->FacilityPaymentDetails->heartland_mid;
        }
        return $ecomMid;
    }
    public static function getPartnerMID($paymentMID, $partnerId)
    {
        $partnerMID = PartnerPaymentGateway::where(['payment_mid' => $paymentMID, 'partner_id' => $partnerId])->first();
        return isset($partnerMID->id) ? $partnerMID->id : false;
    }

    public static function getRegisterUserProfileId($gatewayType, $cardLastFour, $cardExpiry, $userId, $facility)
    {
        $partnerId = $facility->owner_id;
        $pgRowID = self::getPartnerMID(self::getEcommerceMID($facility, $gatewayType), $partnerId);
        if (!$pgRowID) {
            return false;
        }
        // dd($facility->id, $facility->FacilityPaymentDetails, $gatewayType,  self::getEcommerceMID($facility, $gatewayType), $partnerId, $pgRowID);
        switch ($gatewayType) {
            case '1':
                $cardDetails = PlanetPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'expiry' => $cardExpiry, 'partner_id' => $partnerId, 'partner_payment_gateway_id' => $pgRowID])->whereNull('deleted_at')->latest()->first();
                break;
            case '2':
                $cardDetails = DatacapPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'expiry' => $cardExpiry, 'partner_id' => $partnerId, 'partner_payment_gateway_id' => $pgRowID])->whereNull('deleted_at')->latest()->first();
                break;
            case '4':
                $cardDetails = HeartlandPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'expiry' => $cardExpiry, 'partner_id' => $partnerId, 'partner_payment_gateway_id' => $pgRowID])->whereNull('deleted_at')->latest()->first();
                break;
            default:
                Log::error("No Card Found for GateWayType : {$gatewayType} User ID : {$userId} and Last Four : {$cardLastFour} : Exppiry : {$cardExpiry} ");
                return false;
                break;
        }
        return $cardDetails;
    }

    public static function removeAllDefaultCard($partnerId, $user_id, $gatewayType = null)
    {

        // Remove Other card from default
        if ($gatewayType == '1') {
            PlanetPaymentProfile::where(['partner_id' => $partnerId, 'user_id' => $user_id])->update(array('is_default' => 0));
        } else if ($gatewayType == '2') {
            DatacapPaymentProfile::where(['partner_id' => $partnerId, 'user_id' => $user_id])->update(array('is_default' => 0));
        } else if ($gatewayType == '4') {
            HeartlandPaymentProfile::where(['partner_id' => $partnerId, 'user_id' => $user_id])->update(array('is_default' => 0));
        } else {
            PlanetPaymentProfile::where(['partner_id' => $partnerId, 'user_id' => $user_id])->update(array('is_default' => 0));
            DatacapPaymentProfile::where(['partner_id' => $partnerId, 'user_id' => $user_id])->update(array('is_default' => 0));
            HeartlandPaymentProfile::where(['partner_id' => $partnerId, 'user_id' => $user_id])->update(array('is_default' => 0));
        }
        return true;
        //  !!!!

    }

    public static function getAllRegisterUserProfiles($gatewayType, $userId, $facility)
    {
        $partnerId = $facility->owner_id;
        $pgRowID = self::getPartnerMID(self::getEcommerceMID($facility, $gatewayType), $partnerId);
        if (!$pgRowID) {
            return false;
        }
        // dd($facility->id, $facility->FacilityPaymentDetails, $gatewayType,  self::getEcommerceMID($facility, $gatewayType), $partnerId, $pgRowID);
        switch ($gatewayType) {
            case '1':
                $cardDetails = PlanetPaymentProfile::where(['user_id' => $userId, 'partner_id' => $partnerId, 'partner_payment_gateway_id' => $pgRowID])->whereNull('deleted_at')->get();
                break;
            case '2':
                $cardDetails = DatacapPaymentProfile::where(['user_id' => $userId, 'partner_id' => $partnerId, 'partner_payment_gateway_id' => $pgRowID])->whereNull('deleted_at')->get();
                break;
            case '4':
                $cardDetails = HeartlandPaymentProfile::where(['user_id' => $userId, 'partner_id' => $partnerId, 'partner_payment_gateway_id' => $pgRowID])->whereNull('deleted_at')->get();
                break;
            default:
                Log::error("No Card Found for GateWayType : {$gatewayType} User ID : {$userId} ");
                return false;
                break;
        }
        return $cardDetails;
    }
}
