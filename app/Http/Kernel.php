<?php

namespace App\Http;

use App\Http\Middleware\PartnerLoggingMiddleware;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \LucaDegasperi\OAuth2Server\Middleware\OAuthExceptionHandlerMiddleware::class,
        \App\Http\Middleware\Preflight::class,
        \App\Http\Middleware\ForceSSL::class,
        \App\Http\Middleware\TrackForeignRequest::class
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        ],

        'api' => [
            'throttle:60,1',
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'monthlyparkinguserauth' => \App\Http\Middleware\MonthlyParkingUserAuthentication::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'facility' => \App\Http\Middleware\FacilityMiddleware::class,
        'user' => \App\Http\Middleware\UserMiddleware::class,
        'oauth' => \LucaDegasperi\OAuth2Server\Middleware\OAuthMiddleware::class,
        'oauth-user' => \LucaDegasperi\OAuth2Server\Middleware\OAuthUserOwnerMiddleware::class,
        'oauth-client' => \LucaDegasperi\OAuth2Server\Middleware\OAuthClientOwnerMiddleware::class,
        'check-authorization-params' => \LucaDegasperi\OAuth2Server\Middleware\CheckAuthCodeRequestMiddleware::class,
        'fetch-user' => \App\Http\Middleware\UserFetchMiddleware::class,
        'csrf' => \App\Http\Middleware\VerifyCsrfToken::class,
        'apiresponse' => \App\Http\Middleware\ApiResponseMiddleware::class,
        'role' => \Zizaco\Entrust\Middleware\EntrustRole::class,
        'permission' => \Zizaco\Entrust\Middleware\EntrustPermission::class,
        'ability' => \Zizaco\Entrust\Middleware\EntrustAbility::class,
        'rate' => \App\Http\Middleware\RateMiddleware::class,
        'ratecategory' => \App\Http\Middleware\RateCategoryMiddleware::class,
        'pagination' => \App\Http\Middleware\PaginationMiddleware::class,
        'legacy-auth' => \App\Http\Middleware\LegacyAuthenticationMiddleware::class,
        'coupon-logging' => \App\Http\Middleware\VisitorCouponMiddleware::class,
        'visitor' => \App\Http\Middleware\VisitorMiddleware::class,
        'auth-or-anon' =>  \App\Http\Middleware\GetAuthedOrAnonUser::class,
        'partner-logging' => \App\Http\Middleware\PartnerLoggingMiddleware::class,
        'checkpermission' => \App\Http\Middleware\CheckPermissionMiddleware::class,
        'prevent-back-history' => \App\Http\Middleware\PreventBackHistory::class,
        'timezone' => \App\Http\Middleware\TimezoneMiddleware::class,
    ];
}
