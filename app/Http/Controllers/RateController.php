<?php

namespace App\Http\Controllers;

use App;
use App\Classes\CsvRate;
use App\Models\RateCategory;
use App\Models\Facility;
use App\Models\Rate;
use App\Http\Requests;
use App\Models\RateType;
use App\Models\Partner;
use App\Models\FacilityRate;
use Illuminate\Http\Request;
use App\Exceptions\NotFoundException;
use App\Exceptions\FileFormatException;
use GuzzleHttp\Client;
use Carbon\Carbon;
use App\Classes\Inventory;
use App\Exceptions\ApiGenericException;
use App\Models\FacilityAvailability;
use App\Models\CustomText;
use App\Models\OauthClient;
use App\Models\ParkEngage\RatePass;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Models\PartnerRateType;
use App\Models\ParkEngage\UserMembership;
use App\Models\Reservation;
use App\Models\Ticket;
use App\Models\User;
use App\Models\ParkEngage\ParkingDevice;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\FacilityConfiguration;
use Illuminate\Support\Facades\Auth;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\ParkEngage\Gate;
use App\Models\PromoCode;
use App\Models\PromoUsage;
use App\Models\RateDefCustom;
use App\Models\ReservationHistroy;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use GuzzleHttp\Psr7\Query;

class RateController extends Controller
{
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const IS_FACILITY_CLOSED  = 1;
    const IS_FACILITY_OPEN  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "All slots are sold out at this moment.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    public $request;
    public $log;
    public $reservationRate;
    public $latestPromotionController;
    protected $customerPortalPermitions;
    protected $partnerId;
    protected $customMessage;

    public function __construct(Request $request, LoggerFactory $logFactory, LatestPromotionController $latestPromotionController)
    {
        $this->request = $request;
        $this->log = $logFactory->setPath('logs/rate')->createLogger('rate');
        $this->reservationRate = $logFactory->setPath('logs/roc/rate')->createLogger('rate');
        $this->latestPromotionController = $latestPromotionController;
        $this->customerPortalPermitions = null;
        $this->partnerId        = 0;
        $this->customMessage    = false;
    }


    public function index()
    {
        return Rate::with('rateType', 'facility', 'category', 'partner')->get();
    }

    public function show(Rate $rate)
    {
        return $rate->load('partner', 'rateType', 'facility', 'event', 'eventCategory');
    }

    /**
     * Get rate for a specific reservation.
     * Request should include arrival timestamp and length of stay to get
     * single rate for the facility
     *
     * $request->arrival_time should be of format 2016-07-18 21:00:00
     * $request->length_of_stay should be a regular integer
     *
     * @param  Request  $request  [description]
     * @param  Facility $facility [description]
     * @return [type]             [description]
     */
    public function getFacilityRate(Request $request, Facility $facility)
    {
        $this->validate(
            $request,
            [
                'arrival_time' => 'required|string',
                'length_of_stay' => 'required|numeric',
                'use_bonus' => 'boolean'
            ]
        );

        $coupon_threshold_price = self::DEFAULT_VALUE_STR;
        $is_coupon_threshold_applied = self::DEFAULT_VALUE_STR;
        $is_coupon_threshold_price_percentage = self::DEFAULT_VALUE_STR;
        $device_type = self::DEFAULT_VALUE;

        /** this function is used to get Availability Information for respective facility **/
        $rateData = $this->updateRateInformationWithAvailibilty($request, $facility);

        if ((isset($rateData['is_coupon_threshold_applied'])) && ($rateData['is_coupon_threshold_applied'] > 0)) {
            $is_coupon_threshold_applied = 1;
        }

        if ((isset($rateData['coupon_threshold_price'])) && ($rateData['coupon_threshold_price'] > 0) && ($is_coupon_threshold_applied == self::DEFAULT_VALUE_ONE)) {
            $coupon_threshold_price = $rateData['coupon_threshold_price'];
        }

        if ((isset($rateData['is_coupon_threshold_price_percentage'])) && ($rateData['is_coupon_threshold_price_percentage'] > 0)) {
            $is_coupon_threshold_price_percentage = 1;
        }

        $bonusRequest = [];
        if ($request->use_bonus) {
            $bonusRequest['is_filter'] = $request->is_filter ? true : false;
            $bonusRequest['price'] = isset($request->price) ? $request->price : 0;
        }
        // to handle not to add threshold or check availability on payment page.
        if ((isset($request->coupon_threshold_price)) && ($request->coupon_threshold_price > self::DEFAULT_VALUE)) {
            $coupon_threshold_price = $request->coupon_threshold_price;
            $is_coupon_threshold_price_percentage = self::DEFAULT_THRESHOLD_FLAG_IGNORE;
        }

        if ((isset($request->device_type)) && ($request->device_type == self::DEFAULT_WEB)) {
            $device_type = self::DEFAULT_VALUE_ONE;
        }
        $lengthOfStay = $request->length_of_stay;
        if (!isset($request->length_of_stay)) {
            $lengthOfStay = 24;
        }

        //rate for resrvation
        //$rate = $facility->rateForReservation($request->arrival_time,$lengthOfStay , $request->use_bonus , false,  null, false, $bonusRequest, $coupon_threshold_price, $is_coupon_threshold_price_percentage);
        $isMember = 0;
        $rate = $facility->rateForReservationOnMarker($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
        //return current availabilit      
        if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
            $rate['availability'] = $rateData['availability'];
        } else {
            $rate['availability'] = self::DEFAULT_VALUE;
        }

        //if price is coming n/A it may be because if not working hours of facility so set avalability as 0
        if ($rate['price'] == 'N/A') {
            $rate['availability'] = self::DEFAULT_VALUE;
            $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
        }


        if ((isset($rate['coupon_price'])) && ($rate['coupon_price'] > self::DEFAULT_VALUE)) {
            $rate['coupon_price_applied'] = "$" . number_format($rate['coupon_price'], 0) . " WITH COUPON";
        } else {
            //return if coupon price got applied
            $rate['coupon_price_applied'] = self::DEFAULT_VALUE;
        }


        //returning  message as per availibility 
        $rate['availability_msg'] = '';
        $rate['availability_msg_some_space'] = '';
        if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
            if ($rate['availability'] == self::DEFAULT_VALUE) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
            } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
            } else {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
            }
        } else if ($rate['price'] == 'N/A') {
            $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
            $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
        } else if ($rate['availability'] == self::DEFAULT_VALUE) {
            $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
            $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
        }


        //price to show on facility details page if availibity is 0.
        if ((isset($rate['availability'])) && ($rate['availability'] > 0)) {
            $rate['price_without_availibility'] = self::DEFAULT_VALUE;
        } else {
            $rate['price_without_availibility'] = ($rate['price'] != 'N/A') ? number_format($rate['price']) : $rate['price'];
            //as per discussion with sir/mam we need to disable check on avalability on payment page
            if ($device_type < self::DEFAULT_VALUE_ONE) {
                $rate['price'] = 'N/A';
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            }
        }

        if (!$rate) {

            throw new NotFoundException('No valid rates for those parameters.');
        }

        if ($rate['price'] != 'N/A') {
            $rate['price'] = number_format($rate['price'], 2);
        }

        return $rate;
    }



    public function getFacilityRateOnSideMap(Request $request)
    {
        $this->validate(
            $request,
            [
                'arrival_time' => 'required|string',
                'length_of_stay' => 'required|numeric',
                'use_bonus' => 'boolean'
            ]
        );

        $arrayToReturn = array();
        $facilitiesInputs = isset($request->facilities) ? $request->facilities : 0;
        if ($facilitiesInputs != 0) {
            $facilitiesIds = explode(",", $facilitiesInputs);
            if (count($facilitiesIds) > 0) {
                $facilityCount = 0;
                foreach ($facilitiesIds as $facilityId) {
                    $facilityData = Facility::find($facilityId);
                    $arrayToReturn[$facilityCount] = $this->getFacilityRateOnSideMapMarker($request, $facilityData);
                    $facilityCount++;
                }
            }
        }
        return $arrayToReturn;
    }

    public function getFacilityRateOnSideMapMarker(Request $request, Facility $facility)
    {

        $rateData = $this->updateRateInformationWithAvailibiltySideMap($request, $facility);
        $coupon_threshold_price = 0;
        $is_coupon_threshold_applied = 0;
        $is_coupon_threshold_price_percentage = 0;

        if ((isset($rateData['is_coupon_threshold_applied'])) && ($rateData['is_coupon_threshold_applied'] > 0)) {
            $is_coupon_threshold_applied = 1;
        }

        if ((isset($rateData['coupon_threshold_price'])) && ($rateData['coupon_threshold_price'] > 0) && ($is_coupon_threshold_applied == self::DEFAULT_VALUE_ONE)) {
            $coupon_threshold_price = $rateData['coupon_threshold_price'];
        }

        if ((isset($rateData['is_coupon_threshold_price_percentage'])) && ($rateData['is_coupon_threshold_price_percentage'] > 0)) {
            $is_coupon_threshold_price_percentage = 1;
        }

        $rate = $facility->rateForReservationSideMap($request->arrival_time, $request->length_of_stay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage);

        //return current availabilit
        if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
            $rate['availability'] = $rateData['availability'];
        } else {
            $rate['availability'] = self::DEFAULT_VALUE;
        }

        //if price is coming n/A it may be because if not working hours of facility so set avalability as 0
        if ($rate['price'] == 'N/A') {
            $rate['availability'] = self::DEFAULT_VALUE;
        }

        //        if(($coupon_threshold_price > self::DEFAULT_VALUE) && ($rate['availability'] > self::DEFAULT_VALUE))
        //        {
        //          
        //            if($is_coupon_threshold_price_percentage!=1)
        //            {
        //              $rate['coupon_threshold_price'] = (string)round($coupon_threshold_price,0);  
        //            }else{
        //                 $rate['coupon_threshold_price'] = (string)round((($rate['price'] * $coupon_threshold_price)/100),0);
        //            }
        //            $rate['is_coupon_threshold_applied'] = self::DEFAULT_VALUE_ONE;  
        //        }else{
        //              $rate['coupon_threshold_price'] = self::DEFAULT_VALUE_STR;  
        //              $rate['is_coupon_threshold_applied'] = self::DEFAULT_VALUE;          
        //        }

        //return if coupon price get applied
        if ((isset($rate['coupon_price'])) && ($rate['coupon_price'] > self::DEFAULT_VALUE)) {
            $rate['coupon_price_applied'] = "$" . number_format($rate['coupon_price'], 0) . " WITH COUPON";
        } else {
            $rate['coupon_price_applied'] = self::DEFAULT_VALUE;
        }


        //returning  message as per availibility 
        $rate['availability_msg'] = '';
        $rate['availability_msg_some_space'] = '';
        if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
            if ($rate['availability'] == self::DEFAULT_VALUE) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
            } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
            } else {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
            }
        } else if ($rate['price'] == 'N/A') {
            $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
            $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
        } else if ($rate['availability'] == self::DEFAULT_VALUE) {
            $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
            $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
        }


        //price to show on facility details page if availibity is 0.
        if ((isset($rate['availability'])) && ($rate['availability'] > 0)) {
            $rate['price_without_availibility'] = self::DEFAULT_VALUE;
        } else {
            $rate['price_without_availibility'] = ($rate['price'] != 'N/A') ? number_format($rate['price']) : $rate['price'];
            $rate['price'] = 'N/A';
        }

        if (!$rate) {

            throw new NotFoundException('No valid rates for those parameters.');
        }

        if ($rate['price'] != 'N/A') {
            $rate['price'] = number_format($rate['price'], 2);
        }

        if ((!isset($rate['coupon_threshold_price'])) || (($rate['coupon_threshold_price']) < 0)) {
            $rate['coupon_threshold_price'] =  self::DEFAULT_VALUE;
        }


        return $rate;
    }

    public function getFacilityRateOnMapMarker(Request $request, Facility $facility)
    {
        $this->log->info("Get Rate Request: " . json_encode($request->all()));
        try {
            $this->validate(
                $request,
                [
                    'arrival_time' => 'required|string',
                    'length_of_stay' => 'required|numeric',
                    'use_bonus' => 'boolean'
                ]
            );
            $total_amount = $parking_amount = $payable_amount = $amount_paid = $discount = 0;

            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                $this->partnerId = $secret->partner_id;

                if (!Facility::where('id', $facility->id)->where('owner_id', $secret->partner_id)->first()) {
                    throw new NotFoundException('No garage found with this partner.');
                }
            }

            $rateData = $this->updateRateInformationWithAvailibilty($request, $facility);
            $this->log->info("updateRateInformationWithAvailibilty : " . json_encode($rateData));
            $coupon_threshold_price = 0;
            $is_coupon_threshold_applied = 0;
            $is_coupon_threshold_price_percentage = 0;

            if ((isset($rateData['is_coupon_threshold_applied'])) && ($rateData['is_coupon_threshold_applied'] > 0)) {
                $is_coupon_threshold_applied = 1;
            }

            if ((isset($rateData['coupon_threshold_price'])) && ($rateData['coupon_threshold_price'] > 0) && ($is_coupon_threshold_applied == self::DEFAULT_VALUE_ONE)) {
                $coupon_threshold_price = $rateData['coupon_threshold_price'];
            }

            if ((isset($rateData['is_coupon_threshold_price_percentage'])) && ($rateData['is_coupon_threshold_price_percentage'] > 0)) {
                $is_coupon_threshold_price_percentage = 1;
            }

            $lengthOfStay = $request->length_of_stay;

            if (!isset($request->length_of_stay)) {
                $lengthOfStay = 24;
            }

            if ($facility->owner_id == 45) {
                $lengthOfStay = 23.59;
            }

            $refundFlag = false;
            $updateRequestWithNoTimeChange = false;
            $appliedProcessingFee = 0;
            if ($facility->is_service_update == '1' && isset($request->reservation_id)) { // indicate service update enable
                $reservation = Reservation::find($request->reservation_id);
                $discount = $reservation->getDiscountedValue($request->reservation_id);
                $appliedProcessingFee = $reservation->getAppliedProcessingFees($request->reservation_id);
                if ($request->length_plus_minus == '1') {
                } else if ($request->length_plus_minus == '0') {
                    $refundFlag = true;
                } else {
                    $lengthOfStay = $request->length_of_stay;
                    if (isset($request->length_plus_minus)) {
                        $updateRequestWithNoTimeChange =  true;
                    }
                }
                $lengthOfStay = $request->length_of_stay;
            }

            $isMember = isset($request->is_member) ? $request->is_member : 0;
            if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                $this->log->info("function rateForReservationByPassRateEngine");
                $rate = $facility->rateForReservationByPassRateEngine($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
            } else {
                $this->log->info("function rateForReservationOnMarker");
                $rate = $facility->rateForReservationOnMarker($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
            }

            //return current availabilit
            $parking_amount = $rate['price'];
            $this->log->info("Parking Amount: $parking_amount, Length of Stay: $lengthOfStay");

            $rate['isFacilityClosed'] = self::IS_FACILITY_OPEN;
            if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                $rate['availability'] = $rateData['availability'];
            } else {
                $rate['availability'] = self::DEFAULT_VALUE;
            }

            //if price is coming n/A it may be because if not working hours of facility so set avalability as 0
            if ($rate['price'] == 'N/A') {
                $rate['availability'] = self::DEFAULT_VALUE;
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            }
            //        if(($coupon_threshold_price > self::DEFAULT_VALUE) && ($rate['availability'] > self::DEFAULT_VALUE))
            //        {
            //          
            //            if($is_coupon_threshold_price_percentage!=1)
            //            {
            //              $rate['coupon_threshold_price'] = (string)round($coupon_threshold_price,0);  
            //            }else{
            //                 $rate['coupon_threshold_price'] = (string)round((($rate['price'] * $coupon_threshold_price)/100),0);
            //            }
            //            $rate['is_coupon_threshold_applied'] = self::DEFAULT_VALUE_ONE;  
            //        }else{
            //              $rate['coupon_threshold_price'] = self::DEFAULT_VALUE_STR;  
            //              $rate['is_coupon_threshold_applied'] = self::DEFAULT_VALUE;          
            //        }

            //return if coupon price get applied
            if ((isset($rate['coupon_price'])) && ($rate['coupon_price'] > self::DEFAULT_VALUE)) {
                $rate['coupon_price_applied'] = "$" . number_format($rate['coupon_price'], 0) . " WITH COUPON";
            } else {
                $rate['coupon_price_applied'] = self::DEFAULT_VALUE;
            }

            $facilityConfig = FacilityConfiguration::where("facility_id", $facility->id)->first();
            // dd($this->partnerId,  $facilityConfig->is_inventory_check, $facility->id, $rate['availability'] == self::DEFAULT_VALUE, $rate['availability'] < self::MIN_AVAILABILITY);

            //returning  message as per availibility 
            $rate['availability_msg'] = '';
            $rate['availability_msg_some_space'] = '';
            if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
                // Lokesh: Inventory check for availability 

                if (isset($facilityConfig->is_inventory_check) && !$facilityConfig->is_inventory_check) {
                    $rate['availability'] = 1;
                }


                if ($rate['availability'] == self::DEFAULT_VALUE) {
                    // PIMS - 13805 VP : 06-05-2025 
                    $this->customMessage = QueryBuilder::getCustomMessage('zero-slot', $facility->id);

                    if ($this->customMessage != false) {
                        $rate['availability_msg'] = $this->customMessage;
                    } else {
                        $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                        $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
                    }
                } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
                } else {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                    $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                }
            } else if ($rate['price'] == 'N/A') {
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
            } else if ($rate['availability'] == self::DEFAULT_VALUE) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
            }
            if (!isset($facilityConfig->is_inventory_check) || $facilityConfig->is_inventory_check <= 0) {
                $rate['availability'] = 1;
                $rate['availability_msg'] = 'No inventory check.';
            }

            //price to show on facility details page if availibity is 0.
            if ((isset($rate['availability'])) && ($rate['availability'] > 0)) {
                $rate['price_without_availibility'] = self::DEFAULT_VALUE;
            } else {
                $rate['price_without_availibility'] = ($rate['price'] != 'N/A') ? number_format($rate['price']) : $rate['price'];
                // commented by ujjwal for skip inventory hours of operation availability check 27-12-2023
                //    $rate['price'] = 'N/A';
                //  $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            }

            //change msg of facility close in worlport rate
            if (isset($rate['todayHoursOfOperation'])) {
                if ($rate['todayHoursOfOperation'] == '0' && $rate['twenty_four_hours'] == '0') {
                    $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
                }
            }

            if (!$rate) {
                throw new NotFoundException('No valid rates for those parameters.');
            }


            if ($rate['price'] != 'N/A') {
                $rate['price'] = number_format($rate['price'], 2);
            }

            if ((!isset($rate['coupon_threshold_price'])) || (($rate['coupon_threshold_price']) < 0)) {
                $rate['coupon_threshold_price'] =  self::DEFAULT_VALUE;
            }

            $rate['facility_hours_of_operation'] = $facility->hoursOfOperation()->orderBy('day_of_week')->get();

            if ($facility->is_available == '2' || $facility->is_available == 2) {
                $rate['is_available_msg'] = self::FACILITY_NOT_AVAILABLE;
            } elseif ($facility->is_available == '3' || $facility->is_available == 3) {
                $rate['is_available_msg'] = self::FACILITY_COMING_SOON;
            } else {
                $rate['is_available_msg'] = self::FACILITY_AVAILABLE;
            }


            $facilityrate = Rate::select('max_stay', 'price')->where('facility_id', $facility->id)->where('active', 1)->get();

            if (count($facilityrate) > 0) {
                foreach ($facilityrate as $value) {
                    $rate['facility_rates'][] = '$' . $value->price . ' for ' . $value->max_stay . ' hours';
                }
                $rate['facility_rates'] = implode(', ', $rate['facility_rates']);
            }

            // tax rate for amount and percentage calculation
            $reservationAmount = 0;

            $tax_rate = $facility->getTaxRate($rate);
            $processingFee = $facility->getProcessingFee('1');
            if ($facility->is_service_update == '1' && isset($request->reservation_id))
                $processingFee = 0;

            if ($tax_rate > 0) {
                // $tax_rate = (($rate['price'] * $facility->tax_rate) / 100);
                $rate['tax_fee'] = $tax_rate;
            } else {
                $rate['tax_fee'] = '0.00';
            }
            if ($rate['price'] <= 0) {
                $rate['tax_fee'] = '0.00';
                $rate['facility']['processing_fee'] = '0.00';
            }
            $rate['tax_fee'] =  $parking_amount > 0 ? $tax_rate : '0.00';
            $rate['processing_fee'] =  $parking_amount > 0 ?  $processingFee : '0.00';

            // Vijay Deployed : 12-03-2024 : ROOC Respect change Start 
            $reservationAmount          = $parking_amount > 0 ? ($parking_amount + $tax_rate + $processingFee + $appliedProcessingFee - $discount) : '0';
            $rate['refundFlag']         = $refundFlag;
            $rate['total']              = sprintf("%.2f", $reservationAmount);
            $rate['amount_paid']        = $amount_paid;
            $rate['parking_amount']     = sprintf("%.2f", $parking_amount);
            $rate['payable_amount']     = sprintf("%.2f", $reservationAmount);

            if ($refundFlag) {
                $rate['refund_amount']  = sprintf("%.2f", $reservation->total - $reservationAmount);
                $rate['payable_amount'] = '0.00';
                $rate['amount_paid']    = $reservation->total;
                $rate['refundFlag']     = $refundFlag;
            } else if ($refundFlag == false && $request->length_plus_minus == '1') {
                $rate['total']          = sprintf("%.2f", $reservationAmount);
                $rate['amount_paid']    = sprintf("%.2f", $reservation->total);
                $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
                $rate['payable_amount'] = ($reservationAmount >= $reservation->total) ? sprintf("%.2f", ($reservationAmount - $reservation->total)) : sprintf("%.2f", ($reservation->total - $reservationAmount));
            }
            if ($updateRequestWithNoTimeChange) {
                $rate['total']          = sprintf("%.2f", $reservationAmount);
                $rate['amount_paid']    = sprintf("%.2f", $reservation->total);
                $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
                $rate['payable_amount'] = sprintf("%.2f", self::DEFAULT_VALUE);
            }

            // !!! Close here .

            if (isset($request->user_id) && $request->user_id != '') {
                if (isset($rate['facility_id'])) {

                    $parkingDevice = ParkingDevice::where("facility_id", $rate['facility_id'])->first();
                    if ($parkingDevice) {
                        $rate['facility']['is_device_facility'] = '1';
                    } else {
                        $rate['facility']['is_device_facility'] = '0';
                    }



                    $reservations = Reservation::with('facility')->where("user_id", $request->user_id)->where("facility_id", $rate['facility_id'])->orderBy("id", "DESC")->get();

                    if (count($reservations) <= 0) {
                        $rate['facility']['is_reservation_found'] = '0';
                    } else {
                        $now = date("Y-m-d H:i:s");

                        foreach ($reservations as $reservation) {
                            $entryTime = $reservation->start_timestamp;


                            $exitTime = Carbon::parse($reservation->start_timestamp)->addMinutes(($reservation->length * 60));
                            if (strtotime($entryTime) <= strtotime($now) && strtotime($exitTime) >= strtotime($now)) {
                                $rate['facility']['is_reservation_found'] = '1';
                                $rate['facility']['reservation_code'] = $reservation->ticketech_guid;
                                $rate['facility']['iq_code'] = $reservation->ticketech_code;

                                $ticket = Ticket::where("reservation_id", $reservation->id)->orderBy("id", "DESC")->first();
                                if (!$ticket) {
                                    $rate['facility']['is_reservation_checkin_found'] = '0';
                                } else {
                                    $rate['facility']['is_reservation_checkin_found'] = '1';
                                }
                                break;
                            }
                        }
                    }
                } else {
                    $parkingDevice = ParkingDevice::where("facility_id", $rate['facility']->id)->first();
                    if ($parkingDevice) {
                        $rate['facility']['is_device_facility'] = '1';
                    } else {
                        $rate['facility']['is_device_facility'] = '0';
                    }
                    $rate['facility']['is_reservation_found'] = '0';
                    $rate['facility']['is_reservation_checkin_found'] = '0';
                }
            }
            $rate['facility_brand_setting'] = $facility->faciltyBrandSetting();
            $rate['discount'] = $discount;
            $rate['discount_amount'] = $discount;
            return $rate;
        } catch (\Exception $e) {
            $this->log->error("Error message" . $e);
            throw new ApiGenericException($e);
        }
    }

    /**
     * Create a new rate and return the saved rate
     */
    public function store(Request $request)
    {
        $this->log->info("Rate store( request " . json_encode($request->all()));
        if ($request->entry_time_begin > $request->exit_time_begin) {
            throw new NotFoundException('Entry time begin should less than exit time begin');
        }
        if ($request->entry_time_end > $request->exit_time_end) {
            throw new NotFoundException('Entry time end should less than exit time end');
        }
        $rate = new Rate();

        if ($request->is_device_specific_rate == '' || $request->is_device_specific_rate == NULL) {
            $request->request->add(['is_device_specific_rate' => '0']);
        }
        if ($request->bundle_pass == '1' || $request->bundle_pass == true) {
            $request->request->add(['pass_type_status' => '2']);
        } elseif ($request->multiple_entry_per_pass_event == '1' || $request->multiple_entry_per_pass_event == true) {
            $request->request->add(['pass_type_status' => '1']);
        } else {
            $request->request->add(['pass_type_status' => '0']);
        }

        if ($request->bundle_pass == '1' || $request->multiple_entry_per_pass_event == '1') {
            if (($request->event_category_id == '' || $request->event_category_id == '0') && ($request->event_id == '' || $request->event_id == '0')) {
                throw new NotFoundException('Event category should be selected.');
            }
        }

        if ($request->bundle_pass == '1' && $request->is_full_season == '1') {
            $eventCount = EventCategoryEvent::where('event_category_id', $request->event_category_id)->count();
            if ($eventCount > $request->total_usage) {
                throw new NotFoundException('You can not add pass more than total usage');
            }
        }

        $rate->fill($request->only($rate->getFillable()));

        $rate->save();
        //if custom rate band date is selected 
        if (isset($request->custom_date) && !empty($request->custom_date)) {
            $custom_date = $request->custom_date;
            foreach ($custom_date as $customDateResult) {
                if ($request->day_type == 0) {
                    $startDate = $customDateResult;
                    $endDate = $customDateResult;
                } elseif ($request->day_type == 1) {
                    $startDate = $customDateResult['start_date'];
                    $endDate = $customDateResult['end_date'];
                }
                $rate_applicable = new RateDefCustom();
                $rate_applicable->rate_id = $rate->id;
                $rate_applicable->rate_start_date = $startDate;
                $rate_applicable->rate_end_date = $endDate;
                $rate_applicable->day_type = $request->day_type;
                $rate_applicable->status = 1;
                $rate_applicable->facility_id = $request->facility_id;
                $rate_applicable->save();
            }
        }
        //end custom rate band 
        $savedRate = Rate::find($rate->id);
        $this->log->info("Rate saved " . json_encode($savedRate));
        return $savedRate;
    }

    /**
     * Delete the rate given in the path
     */
    public function destroy($id)
    {
        $this->log->info("Rate destroy( " . $id);
        $rate = Rate::find($id);

        $this->log->info("Rate before destroy " . json_encode($rate));

        if (!$rate) {
            throw new NotFoundException('No rate found with that ID');
        }

        $rate->delete();
        $this->log->info("Rate destroy ");
        return $rate;
    }

    /**
     * Update the rate given in the path,
     * and return the saved rate
     */
    public function update($id, Request $request)
    {
        $this->log->info("Rate update( request " . json_encode($request->all()));
        $rate = Rate::find($id);
        if (!$rate) {
            throw new NotFoundException('No rate found with that ID');
        }
        $this->log->info("Rate data " . json_encode($rate));
        if ($request->rate_type['rate_type'] == 'Member Rates' || $request->rate_type['rate_type'] == 'Non-Member Rates') {
            if ($request->active == '0') {
                throw new NotFoundException('You can not inactive the rates.');
            }
            /*if ($request->monday == '0' || $request->tuesday == '0' || $request->wednesday == '0' || $request->thursday == '0' || $request->friday == '0' || $request->saturday == '0' || $request->sunday == '0') {
                throw new NotFoundException('You can not change any day rates.');
            }*/
        }
        if ($request->bundle_pass == '1' || $request->multiple_entry_per_pass_event == '1') {
            if (($request->event_category_id == '' || $request->event_category_id == '0') && ($request->event_id == '' || $request->event_id == '0')) {
                throw new NotFoundException('Event category should be selected.');
            }
        }
        if ($request->bundle_pass == '1' && $request->is_full_season == '1') {
            $eventCount = EventCategoryEvent::where('event_category_id', $request->event_category_id)->count();
            if ($eventCount > $request->total_usage) {
                throw new NotFoundException('You can not update pass more than total usage');
            }
        }

        $rateArray = $request->only($rate->getFillable());
        if ($rateArray) {
            /*if($request->event_id != '0'){
                $rateArray['event_category_id'] = '0';
            }if($request->event_category_id != '0'){
                $rateArray['event_id'] = '0';
            }*/

            if ($request->bundle_pass == '1') {
                $request->request->add(['pass_type_status' => '2']);
                $rateArray['max_entry'] = '';
                $rateArray['pass_type_status'] = '2';
            } elseif ($request->multiple_entry_per_pass_event == '1') {
                $rateArray['max_entry'] = '';
                $rateArray['pass_type_status'] = '1';
            } else {
                $rateArray['pass_type_status'] = '0';
            }
            if (!empty($request->is_device_specific_rate) && $request->is_device_specific_rate == '3') {

                $rateArray['is_device_specific_rate'] = $request->is_device_specific_rate;
            }
        }
        //$rate->fill($request->only($rateArray));
        $rate->fill($rateArray);
        $rate->save();
        //update when custom date is selected here
        // if(isset($request->custom_date) && !empty($request->custom_date)){
        //   $getDublicateDate=count($request->custom_date) >= count(array_unique($request->custom_date)); 
        //   if($getDublicateDate){
        //     throw new NotFoundException('Please ensure each date entered is unique.');
        //   }
        // }
        // die;
        //@******** remove old rate id record************
        if (isset($request->remove_edit) && !empty($request->remove_edit)) {
            $affectedRows = RateDefCustom::where('facility_id', $request->facility_id)
                ->where('rate_id', $id)
                ->delete();
        }
        //@ ************ end remove old data*************
        if (isset($request->custom_date) && !empty($request->custom_date)) {
            //*********insert new record for date************ */
            //if custom rate band date is selected 
            $custom_date = $request->custom_date;
            foreach ($custom_date as $customDateResult) {

                if ($request->day_type == 0) {
                    $startDate = $customDateResult;
                    $endDate = $customDateResult;
                } elseif ($request->day_type == 1) {
                    $startDate = $customDateResult['start_date'];
                    $endDate = $customDateResult['end_date'];
                }

                //   $start_date = Carbon::parse($startDate);
                //   $end_date = Carbon::parse($endDate);
                //    if($request->day_type == 1){
                //        if ($end_date->lte($start_date)) {
                //            throw new NotFoundException('End date should be greater than start date');
                //        }
                //    }

                $rate_applicable = new RateDefCustom();
                $rate_applicable->rate_id = $id;
                $rate_applicable->rate_start_date = $startDate;
                $rate_applicable->rate_end_date = $endDate;
                $rate_applicable->day_type = $request->day_type;
                $rate_applicable->status = 1;
                $rate_applicable->facility_id = $request->facility_id;
                $rate_applicable->save();
            }
        }
        //end custom rate band 
        //    ********** end insert new record for date
        // end update custom date
        $savedRate = Rate::find($id);
        $this->log->info("Rate data after updated " . json_encode($savedRate));
        return $savedRate;
    }

    public function downloadAllFacilityRates(Facility $facility)
    {
        $rates = Rate::where('facility_id', $facility->id)->get();
        $csvRate = new CsvRate();
        $csvRate->downloadRatesCsv($rates);
    }

    public function downloadAllRates()
    {

        $rates = Rate::all();
        $csvRate = new CsvRate();
        $csvRate->downloadRatesCsv($rates);
    }

    public function updateAllRatesCsv(Request $request)
    {
        if ($request->hasFile('CSV')) {
            $csvRate = new CsvRate($request->file('CSV'));
            $status = $csvRate->updateRatesFromCsv($request);
            if (!$status) {
                throw new FileFormatException($csvRate->getErrorMessage());
            }
            $data['UPDATED'] = $csvRate->getUpdatedRates();
            $data['CREATED'] = $csvRate->getCreatedRates();
            $data['DELETED'] = $csvRate->getDeletedRates();
            $data['FAILED'] = $csvRate->getFailedRates();
            return $data;
        } else {
            throw new FileFormatException("Missing File");
        }
    }

    public function setCategory($rate, $request)
    {
        $category = $request->category;
        if ($category) {
            $model = RateCategory::firstOrNew(['category' => $request->category]);
            if (!$rate->id) {
                $rate->save();
            }
            $rate->category()->save($model);
        }
    }

    public function associateRateType($rate, $request)
    {
        $rate_type = $request->input('rate_type.rate_type');
        if ($rate_type) {
            $model = RateType::firstOrCreate(['rate_type' => $rate_type]);
            $rate->rate_type_id = $model->id;
            $rate->rateType()->associate($model);
        }
    }

    public function getFacilityRatesByCategory(Facility $facility)
    {
        $facilityRates = RateCategory::with(
            ['rates' => function ($query) use ($facility) {
                $query->where('facility_id', $facility->id)->orderby('description', 'ASC');
            }]
        )->get();

        return $facilityRates;
    }

    /*
    public function getFacilityRatesByType($facilityId)
    {
      $facility = Facility::where('id', $facilityId)->first();
      $partner = \DB::table('partner_rate_types')->where('partner_id', $facility->owner_id)->get();
      if($partner){
        $rateTypeId = [];
        foreach ($partner as $key => $value) {
          $rateTypeId[] = $value->rate_type_id;
        }
          return RateType::with(
              ['rates' => function ($query) use ($facilityId) {
                  $query->where('facility_id', $facilityId)->orderby('description', 'ASC');
              }]
          )->whereIN('id', $rateTypeId)->get();
      }else{
        return RateType::with(
            ['rates' => function ($query) use ($facilityId) {
                $query->where('facility_id', $facilityId)->orderby('description', 'ASC');
            }]
        )->get();
      }

    }
    */
    public function getFacilityRatesByType($facilityId)
    {

        $facility = Facility::where('id', $facilityId)->first();
        $partner = \DB::table('partner_rate_types')->where('partner_id', $facility->owner_id)->get();
        //return $partner;
        if ($partner) {
            $rateTypeId = [];
            foreach ($partner as $key => $value) {
                $rateTypeId[] = $value->rate_type_id;
            }
            return RateType::with(
                ['rates' => function ($query) use ($facilityId) {
                    $query->where('facility_id', $facilityId)->orderby('description', 'ASC');
                }]
            )->whereIN('id', $rateTypeId)->get();
        } else {

            $membership = UserMembership::where("user_id", $facility->owner_id)->first();
            //return $membership;
            $rateTypeId = [];
            if ($membership) {
                $permission = PartnerRateType::where("membership_plan_id", $membership->membership_plan_id)->get();
                //$permission = Permission::where("membership_plan_id", $membership->membership_plan_id)->where("type", '4')->get();

                if (count($permission) > 0) {
                    foreach ($permission as $key => $value) {
                        $rateTypeId[] = $value->rate_type_id;
                    }
                }
            }
            $result = RateType::with(
                [
                    'rates' => function ($query) use ($facilityId) {
                        $query->where('facility_id', $facilityId)->orderby('description', 'ASC');
                    }
                ]
            )->whereIn("id", $rateTypeId)->get();

            //handle rate id date changes
            $rateid = [];
            if (count($result) > 0) {
                foreach ($result as $k => $v) {
                    $partnerDetails = UserPaymentGatewayDetail::where("user_id", $facility->owner_id)->first();
                    $result[$k]->partner_slug = $partnerDetails->touchless_payment_url;
                }
                foreach ($result[0]['rates'] as $key) {
                    $rateid[] = $key['id'];
                }
                $resultArray = \DB::table('rate_definition_date')->whereIn('rate_id', $rateid)->get();
                // $data = json_decode($resultArray, true)['data'];
                $groupedData = [];
                foreach ($resultArray as $item) {
                    $rateId = $item->rate_id;
                    if (!isset($groupedData[$rateId])) {
                        $groupedData[$rateId] = [
                            'rate_id' => $rateId,
                            'rate_start_date' => [],
                            'rate_date_type' => $item->day_type
                        ];
                    }
                    $groupedData[$rateId]['rate_start_date'][] = [
                        'start_date' => $item->rate_start_date,
                        'end_date' => $item->rate_end_date
                    ];
                }
                $getRateDateArray =  array_values($groupedData);
                //add Key in $result Array if found in $getRateDateArray
                foreach ($getRateDateArray as $item2) {
                    $rateId = $item2['rate_id'];
                    foreach ($result[0]['rates'] as &$item1) {
                        if ($item1['id'] == $rateId) {
                            // Merge the rate_start_date information from $a2 to $a1
                            $item1['custom_date'] = $item2['rate_start_date'];
                            $item1['day_type'] = $item2['rate_date_type'];
                            break;
                        }
                    }
                }
            }


            //$result->membership_plan_id = $membership->membership_plan_id;
            return $result;
        }
    }

    public function getFacilityRatesByTypeMobile($facilityId)
    {
        $rate = [];
        $rate['facilities'] = Facility::with('photos')->where('id', $facilityId)->first();
        $rate['rate'] =  RateType::with(
            ['rates' => function ($query) use ($facilityId) {
                $query->where('facility_id', $facilityId)->orderby('price', 'ASC');
            }]
        )->get();

        return $rate;
    }

    public function getFacilityRatesByTypeTest($facilityId)
    {
        $rate_types["facilities"] = Facility::with('photos')->where('id', $facilityId)->first();

        $rate_types["rate"] = RateType::with(
            ['rates' => function ($query) use ($facilityId) {
                $query->where('facility_id', $facilityId)->orderby('price', 'ASC');
            }]
        )->get()->toArray();

        foreach ($rate_types["rate"] as $key1 => $rate_type) {
            foreach ($rate_type["rates"] as $key2 => $rate) {
                unset($rate['facility']);
                $rate_types["rate"][$key1]["rates"][$key2] = $rate;
            }
        }

        return $rate_types;
    }


    public function validateCouponCode(Request $request)
    {

        return ['valid' => "true"];
        /**
         * Creating an instance of a rate to do on the fly validation before a rate is created
         * This avoids creating any new rates with invalid coupon_codes
         */
        /** $rate = new Rate();
        $facility = Facility::find($request->facility_id);
        $rate->price = 10; //Generic Price
        $rate->coupon_code = $request->coupon_code;
        $rate->facility = $facility;
        return ['valid' => $rate->isCouponCodeValid()];/**/
    }

    public function updateRateInformationWithAvailibiltySideMap($request, Facility $facility)
    {
        $returnResultArr = array();

        $returnResultArr['coupon_threshold_price'] = 0;
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;

        $inventory = new Inventory();
        $date_time_out = Carbon::parse($request->arrival_time)->addHours($request->length_of_stay);

        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

        $timeDifference = date_diff(date_create($request->arrival_time), Carbon::now());

        $isAvailable = true;

        $thresholdAvailability = self::DEFAULT_VALUE;

        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
            //$difference = date_diff(date_create($request->arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d', strtotime($request->arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

            if ($difference->d > 0) {
                //                $dates   = $inventory->generateArrayOfDates(
                //                '', date($request->arrival_time), date($date_time_out));

                $dates   = $inventory->generateArrayOfDates(
                    ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                    date('Y-m-d H:i:s', strtotime($request->arrival_time))
                );

                $dayDifference = $difference->d;

                foreach ($dates as $key => $date) {
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

                    if ($facilityAvailability) {
                        $inventory = json_decode($facilityAvailability->availability);

                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should check from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($request->arrival_time));
                            while ($i <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$i})) {
                                    if ($inventory->{$i} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$i}) {
                                            $thresholdAvailability = $inventory->{$i};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$i};
                                    }
                                }
                                $i++;
                            }
                        } elseif ($key == $dayDifference) {
                            $i = date('G', strtotime($date_time_out));
                            $minutes = date('i', strtotime($date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }
                            /**
                             * because this is the last day in the dates provided
                             * we should check from each time_slot starting
                             * till the hour provided in the api call
                             */
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    if ($inventory->{$j} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$j}) {
                                            $thresholdAvailability = $inventory->{$j};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$j};
                                    }
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should check from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    if ($inventory->{$k} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$k}) {
                                            $thresholdAvailability = $inventory->{$k};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$k};
                                    }
                                }
                                $k++;
                            }
                        }
                    }
                }
            } else {
                $startingHour = date('G', strtotime($request->arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($request->arrival_time))]
                )->first();

                if ($facilityAvailability) {
                    $availability = json_decode($facilityAvailability->availability, true);

                    while ($startingHour <= $endingHour) {
                        if (isset($availability[$startingHour])) {
                            if (($availability[$startingHour] < 1)) {
                                $isAvailable = false;
                            }
                            if ($thresholdAvailability > 0) {
                                if ($thresholdAvailability > $availability[$startingHour]) {
                                    $thresholdAvailability = $availability[$startingHour];
                                }
                            } else {
                                $thresholdAvailability = $availability[$startingHour];
                            }
                        }
                        $startingHour++;
                    }
                }
            }

            if ($thresholdAvailability < self::DEFAULT_VALUE) {
                $thresholdAvailability = self::DEFAULT_VALUE;
            }

            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

                $dateIn = date('Y-m-d', strtotime($request->arrival_time));
                $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

                if ($facilityAvailability) {
                    //$availabilities = json_decode($facilityAvailability->availability, true);

                    if ($thresholdAvailability >= 0) {

                        $couponThresholdsNew = $facility->facilityCouponThreshold;
                        $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

                        $thresholds       = array();
                        if ($couponThresholdsNew) {
                            $thresholdCounter = self::DEFAULT_VALUE;
                            foreach ($couponThresholdsNew as $couponThreshold) {

                                if ($couponThreshold->uptick_type !== 'deleted') {
                                    $thresholds[$thresholdCounter] =
                                        ['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                                    $thresholdCounter++;
                                }
                            }
                        }
                        $thresholdPrice = 0;
                        $currentAvailability = $thresholdAvailability;
                        foreach ($thresholds as $key => $threshold) {
                            if ($thresholdAvailability <= $threshold['threshold']) {
                                if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                                    if ($threshold['uptick_type'] == 'price') {
                                        $thresholdPrice = $threshold['uptick'];

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    } else if ($threshold['uptick_type'] == 'percentage') {
                                        $thresholdPrice =  $threshold['uptick'];
                                        $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    }
                                }
                            }
                        }
                        $returnResultArr['coupon_threshold_price'] = $thresholdPrice;

                        $returnResultArr['availability'] = $currentAvailability;
                    }
                }
            }
        }

        //check realtime availability
        if ($timeDifference->h <= $realtimeWindow) {

            if ($facility->realtime_minimum_availability > $returnResultArr['availability']) {
                $returnResultArr['coupon_threshold_price'] = 0;
                $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
                $returnResultArr['availability'] = 0;
                $returnResultArr['is_coupon_threshold_applied'] = 0;
            }
        }

        return $returnResultArr;
    }


    public function updateRateInformationWithAvailibilty($request, Facility $facility)
    {
        $this->log->info("Get Availibilty");
        $returnResultArr = array();

        $returnResultArr['coupon_threshold_price'] = 0;
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;

        $inventory = new Inventory();
        // $date_time_out = Carbon::parse($request->arrival_time)->addHOurs((number_format($request->length_of_stay, 2) * 60));
        $date_time_out = Carbon::parse($request->arrival_time)->addHours($request->length_of_stay);

        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

        $timeDifference = date_diff(date_create($request->arrival_time), Carbon::now());

        $isAvailable = true;

        $thresholdAvailability = self::DEFAULT_VALUE;
        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
            //$difference = date_diff(date_create($request->arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d', strtotime($request->arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));
            $this->log->info("Difference Update: " . json_encode($difference->d > 0));
            if ($difference->d > 0) {
                //                $dates   = $inventory->generateArrayOfDates(
                //                '', date($request->arrival_time), date($date_time_out));

                $dates   = $inventory->generateArrayOfDates(
                    ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                    date('Y-m-d H:i:s', strtotime($request->arrival_time))
                );

                $dayDifference = $difference->d;

                foreach ($dates as $key => $date) {
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

                    if ($facilityAvailability) {
                        $inventory = json_decode($facilityAvailability->availability);
                        $this->log->info("Before Update Availability  Key : {$key} : " . json_encode($inventory));
                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should check from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($request->arrival_time));
                            while ($i <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$i})) {
                                    if ($inventory->{$i} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$i}) {
                                            $thresholdAvailability = $inventory->{$i};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$i};
                                    }
                                    $this->log->info("Check while loop  : {$i} : " . json_encode($thresholdAvailability));
                                }
                                $i++;
                            }
                        } elseif ($key == $dayDifference) {
                            $i = date('G', strtotime($date_time_out));
                            $minutes = date('i', strtotime($date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }
                            /**
                             * because this is the last day in the dates provided
                             * we should check from each time_slot starting
                             * till the hour provided in the api call
                             */
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    if ($inventory->{$j} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$j}) {
                                            $thresholdAvailability = $inventory->{$j};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$j};
                                    }
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should check from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    if ($inventory->{$k} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$k}) {
                                            $thresholdAvailability = $inventory->{$k};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$k};
                                    }
                                }
                                $k++;
                            }
                        }
                    }
                }
            } else {
                $startingHour = date('G', strtotime($request->arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($request->arrival_time))]
                )->first();
                if ($facilityAvailability) {
                    $availability = json_decode($facilityAvailability->availability, true);
                    $this->log->info("Before Update Availability: " . json_encode($availability));
                    while ($startingHour <= $endingHour) {
                        if (isset($availability[$startingHour])) {
                            if (($availability[$startingHour] < 1)) {
                                $this->log->info("Not Availabe for this hours: " . json_encode($availability[$startingHour]));
                                $isAvailable = false;
                            }
                            if ($thresholdAvailability > 0) {
                                if ($thresholdAvailability > $availability[$startingHour]) {
                                    $thresholdAvailability = $availability[$startingHour];
                                }
                            } else {
                                $thresholdAvailability = $availability[$startingHour];
                            }
                        }
                        $startingHour++;
                    }
                }
            }

            if ($thresholdAvailability < self::DEFAULT_VALUE) {
                $thresholdAvailability = self::DEFAULT_VALUE;
            }

            $this->log->info("Reached 121112323: " . json_encode($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)));
            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

                $dateIn = date('Y-m-d', strtotime($request->arrival_time));
                $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

                if ($facilityAvailability) {
                    //$availabilities = json_decode($facilityAvailability->availability, true);

                    if ($thresholdAvailability >= 0) {

                        $couponThresholdsNew = $facility->facilityCouponThreshold;
                        $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

                        $thresholds       = array();
                        if ($couponThresholdsNew) {
                            $thresholdCounter = self::DEFAULT_VALUE;
                            foreach ($couponThresholdsNew as $couponThreshold) {

                                if ($couponThreshold->uptick_type !== 'deleted') {
                                    $thresholds[$thresholdCounter] =
                                        ['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                                    $thresholdCounter++;
                                }
                            }
                        }
                        $thresholdPrice = 0;
                        $currentAvailability = $thresholdAvailability;
                        foreach ($thresholds as $key => $threshold) {
                            if ($thresholdAvailability <= $threshold['threshold']) {
                                if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                                    if ($threshold['uptick_type'] == 'price') {
                                        $thresholdPrice = $threshold['uptick'];

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    } else if ($threshold['uptick_type'] == 'percentage') {
                                        $thresholdPrice =  $threshold['uptick'];
                                        $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    }
                                }
                            }
                        }
                        $returnResultArr['coupon_threshold_price'] = $thresholdPrice;

                        $returnResultArr['availability'] = $currentAvailability;
                    }
                }
            }
        }

        //check realtime availability
        /*if ($timeDifference->h <= $realtimeWindow) {

            if($facility->realtime_minimum_availability > $returnResultArr['availability']){
              $returnResultArr['coupon_threshold_price'] = 0;        
              $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
              $returnResultArr['availability'] = 0;
              $returnResultArr['is_coupon_threshold_applied'] = 0;
            }
        }*/
        return $returnResultArr;
    }

    public function fun_array_sort($array, $on, $order = SORT_ASC)
    {

        $new_array = array();
        $sortable_array = array();

        if (count($array) > 0) {
            foreach ($array as $k => $v) {
                if (is_array($v)) {
                    foreach ($v as $k2 => $v2) {
                        if ($k2 == $on) {
                            $sortable_array[$k] = $v2;
                        }
                    }
                } else {
                    $sortable_array[$k] = $v;
                }
            }

            switch ($order) {
                case SORT_ASC:
                    asort($sortable_array);
                    break;
                case SORT_DESC:
                    arsort($sortable_array);
                    break;
            }

            foreach ($sortable_array as $k => $v) {
                $new_array[$k] = $array[$k];
            }
        }

        return $new_array;
    }


    public function getFacilityRateWithPass($facility_id, Request $request)
    {

        $facility = Facility::where('id', $facility_id)->first();

        $partner = \DB::table('partner_rate_types')->where('partner_id', $facility->owner_id)->get();

        $arrToReturn = [];
        if ($request->is_member) {
            $rateTypeId = [0];
            foreach ($partner as $key => $value) {
                if ($value->rate_type_id == 7) {
                    $rateTypeId[] = $value->rate_type_id;
                }
            }


            $arrToReturn = RateType::with(
                ['rates' => function ($query) use ($facility_id) {
                    $query->where('facility_id', $facility_id)->where('active', '1')->orderby('total_usage', 'ASC');
                }]
            )->whereIN('id', $rateTypeId)->get();
        }
        return $arrToReturn;
    }

    public function getFacilityPass($facilityId)
    {
        $facility = Facility::where('id', $facilityId)->first();
        if (!$facility) {
            throw new NotFoundException('Facility Not Found.');
        }

        $result = \DB::table('rates')->select('id', 'description')->where('facility_id', $facilityId)->whereIn('rate_type_id', [7])->get();

        return $result;
    }

    public function getFacilityUserPass($facilityId, Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $start_date = date("m/d/Y");
            $end_date = date('m/d/Y', strtotime('+1 year'));

            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $facility = Facility::where('id', $facilityId)->first();
            if (!$facility) {
                throw new NotFoundException('Facility Not Found.');
            }
            $rates = Rate::with(['facility', 'facility.facilityfee'])->where('facility_id', $facilityId)->whereIn('rate_type_id', [7])->orderBy('total_usage', 'ASC')->get();
            //return $rates;
            $fee1 = isset($val->facility->facilityfee[0]->val) ? $val->facility->facilityfee[0]->val : '0';
            $fee2 = isset($val->facility->facilityfee[1]->val) ? $val->facility->facilityfee[1]->val : '0';
            $fee3 = isset($val->facility->facilityfee[2]->val) ? $val->facility->facilityfee[2]->val : '0';
            foreach ($rates as $key => $val) {
                if (isset($val->facility->facilityfee[0]->name) && $val->facility->facilityfee[0]->name == 'processing_fee') {
                    $rates[$key]['total_amount'] =  number_format($val->price + $fee1, 2);
                } else if (isset($val->facility->facilityfee[1]->name) && $val->facility->facilityfee[1]->name == 'processing_fee') {
                    $rates[$key]['total_amount'] = number_format($val->price + $fee2, 2);
                } else if (isset($val->facility->facilityfee[2]->name) && $val->facility->facilityfee[2]->name == 'processing_fee') {
                    $rates[$key]['total_amount'] = number_format($val->price + $fee3, 2);
                }

                $rates[$key]['booking_start_date'] = $start_date;
                $rates[$key]['booking_end_date'] = $end_date;
            }
            //$result = \DB::table('rates')->where('facility_id', $facilityId)->whereIn('rate_type_id', [7])->get();

            return $rates;
        }
    }

    /***
     *   
     *   Vijay :: 15-5-2024 Deployed
     *  To get rate details according to facility id
     *  @return object 
     * */
    public function getRateDetails(Request $request, $facilityId)
    {
        try {
            $rateDetails = [];
            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }

                $facility = Facility::find($facilityId);
                if (!$facility) {
                    throw new NotFoundException('Facility not found.');
                }

                $rateDetails = Rate::where('facility_id', $facilityId)->where('active', 1)->get();
                if (!$rateDetails) {
                    throw new NotFoundException('Rate not found.');
                }
            }
            return $rateDetails;
        } catch (\Throwable $e) {
            $this->log->error("Error message" . $e);
            throw new NotFoundException($e);
        }
    }


    public function gethotelpasses(Request $request)
    {
        try {

            $facility = Facility::find($request->facility_id);
            if (!$facility) {
                throw new ApiGenericException('Facility not found.');
            }

            $dates = QueryBuilder::setCustomTimezone($request->facility_id);

            $query = Rate::where('is_device_specific_rate', 3)
                ->where('facility_id', $request->facility_id)
                ->whereDate('start_date', '<=', date('Y-m-d'))
                ->whereDate('end_date', '>=', date('Y-m-d'))
                ->select('rates.id', 'rates.description');

            // dd($query->toSql(), $query->getBindings());

            $data = $query->get();

            if ($data->isEmpty()) {
                throw new ApiGenericException('Pass not found.');
            }

            return $data;
        } catch (NotFoundException $e) {
            throw new ApiGenericException($e->getMessage());
        } catch (\Throwable $e) {
            $this->log->error("Error message: " . $e->getMessage());
            throw new ApiGenericException($e->getMessage());
        }
    }

    // Rate Resrevation Start 
    public function isExtendReservation($request, $facility)
    {
        if (isset($request->reservation_id) && !empty($request->reservation_id) && $facility->is_service_update == "1") {
            return true;
        }
        return false;
    }

    public function isEditReservation($request, $facility)
    {
        if (isset($request->reservation_id)) {
            if ($facility->is_service_update == "0") {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public function getRateForReservation(Request $request, Facility $facility)
    {
        $this->reservationRate->info("Get Rate Request: " . json_encode($request->all()));
        // try {
        $this->validate($request, [
            'arrival_time' => 'required|string',
            'length_of_stay' => 'required|numeric',
            'use_bonus' => 'boolean'
        ]);
        $total_amount = $parking_amount = $reservationAmount = $payable_amount = $amount_paid =
            $discount = $tax_rate = $processingFee = $appliedTaxFee = $appliedProcessingFee = $refundAmount =
            $coupon_threshold_price = $is_coupon_threshold_applied = $is_coupon_threshold_price_percentage = 0;
        $refundFlag = $updateRequestWithNoTimeChange = false;
        $secret = null;

        if ($this->isEditReservation($request, $facility)) {
            throw new ApiGenericException('Modification is not allowed');
        }

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }

            if (!Facility::where('id', $facility->id)->where('owner_id', $secret->partner_id)->first()) {
                throw new NotFoundException('No garage found with this partner.');
            }
        }
        // dd("First",$secret);

        $lengthOfStay = $request->length_of_stay ?? 24;
        if ($facility->owner_id == config('parkengage.PARTNER_AAA')) {
            $lengthOfStay = 23.59;
        }

        //get updated Rate reservation
        $is_admin = false;
        if ($this->isExtendReservation($request, $facility)) { // indicate service update enable
            $reservation = Reservation::with('user')->where('id', $request->reservation_id)->whereNull('cancelled_at')->first();
            if (!$reservation) {
                throw new ApiGenericException('Reservation can not be modified because it has been cancelled before.');
            }
            $discount = $reservation->discount;
            $amount_paid = $reservation->total;
            $appliedProcessingFee = $reservation->processing_fee;
            $appliedTaxFee = $reservation->tax_fee;

            if (is_null($secret)) {
                $is_admin = true;
                $partner_id = $reservation->partner_id;
                $secret = OauthClient::where('partner_id', $partner_id)->first();
            }

            if ($request->length_plus_minus == '1') {
            } else if ($request->length_plus_minus == '0') {
                $refundFlag = true;
            } else {
                $lengthOfStay = $request->length_of_stay;
                if (isset($request->length_plus_minus)) {
                    $updateRequestWithNoTimeChange =  true;
                }
            }
            $lengthOfStay = $request->length_of_stay;
        }
        // end updated Rate reservation

        $isMember = isset($request->is_member) ? $request->is_member : 0;
        if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
            $this->reservationRate->info("function rateForReservationByPassRateEngine");
            $rate = $facility->rateForReservationByPassRateEngine($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
        } else {
            $this->reservationRate->info("function rateForReservationOnMarker");
            $rate = $facility->rateForReservationOnMarker($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
        }

        if (!$rate) {
            throw new ApiGenericException('No valid rates for those parameters.');
        }

        //return current availabilit
        $parking_amount = $rate['price'];
        if ($parking_amount > 0) {
            $tax_rate = $facility->getTaxRate($rate, '1');
            if ($facility->tax_rate_type == 0) {
                $tax_rate += $appliedTaxFee;
            }
            $processingFee = $facility->getProcessingFee('1');
        }
        $reservationAmount = $parking_amount + $tax_rate + $processingFee;

        // dd($reservationAmount, $parking_amount, $tax_rate, $processingFee, $discount, $this->isExtendReservation($request, $facility));
        if ($this->isExtendReservation($request, $facility)) {
            // dd($parking_amount == $reservation->parking_amount);
            if ($parking_amount == $reservation->parking_amount) {
                $updateRequestWithNoTimeChange =  true;
            }
        }



        // if ($tax_rate > 0) {
        //     // $tax_rate = (($rate['price'] * $facility->tax_rate) / 100);
        //     $rate['tax_fee'] = $tax_rate;
        // } else {
        //     $rate['tax_fee'] = '0.00';
        // }


        // if ($rate['price'] <= 0) {
        //     $rate['tax_fee'] = '0.00';
        //     $rate['facility']['processing_fee'] = '0.00';
        // }

        $rate['tax_fee'] =  $parking_amount > 0 ? $tax_rate : '0.00';
        $rate['processing_fee'] =  $parking_amount > 0 ?  $processingFee : '0.00';

        // Vijay Deployed : 12-03-2024 : ROC Respect change Start 
        $reservationAmount = $parking_amount > 0 ? ($parking_amount + $tax_rate + $processingFee) : '0';
        $payable_amount = ($reservationAmount - $discount);

        // dd($reservationAmount, $parking_amount , $tax_rate , $processingFee , $appliedProcessingFee , $appliedTaxFee, $discount);
        $promocodeAmount = $reservationAmount + $discount;

        $rate['refundFlag']     = $refundFlag;
        $rate['total']          = sprintf("%.2f", $payable_amount);
        $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
        $rate['payable_amount'] = sprintf("%.2f", $payable_amount);

        if ($refundFlag) {
            $refund_amount = 0;
            ($refund_amount = max(0, $amount_paid - $reservationAmount - $discount));
            // dd($refund_amount, $reservationAmount, $amount_paid, $discount);
            $rate['refund_amount']  = sprintf("%.2f", $refund_amount);
            $rate['payable_amount'] = '0.00';
            $rate['amount_paid']    = $reservation->total;
            $rate['refundFlag']     = $refundFlag;
        } else if ($refundFlag == false && $request->length_plus_minus == '1') {
            $this->reservationRate->info("Reached 222 Total Amount: $reservationAmount");
            $payable_amount = ($reservationAmount >= $reservation->total) ? sprintf("%.2f", ($reservationAmount - $reservation->total)) : sprintf("%.2f", ($reservation->total - $reservationAmount));
            // dd($reservationAmount, $payable_amount, $is_admin);
            if (isset($reservation->promocode)) {
                $promoUsages = PromoUsage::where('reservation_id', $reservation->ticketech_code)->where('email', $reservation->user->email)->latest()->first();

                // make Request for promocode 
                $request->request->add(['is_guest' => $reservation->user->anon]);
                $request->request->add(['reservation_id' => isset($request->reservation_id) ? $request->reservation_id : '']);
                $request->request->add(['client_id' => $secret->secret]);
                $request->request->add(['email' => isset($request->email) ? $request->email : $reservation->user->email]);
                $request->request->add(['amount' => $reservationAmount]);
                if ($payable_amount > 0) {
                    $request->request->add(['tax_amount' => $rate['tax_fee']]);
                    $request->request->add(['processing_fee' => $rate['processing_fee'] > 0 ? $rate['processing_fee'] : $appliedProcessingFee]);
                    // $request->request->add(['tax_amount' => $rate['tax_fee'] - $appliedTaxFee]);
                    // $request->request->add(['processing_fee' => $rate['processing_fee']]);
                } else {
                    $request->request->add(['tax_amount' => "0.00"]);
                    $request->request->add(['processing_fee' => "0.00"]);
                }
                $request->request->add(['promocode' => $reservation->promocode]);

                $this->reservationRate->info("Request for Promocode: " . json_encode($request->all()));

                $maxDiscount = 0;
                if ($promoUsages) {
                    $this->log->info("Promocode usage found: Promocode: {$promoUsages->promocode} and Partner Id: {$secret->partner_id}");
                    $promoDetail = PromoCode::where('promocode', $promoUsages->promocode)->first();
                    if (!$promoDetail) {
                        throw new ApiGenericException('Invalid promocode.');
                    }
                    if (isset($promoDetail->promo_type_id) && $promoDetail->promo_type_id != 2) {
                        $promocode = $this->latestPromotionController->checkPromoThirdParty($request);
                        $responseData = json_decode($promocode->getcontent(), true);
                        if ($responseData) {
                            $rate['promocode'] = $responseData;
                            $this->reservationRate->info("Promocode Applied info IN IF" . json_encode($rate['promocode']));
                            // dd($responseData, $responseData['promocode']['discount_type'], $responseData['promocode']['discount_type'] == "percentage");
                            if ($responseData['promocode']['discount_type'] == "percentage") {
                                $maxDiscount = $responseData['max_percentage_discount'];
                                $discount = $responseData['discount_in_dollar'];
                            } else if ($responseData['promocode']['discount_type'] == "value") {
                                $maxDiscount = $responseData['promocode']['discount_value'];
                                $discount = $responseData['discount_in_dollar'];
                            }
                            $payable_amount = $responseData['payable_amount'] - $amount_paid;
                            if ($discount > $maxDiscount) {
                                $payable_amount = $discount - $maxDiscount;
                                $discount = $maxDiscount;
                            }
                            // dd($request->amount, $payable_amount, $discount, $amount_paid, $rate['tax_fee'], $rate['processing_fee'], $responseData);

                        }
                    } else {
                        $this->reservationRate->info("Promo Type Id" . json_encode($promoDetail->promo_type_id));
                    }
                } else {
                    $this->reservationRate->info("No usage found");
                    if (!empty($reservation->promocode)) {
                        $promocode = $this->latestPromotionController->checkPromoThirdParty($request);
                        $responseData = json_decode($promocode->getcontent(), true);
                        if ($responseData) {
                            $rate['promocode'] = $responseData;
                            $this->log->info("Promocode Applied info IN IF" . json_encode($rate['promocode']));
                            // dd($responseData, $responseData['promocode']['discount_type'], $responseData['promocode']['discount_type'] == "percentage");
                            if ($responseData['promocode']['discount_type'] == "percentage") {
                                $maxDiscount = $responseData['max_percentage_discount'];
                                $discount += $responseData['discount_in_dollar'];
                            }
                            // dd($responseData, $request->amount, $payable_amount, $discount, $rate['tax_fee'], $rate['processing_fee']);
                            $payable_amount = $responseData['payable_amount'];
                            $discount -= $amount_paid;
                            if ($discount > $maxDiscount) {
                                $payable_amount = $discount - $maxDiscount;
                                $discount = $maxDiscount;
                            }
                        }
                    }
                }
            } else {
                $this->reservationRate->info("No Promocode Applied: Payable Amount: " . $payable_amount);
            }
            // dd($rate['payable_amount']);

            $rate['total']          = sprintf("%.2f", $reservationAmount);
            $rate['amount_paid']    = sprintf("%.2f", $reservation->total);
            $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
            $rate['payable_amount'] =  $payable_amount;
        }

        if ($updateRequestWithNoTimeChange) {
            $rate['total']          = sprintf("%.2f", $reservationAmount);
            $rate['amount_paid']    = sprintf("%.2f", $reservation->total);
            $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
            $rate['payable_amount'] = sprintf("%.2f", self::DEFAULT_VALUE);
        }
        $rate['discount'] = $discount;
        $rate['discount_amount'] = $discount;

        // dd($rate['total'], $rate['amount_paid'], $rate['parking_amount'], $rate['payable_amount'], $tax_rate , $processingFee, $appliedProcessingFee, $updateRequestWithNoTimeChange, $lengthOfStay, $refundFlag == false && $request->length_plus_minus == '1');
        // !!! Close here .
        $this->reservationRate->info("Parking Amount: $parking_amount, Length of Stay: $lengthOfStay and Tax rate: $tax_rate and Processing fee: $processingFee");

        //if price is coming n/A it may be because if not working hours of facility so set avalability as 0

        // Set default Values: 
        $rate['isFacilityClosed'] = self::IS_FACILITY_OPEN;
        $rate['coupon_price_applied'] = self::DEFAULT_VALUE;
        $rate['availability'] = self::DEFAULT_VALUE;


        if ($rate['price'] == 'N/A') {
            $rate['availability'] = self::DEFAULT_VALUE;
            $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
        }


        //return if coupon price get applied
        if ((isset($rate['coupon_price'])) && ($rate['coupon_price'] > self::DEFAULT_VALUE)) {
            $rate['coupon_price_applied'] = "$" . number_format($rate['coupon_price'], 0) . " WITH COUPON";
        }

        $rateData = $this->updateRateInformationWithAvailibilty($request, $facility);
        $this->reservationRate->info("updateRateInformationWithAvailibilty : " . json_encode($rateData));

        if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
            $rate['availability'] = $rateData['availability'];
        }

        $facilityConfig = FacilityConfiguration::where("facility_id", $facility->id)->first();
        //returning  message as per availibility 
        $rate['availability_msg'] = '';
        $rate['availability_msg_some_space'] = '';
        // dd((float)$rate['price'] >  self::DEFAULT_VALUE, !$facilityConfig->is_inventory_check, $rate['availability'], $facilityConfig->facility_id);
        if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
            // Lokesh: Inventory check for availability 
            // dd(!$facilityConfig->is_inventory_check, $rate['availability'], $facilityConfig->facility_id);
            if (!$facilityConfig->is_inventory_check)
                $rate['availability'] = 1;
            if ($rate['availability'] == self::DEFAULT_VALUE) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
            } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
            } else {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
            }
        } else if ($rate['price'] == 'N/A') {
            $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
            $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
        } else if ($rate['availability'] == self::DEFAULT_VALUE) {
            $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
            $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
        }

        //price to show on facility details page if availibity is 0.
        if ((isset($rate['availability'])) && ($rate['availability'] > 0)) {
            $rate['price_without_availibility'] = self::DEFAULT_VALUE;
        } else {
            $rate['price_without_availibility'] = ($rate['price'] != 'N/A') ? number_format($rate['price']) : $rate['price'];
            // commented by ujjwal for skip inventory hours of operation availability check 27-12-2023
            //    $rate['price'] = 'N/A';
            //  $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
        }

        $this->reservationRate->info("availability: " . $rate['availability'] . ", Availability Msg: " . $rate['availability_msg']);

        //change msg of facility close in worlport rate
        if (isset($rate['todayHoursOfOperation'])) {
            if ($rate['todayHoursOfOperation'] == '0' && $rate['twenty_four_hours'] == '0') {
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
            }
        }


        // if ($rate['price'] != 'N/A') {
        //     $rate['price'] = number_format($rate['price'], 2);
        // }

        if ((!isset($rate['coupon_threshold_price'])) || (($rate['coupon_threshold_price']) < 0)) {
            $rate['coupon_threshold_price'] =  self::DEFAULT_VALUE;
        }

        $rate['facility_hours_of_operation'] = $facility->hoursOfOperation()->orderBy('day_of_week')->get();

        if ($facility->is_available == '2' || $facility->is_available == 2) {
            $rate['is_available_msg'] = self::FACILITY_NOT_AVAILABLE;
        } elseif ($facility->is_available == '3' || $facility->is_available == 3) {
            $rate['is_available_msg'] = self::FACILITY_COMING_SOON;
        } else {
            $rate['is_available_msg'] = self::FACILITY_AVAILABLE;
        }


        $facilityrate = Rate::select('max_stay', 'price')->where('facility_id', $facility->id)->where('active', 1)->get();

        if (count($facilityrate) > 0) {
            foreach ($facilityrate as $value) {
                $rate['facility_rates'][] = '$' . $value->price . ' for ' . $value->max_stay . ' hours';
            }
            $rate['facility_rates'] = implode(', ', $rate['facility_rates']);
        }

        $rate['facility_brand_setting'] = $facility->faciltyBrandSetting();


        if ((isset($rateData['is_coupon_threshold_applied'])) && ($rateData['is_coupon_threshold_applied'] > 0)) {
            $is_coupon_threshold_applied = 1;
        }

        if ((isset($rateData['coupon_threshold_price'])) && ($rateData['coupon_threshold_price'] > 0) && ($is_coupon_threshold_applied == self::DEFAULT_VALUE_ONE)) {
            $coupon_threshold_price = $rateData['coupon_threshold_price'];
        }

        if ((isset($rateData['is_coupon_threshold_price_percentage'])) && ($rateData['is_coupon_threshold_price_percentage'] > 0)) {
            $is_coupon_threshold_price_percentage = 1;
        }

        // 24/06/2024- SHalu : Added for adding payment_type in facility object
        if (isset($facility->FacilityPaymentDetails) && !empty($facility->FacilityPaymentDetails)) {
            $facility_payment_type = ($facility->FacilityPaymentDetails->facilityPaymentType->payment_type);
        } else {
            $facility_payment_type = '';
        }
        // End 24/06/2024- SHalu : Added for adding payment_type in facility object

        // if (isset($rate) && !empty($rate)) {
        // }
        $rate['facility']['payment_type'] = $facility_payment_type;

        if (isset($request->user_id) && $request->user_id != '') {
            if (isset($rate['facility_id'])) {
                $parkingDevice = ParkingDevice::where("facility_id", $rate['facility_id'])->first();
                if ($parkingDevice) {
                    $rate['facility']['is_device_facility'] = '1';
                } else {
                    $rate['facility']['is_device_facility'] = '0';
                }
                $reservations = Reservation::with('facility')->where("user_id", $request->user_id)->where("facility_id", $rate['facility_id'])->orderBy("id", "DESC")->get();
                if (count($reservations) <= 0) {
                    $rate['facility']['is_reservation_found'] = '0';
                } else {
                    $now = date("Y-m-d H:i:s");
                    foreach ($reservations as $reservation) {
                        $entryTime = $reservation->start_timestamp;
                        $exitTime = Carbon::parse($reservation->start_timestamp)->addMinutes(($reservation->length * 60));
                        if (strtotime($entryTime) <= strtotime($now) && strtotime($exitTime) >= strtotime($now)) {
                            $rate['facility']['is_reservation_found'] = '1';
                            $rate['facility']['reservation_code'] = $reservation->ticketech_guid;
                            $rate['facility']['iq_code'] = $reservation->ticketech_code;
                            $ticket = Ticket::where("reservation_id", $reservation->id)->orderBy("id", "DESC")->first();
                            if (!$ticket) {
                                $rate['facility']['is_reservation_checkin_found'] = '0';
                            } else {
                                $rate['facility']['is_reservation_checkin_found'] = '1';
                            }
                            break;
                        }
                    }
                }
            } else {
                $parkingDevice = ParkingDevice::where("facility_id", $rate['facility']->id)->first();
                if ($parkingDevice) {
                    $rate['facility']['is_device_facility'] = '1';
                } else {
                    $rate['facility']['is_device_facility'] = '0';
                }
                $rate['facility']['is_reservation_found'] = '0';
                $rate['facility']['is_reservation_checkin_found'] = '0';
            }
        }

        return $rate;
        // } catch (\Exception $e) {
        //     $this->log->error("Error message" . $e->getMessage());
        //     throw new NotFoundException($e->getMessage());
        // }
    }
    // Rate Resrevation End


    public function geInventoryCheck(Request $request)
    {
        $this->log->info("Get Rate Request: " . json_encode($request->all()));


        $this->validate(
            $request,
            [
                'facility_id' => 'required',
                'arrival_time' => 'required|string',
                'length_of_stay' => 'required|numeric',
                'use_bonus' => 'boolean'
            ]
        );

        try {
            $total_amount = $parking_amount = $payable_amount = $amount_paid = $discount = 0;

            $facility = Facility::find($request->facility_id);

            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                $this->partnerId = $secret->partner_id;

                if (!Facility::where('id', $facility->id)->where('owner_id', $secret->partner_id)->first()) {
                    throw new NotFoundException('No garage found with this partner.');
                }
            }

            // Get Customer Portal Permissions
            // if ($this->partnerId > 0) {
            //     $this->customerPortalPermitions = QueryBuilder::partnerWiseCustomerPermissions($this->partnerId);
            // }

            $rateData = $this->updateRateInformationWithAvailibilty($request, $facility);
            $this->log->info("updateRateInformationWithAvailibilty : " . json_encode($rateData));
            $coupon_threshold_price = 0;
            $is_coupon_threshold_applied = 0;
            $is_coupon_threshold_price_percentage = 0;

            $lengthOfStay = $request->length_of_stay;

            if (!isset($request->length_of_stay)) {
                $lengthOfStay = 24;
            }

            if ($facility->owner_id == 45) {
                $lengthOfStay = 23.59;
            }

            $rate['isFacilityClosed'] = self::IS_FACILITY_OPEN;
            if ($facility->active != 1) {
                $rate['availability'] = self::DEFAULT_VALUE;
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
                return $rate;
            }

            if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                $rate['availability'] = $rateData['availability'];
            } else {
                $rate['availability'] = self::DEFAULT_VALUE;
            }
            $facilityConfig = FacilityConfiguration::where("facility_id", $facility->id)->first();

            if (isset($facilityConfig->is_inventory_check) && !$facilityConfig->is_inventory_check) {
                $rate['availability'] = 1;
                $rate['availability_msg'] = '';
            } else {
                if ($rate['availability'] == self::DEFAULT_VALUE) {

                    // PIMS - 13805 VP : 06-05-2025 
                    $this->customMessage = QueryBuilder::getCustomMessage('zero-slot', $facility->id, $facility->owner_id);

                    if ($this->customMessage != false) {
                        $rate['availability_msg'] = $this->customMessage;
                    } else {
                        $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                        $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
                    }
                } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
                } else {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                    $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                }
            }



            return $rate;
            // All Code comment below Due to PIMS - 13904



            /*
            if ((isset($rateData['is_coupon_threshold_applied'])) && ($rateData['is_coupon_threshold_applied'] > 0)) {
                $is_coupon_threshold_applied = 1;
            }

            if ((isset($rateData['coupon_threshold_price'])) && ($rateData['coupon_threshold_price'] > 0) && ($is_coupon_threshold_applied == self::DEFAULT_VALUE_ONE)) {
                $coupon_threshold_price = $rateData['coupon_threshold_price'];
            }

            if ((isset($rateData['is_coupon_threshold_price_percentage'])) && ($rateData['is_coupon_threshold_price_percentage'] > 0)) {
                $is_coupon_threshold_price_percentage = 1;
            } */

            $lengthOfStay = $request->length_of_stay;

            if (!isset($request->length_of_stay)) {
                $lengthOfStay = 24;
            }

            if ($facility->owner_id == 45) {
                $lengthOfStay = 23.59;
            }

            $refundFlag = false;
            $updateRequestWithNoTimeChange = false;
            $appliedProcessingFee = 0;

            /*  if ($facility->is_service_update == '1' && isset($request->reservation_id)) { // indicate service update enable
                $reservation = Reservation::find($request->reservation_id);
                $discount = $reservation->getDiscountedValue($request->reservation_id);
                $appliedProcessingFee = $reservation->getAppliedProcessingFees($request->reservation_id);
                if ($request->length_plus_minus == '1') {
                } else if ($request->length_plus_minus == '0') {
                    $refundFlag = true;
                } else {
                    $lengthOfStay = $request->length_of_stay;
                    if (isset($request->length_plus_minus)) {
                        $updateRequestWithNoTimeChange =  true;
                    }
                }
                $lengthOfStay = $request->length_of_stay;
            } 
            */

            $isMember = isset($request->is_member) ? $request->is_member : 0;

            if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                $this->log->info("function rateForReservationByPassRateEngine");
                $rate = $facility->rateForReservationByPassRateEngine($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
            } else {
                $this->log->info("function rateForReservationOnMarker");
                $rate = $facility->rateForReservationOnMarker($request->arrival_time, $lengthOfStay, $request->use_bonus, false, null, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
            }

            //return current availabilit
            $parking_amount = $rate['price'];
            $this->log->info("Parking Amount: $parking_amount, Length of Stay: $lengthOfStay");

            $rate['isFacilityClosed'] = self::IS_FACILITY_OPEN;
            if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                $rate['availability'] = $rateData['availability'];
            } else {
                $rate['availability'] = self::DEFAULT_VALUE;
            }

            //if price is coming n/A it may be because if not working hours of facility so set avalability as 0
            if ($rate['price'] == 'N/A') {
                $rate['availability'] = self::DEFAULT_VALUE;
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            }
            //        if(($coupon_threshold_price > self::DEFAULT_VALUE) && ($rate['availability'] > self::DEFAULT_VALUE))
            //        {
            //          
            //            if($is_coupon_threshold_price_percentage!=1)
            //            {
            //              $rate['coupon_threshold_price'] = (string)round($coupon_threshold_price,0);  
            //            }else{
            //                 $rate['coupon_threshold_price'] = (string)round((($rate['price'] * $coupon_threshold_price)/100),0);
            //            }
            //            $rate['is_coupon_threshold_applied'] = self::DEFAULT_VALUE_ONE;  
            //        }else{
            //              $rate['coupon_threshold_price'] = self::DEFAULT_VALUE_STR;  
            //              $rate['is_coupon_threshold_applied'] = self::DEFAULT_VALUE;          
            //        }

            //return if coupon price get applied
            if ((isset($rate['coupon_price'])) && ($rate['coupon_price'] > self::DEFAULT_VALUE)) {
                $rate['coupon_price_applied'] = "$" . number_format($rate['coupon_price'], 0) . " WITH COUPON";
            } else {
                $rate['coupon_price_applied'] = self::DEFAULT_VALUE;
            }

            $facilityConfig = FacilityConfiguration::where("facility_id", $facility->id)->first();
            // dd($this->partnerId,  $facilityConfig->is_inventory_check, $facility->id, $rate['availability'] == self::DEFAULT_VALUE, $rate['availability'] < self::MIN_AVAILABILITY);

            //returning  message as per availibility 
            $rate['availability_msg'] = '';
            $rate['availability_msg_some_space'] = '';
            if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
                // Lokesh: Inventory check for availability 

                if (isset($facilityConfig->is_inventory_check) && !$facilityConfig->is_inventory_check) {
                    $rate['availability'] = 1;
                }


                if ($rate['availability'] == self::DEFAULT_VALUE) {
                    // PIMS - 13805 VP : 06-05-2025 
                    $this->customMessage = QueryBuilder::getCustomMessage('zero-slot', $facility->id);

                    if ($this->customMessage != false) {
                        $rate['availability_msg'] = $this->customMessage;
                    } else {
                        $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                        $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
                    }
                } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
                } else {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                    $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                }
            } else if ($rate['price'] == 'N/A') {
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
            } else if ($rate['availability'] == self::DEFAULT_VALUE) {
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
            }

            if (!isset($facilityConfig->is_inventory_check) || $facilityConfig->is_inventory_check <= 0) {
                $rate['availability'] = 1;
                $rate['availability_msg'] = 'No inventory check.';
            }

            //price to show on facility details page if availibity is 0.
            if ((isset($rate['availability'])) && ($rate['availability'] > 0)) {
                $rate['price_without_availibility'] = self::DEFAULT_VALUE;
            } else {
                $rate['price_without_availibility'] = ($rate['price'] != 'N/A') ? number_format($rate['price']) : $rate['price'];
                // commented by ujjwal for skip inventory hours of operation availability check 27-12-2023
                //    $rate['price'] = 'N/A';
                //  $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
            }

            //change msg of facility close in worlport rate
            if (isset($rate['todayHoursOfOperation'])) {
                if ($rate['todayHoursOfOperation'] == '0' && $rate['twenty_four_hours'] == '0') {
                    $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
                }
            }

            if (!$rate) {
                throw new NotFoundException('No valid rates for those parameters.');
            }


            if ($rate['price'] != 'N/A') {
                $rate['price'] = number_format($rate['price'], 2);
            }

            if ((!isset($rate['coupon_threshold_price'])) || (($rate['coupon_threshold_price']) < 0)) {
                $rate['coupon_threshold_price'] =  self::DEFAULT_VALUE;
            }

            $rate['facility_hours_of_operation'] = $facility->hoursOfOperation()->orderBy('day_of_week')->get();

            if ($facility->is_available == '2' || $facility->is_available == 2) {
                $rate['is_available_msg'] = self::FACILITY_NOT_AVAILABLE;
            } elseif ($facility->is_available == '3' || $facility->is_available == 3) {
                $rate['is_available_msg'] = self::FACILITY_COMING_SOON;
            } else {
                $rate['is_available_msg'] = self::FACILITY_AVAILABLE;
            }


            $facilityrate = Rate::select('max_stay', 'price')->where('facility_id', $facility->id)->where('active', 1)->get();

            if (count($facilityrate) > 0) {
                foreach ($facilityrate as $value) {
                    $rate['facility_rates'][] = '$' . $value->price . ' for ' . $value->max_stay . ' hours';
                }
                $rate['facility_rates'] = implode(', ', $rate['facility_rates']);
            }

            // tax rate for amount and percentage calculation
            $reservationAmount = 0;

            $tax_rate = $facility->getTaxRate($rate);
            $processingFee = $facility->getProcessingFee('1');

            if ($facility->is_service_update == '1' && isset($request->reservation_id))
                $processingFee = 0;

            if ($tax_rate > 0) {
                // $tax_rate = (($rate['price'] * $facility->tax_rate) / 100);
                $rate['tax_fee'] = $tax_rate;
            } else {
                $rate['tax_fee'] = '0.00';
            }

            if ($rate['price'] <= 0) {
                $rate['tax_fee'] = '0.00';
                $rate['facility']['processing_fee'] = '0.00';
            }
            $rate['tax_fee'] =  $parking_amount > 0 ? $tax_rate : '0.00';
            $rate['processing_fee'] =  $parking_amount > 0 ?  $processingFee : '0.00';

            // Vijay Deployed : 12-03-2024 : ROOC Respect change Start 
            $reservationAmount          = $parking_amount > 0 ? ($parking_amount + $tax_rate + $processingFee + $appliedProcessingFee - $discount) : '0';
            $rate['refundFlag']         = $refundFlag;
            $rate['total']              = sprintf("%.2f", $reservationAmount);
            $rate['amount_paid']        = $amount_paid;
            $rate['parking_amount']     = sprintf("%.2f", $parking_amount);
            $rate['payable_amount']     = sprintf("%.2f", $reservationAmount);

            if ($refundFlag) {
                $rate['refund_amount']  = sprintf("%.2f", $reservation->total - $reservationAmount);
                $rate['payable_amount'] = '0.00';
                $rate['amount_paid']    = $reservation->total;
                $rate['refundFlag']     = $refundFlag;
            } else if ($refundFlag == false && $request->length_plus_minus == '1') {
                $rate['total']          = sprintf("%.2f", $reservationAmount);
                $rate['amount_paid']    = sprintf("%.2f", $reservation->total);
                $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
                $rate['payable_amount'] = ($reservationAmount >= $reservation->total) ? sprintf("%.2f", ($reservationAmount - $reservation->total)) : sprintf("%.2f", ($reservation->total - $reservationAmount));
            }

            if ($updateRequestWithNoTimeChange) {
                $rate['total']          = sprintf("%.2f", $reservationAmount);
                $rate['amount_paid']    = sprintf("%.2f", $reservation->total);
                $rate['parking_amount'] = sprintf("%.2f", $parking_amount);
                $rate['payable_amount'] = sprintf("%.2f", self::DEFAULT_VALUE);
            }

            // !!! Close here .

            if (isset($request->user_id) && $request->user_id != '') {
                if (isset($rate['facility_id'])) {
                    $parkingDevice = ParkingDevice::where("facility_id", $rate['facility_id'])->first();
                    if ($parkingDevice) {
                        $rate['facility']['is_device_facility'] = '1';
                    } else {
                        $rate['facility']['is_device_facility'] = '0';
                    }
                    $reservations = Reservation::with('facility')->where("user_id", $request->user_id)->where("facility_id", $rate['facility_id'])->orderBy("id", "DESC")->get();
                    if (count($reservations) <= 0) {
                        $rate['facility']['is_reservation_found'] = '0';
                    } else {
                        $now = date("Y-m-d H:i:s");
                        foreach ($reservations as $reservation) {
                            $entryTime = $reservation->start_timestamp;
                            $exitTime = Carbon::parse($reservation->start_timestamp)->addMinutes(($reservation->length * 60));
                            if (strtotime($entryTime) <= strtotime($now) && strtotime($exitTime) >= strtotime($now)) {
                                $rate['facility']['is_reservation_found'] = '1';
                                $rate['facility']['reservation_code'] = $reservation->ticketech_guid;
                                $rate['facility']['iq_code'] = $reservation->ticketech_code;
                                $ticket = Ticket::where("reservation_id", $reservation->id)->orderBy("id", "DESC")->first();
                                if (!$ticket) {
                                    $rate['facility']['is_reservation_checkin_found'] = '0';
                                } else {
                                    $rate['facility']['is_reservation_checkin_found'] = '1';
                                }
                                break;
                            }
                        }
                    }
                } else {
                    $parkingDevice = ParkingDevice::where("facility_id", $rate['facility']->id)->first();
                    if ($parkingDevice) {
                        $rate['facility']['is_device_facility'] = '1';
                    } else {
                        $rate['facility']['is_device_facility'] = '0';
                    }
                    $rate['facility']['is_reservation_found'] = '0';
                    $rate['facility']['is_reservation_checkin_found'] = '0';
                }
            }
            $rate['facility_brand_setting'] = $facility->faciltyBrandSetting();
            $rate['discount'] = $discount;
            $rate['discount_amount'] = $discount;
            return $rate;
        } catch (\Exception $e) {
            $this->log->error("Error message" . $e->getMessage());
            throw new ApiGenericException('Something went wrong, please try after some time.');
        }
    }
}
