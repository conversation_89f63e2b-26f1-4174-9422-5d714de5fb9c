<?php

namespace App\Http\Controllers;

use App\Models\Neighborhood;
use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\Facility;
use App\Models\FacilityType;
use Auth;

class NeighborhoodController extends Controller
{
    public function index()
    {
        // return Auth::user()->id;
        return Neighborhood::all();
    }

    public function neighborhoodWithFacilityTypes()
    {
        $arr['neighborhoods'] = Neighborhood::all();
        $arr['facility_types'] = FacilityType::all();
        
        return $arr;
    }
    public function facilitiesByNeighborhood()
    {
        return Neighborhood::with('facilities')->get();
    }
    public function getMonthlyCampaignNeighborhood()
    {
    	/*$neighborhoodIdsArray = array();

        $neighborhoodIds = Facility::select('neighborhood_id')->where('monthly_campaign',1)->distinct('neighborhood_id')->get();
        foreach($neighborhoodIds as $neighborhoodId){
        	$neighborhoodIdsArray[] = $neighborhoodId->neighborhood_id;
        }
        if($neighborhoodIdsArray){
        	return Neighborhood::whereIn('id',$neighborhoodIdsArray)->get();
        }
        else{
        	return;
        }*/

        $neighborhoodIdsArray = array();
        $neighborhoods = Neighborhood::all();

        foreach ($neighborhoods as $neighborhood) {

            $request = [
                'longitude' => $neighborhood->longitude,
                'latitude' => $neighborhood->latitude,
                'radius' => 1
            ];
            if (count($this->facilities($request))) {
                $neighborhoodIdsArray[] = $neighborhood->id;
            }
        }
        if ($neighborhoodIdsArray) {
            return Neighborhood::whereIn('id', $neighborhoodIdsArray)->get();
        } else {
            return;
        }
    }

    public function facilities($request = array())
    {
        
        // setup our radius searching against geolocation database
        $R = 3959;  // earth's mean radius, km
        $lon = $request['longitude'];
        $lat = $request['latitude'];
        $rad = $request['radius'];
        
        // first-cut bounding box (in degrees)
        $maxLat = $lat + rad2deg($rad / $R);
        $minLat = $lat - rad2deg($rad / $R);

        // compensate for degrees longitude getting smaller with increasing latitude
        $maxLon = $lon + rad2deg($rad / $R / cos(deg2rad($lat)));
        $minLon = $lon - rad2deg($rad / $R / cos(deg2rad($lat)));

        $lat = deg2rad($lat);
        $lon = deg2rad($lon);

        $condition = " where facilities.id > 0 and facilities.monthly_campaign='1'";



        $facilities = \DB::select('call facility_search("' . $lat . '", "' . $lon . '", "' . $R . '", "' . $minLat . '", "' . $maxLat . '", "' . $minLon . '", "' . $maxLon . '", "' . $rad . '", "' . $condition . '")');
        return $facilities;
    }
}
