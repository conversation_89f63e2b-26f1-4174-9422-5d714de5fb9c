<?php

namespace App\Http\Controllers;

use Auth;
use Config;
use Authorizer;

use Illuminate\Http\Request;
use App\Classes\LoyaltyProgram;
use App\Models\LoyaltyUserAccounts;

use App\Http\Requests;
use App\Models\OauthClient;
use App\Models\User;
use App\Models\Devicetoken;
use DB;
use App\Classes\CommonFunctions;
use App\Models\AffiliateBusiness;
use App\Exceptions\ApiGenericException;
use App\Models\Facility;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Http\Helpers\QueryBuilder;

class AuthController extends Controller
{

    /**
     * Handle request for oauth token (login)
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    protected $countryCode;

    public function grantOauthToken(Request $request)
    {
        //return $request->all();
        $_SESSION['partner_id'] = '';
        $menu = '';
        if ($request->remember_me) {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
            Config::set('oauth2.grant_types.refresh_token.access_token_ttl', 60 * 60 * 24 * 365);
        }

        if (isset($request->client_id) && $request->client_id == 'parkengage-app') {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
        }

        Config::set('oauth2.grant_types.password.access_token_ttl', 60 * 60 * 24 * 365);

        // Get country Code
        $this->countryCode = QueryBuilder::appendCountryCode();

        $client = OauthClient::where('secret', $request->client_secret)->first();

        if ($client && $client->partner_id != '') {

            $partner = User::where('id', $client->partner_id)->first();
            if ($request->client_id == "inventory-demo" && ($request->client_secret == "wqhqdz9pmkmhwug9pz0oowvrz" || $request->client_secret == "Wqhqdz9PMKMHWUG9pZ0Oowvrz")) {
                $userPartner = User::where('email', $request->username)->first();
                $_SESSION['client_id'] = $request->client_id;
                $_SESSION['client_secret'] = $request->client_secret;
            } else {

                if (is_numeric($request->username) == true) {
                    $userPartner = User::where('phone', $this->countryCode . $request->username)->first();
                } else {
                    // PIMS - 13230 : Change Vijay To Prevent Cross partner Login in email same
                    $userPartner = User::where(['email' => $request->username, 'created_by' => $client->partner_id, 'anon' => '0'])->first();
                }
                if (!$userPartner && $request->grant_type != 'user_id_login') {
                    throw new ApiGenericException("The user credentials were incorrect11.");
                }
            }


            //check for anon user for normal user only vijay/vikrant
            if (!empty($client->partner_id) && !empty($userPartner->created_by) && $client->partner_id != $userPartner->created_by && $userPartner->user_type == '5') {
                throw new ApiGenericException('Invalid user details.');
            }

            if ($partner && ($partner->user_type == '3' || $partner->user_type == '1')) {
                $_SESSION['partner_id'] = $client->partner_id;
            } else {
                $_SESSION['partner_id'] = $client->partner_id;
            }
            // change for clerk user & business user			
            if ($userPartner && ($userPartner->user_type == '8' || $userPartner->user_type == '10')) {
                $_SESSION['partner_id'] = $userPartner->created_by;
            }
        }


        $session = Authorizer::issueAccessToken();
        $user = Auth::user();

        // Code to validate latitude and logitute for Validation App - Ujjwal
        if (isset($request->latitude) && $request->latitude != '' && isset($request->longitude) && $request->longitude != '') {
            if ($user->user_type == '4') {
                $result = AffiliateBusiness::where('id', $user->business_id)->where('status', 1)->first();
                if (!$result) {
                    Auth::logout();
                    throw new ApiGenericException('Business Not Associated With User. Please Contact Admin');
                }
                $sourceLat = $result->latitude; // Source latitude
                $sourceLong = $result->longitude; // Source longitude
                $destinationLat = isset($request->latitude) ? $request->latitude : '0.00'; // Destination latitude (very close to source)
                $destinationLong = isset($request->longitude) ? $request->longitude : '0.00'; // Destination longitude (very close to source)
                $radiusInMeters = $result->range; // Radius in meters

                if (CommonFunctions::isWithinRadius($sourceLat, $sourceLong, $destinationLat, $destinationLong, $radiusInMeters)) {
                    $user->msg = config('parkengage.BOUNCE_MSG_1');
                } else {
                    Auth::logout();
                    throw new ApiGenericException(config('parkengage.BOUNCE_MSG_2'));
                }
            }
        }


        if ((($user->qr_code_number == NULL) || ($user->qr_code_number == '')) && ($user->user_type == '5')) {
            $user->qr_code_number =  $user->generateQrCode();
            $user->save();
        }
        //dd($user);
        $is_device_updated = false;

        if (isset($request->device_token) && $request->device_token != '' && isset($request->device_key) && $request->device_key != '' && isset($request->device_type) && $request->device_type != '') {
            // check id device_key already exists in database
            $device_key = $request->device_key;
            $device = Devicetoken::findDeviceWithKey($device_key);


            // Create New Device Token Entry
            if (!$device) {
                $device = new Devicetoken();
            }

            //	$device->fill($request->only(['user_id', 'device_token', 'device_key', 'device_type']));
            $device->user_id = $user->id;
            $device->device_token = $request->device_token;
            $device->device_id = $request->device_key;
            $device->device_type = $request->device_type;
            $device->save();
            $is_device_updated = true;

            //fetch menu to show

        }
        $menu = DB::table('app_menu')->select('menu_name', 'status')->where('partner_id', $user->created_by)->get();
        $menuArray = [];

        if ($menu) {
            foreach ($menu as $key => $value) {
                if ($value->menu_name == "Park") {
                    $menuArray['is_park'] = $value->status;
                }
                if ($value->menu_name == "Reserve") {
                    $menuArray['is_reserve'] = $value->status;
                }
                if ($value->menu_name == "Permits") {
                    $menuArray['is_permit'] = $value->status;
                }
                if ($value->menu_name == "Passes") {
                    $menuArray['is_passes'] = $value->status;
                }
            }
        }

        $brandsettings = DB::table('brand_settings')->select('id', 'color', 'name', 'url')->where('user_id', $user->created_by)->first();
        if ($brandsettings) {
            $logourl  = config('app.url');
            $applogourl = $logourl . '/brand-settings-logo/' . $brandsettings->id;
            $brandsettings->app_logo = $applogourl;
        }

        $authDetails = [];
        if ($user->user_type == '1' || $user->user_type == '3') {
            $client = OauthClient::where('partner_id', $user->id)->first();
            $authDetails['client_id'] = $client->id;
            $authDetails['client_secret'] = $client->secret;
            $showLot = QueryBuilder::getPartnerConfig("IS_SHOW_LOT", $user->id);  #PIMS-12423 DD
        } else {
            $client = OauthClient::where('partner_id', $user->created_by)->first();
            $authDetails['client_id'] = $client->id;
            $authDetails['client_secret'] = $client->secret;
            $showLot = QueryBuilder::getPartnerConfig("IS_SHOW_LOT", $user->created_by);  #PIMS-12423 DD
        }

        $otherDetails = [];
        #PIMS-12423 DD
        /*  $otherDetails['is_show_lot'] = '0';
        if ($user->id == '358811' || $user->created_by == '3156' || $user->created_by == '169163') {
            $otherDetails['is_show_lot'] = '1';
        } */
        if (isset($showLot->key)) {
            $otherDetails['is_show_lot'] =  $showLot->value;
        } else {
            $otherDetails['is_show_lot'] = '0';
        }

        if ($user->user_type == 4 || $user->user_type == 8) {
            if (count($user->userFacility) > 0) {
                $facility_id = $user->userFacility[0]->facility_id;
                $facility = Facility::with(['facilityConfiguration'])->select('mid')->where('id', $facility_id)->first();
                if ($facility) {
                    $user->mid = $facility->mid;
                    $user->unlock_passowrd = isset($facility->facilityConfiguration->unlock_passowrd) ? $facility->facilityConfiguration->unlock_passowrd : '';
                }
            }
        }

        $hotelProfile = $user->user_type == 8 ? true : false;
        if ($user->business_id != '') {
            $businessBrandSetting = AffiliateBusiness::where('id', $user->business_id)->first();
            if ($businessBrandSetting && $businessBrandSetting->logo != NULL) {
                $user->setRelation('brandSetting', $businessBrandSetting);
                return array_merge($session, [
                    "other_details" => $otherDetails,
                    "auth_details" => $authDetails,
                    'user' => $user ? $user->load('photo', 'membershipPlans', 'userFacility', 'affliateBusiness')->toArray() : null,
                    'hotelProfile' => $hotelProfile
                ]);
            } else {
                return array_merge($session, [
                    "other_details" => $otherDetails,
                    "auth_details" => $authDetails,
                    'user' => $user ? $user->load('photo', 'membershipPlans', 'userFacility', 'affliateBusiness', 'brandSetting')->toArray() : null,
                    'hotelProfile' => $hotelProfile
                ]);
            }
        } else {
            $user->setRelation('brandSetting', $brandsettings);
            return array_merge(
                $session,
                [
                    "other_details" => $otherDetails,
                    "auth_details" => $authDetails,
                    'menu' => $menuArray,
                    'brands' => $brandsettings,
                    'user' => $user ? $user->load('photo', 'membershipPlans', 'userFacility', 'affliateBusiness', 'brandSetting')->toArray() : null,
                    'hotelProfile' => $hotelProfile
                ]
            );
        }
    }
}
