<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Exception;
use Artisan;
use Auth;
use App\Models\Facility;
use App\Classes\MagicCrypt;
use App\Models\User;
use App\Models\OauthClient;
use Mail;
use Hash;
use config;
use DateTime;
use Authorizer;
use App\Models\FacilityFee;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Models\UserPass;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Services\LoggerFactory;
use Response;
use Excel;
use App\Models\PermitVehicle;
use Carbon\Carbon;
use App\Models\Rate;
use Illuminate\Support\Facades\DB;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\BlackListedVehicle;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\BrandSetting;
use App\Services\Pdf;
use Illuminate\Support\Facades\Storage;
use App\Classes\HeartlandPaymentGateway;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\SendSms;

class UserPassesController extends Controller
{

    use DispatchesJobs;
    const QUEUE_NAME = 'sms-send';

    /**
     * Get a paginated list of all users
     *
     * @return [type] [description]
     */
    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->log = $logFactory->setPath('logs/buy-userpass')->createLogger('buy-user-pass');
        $this->errorLog = $logFactory->setPath('logs/buy-userpass')->createLogger('buy-pass-error');
        $this->log_upload = $logFactory->setPath('logs/are/passupload')->createLogger('uploadpass');
        $this->userpasslog = $logFactory->setPath('logs/are/userpass')->createLogger('userpass');

        $this->authNet = $authNet;
        $this->cim = $cim;
        $this->request = $request;
        $this->authorizeNetTransaction = null;
    }

    const PASSWORD_PATTERN = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%&*()';

    protected $sendAnet = false;
    protected $anonymousAnet = false;

    public function index(Request $request)
    {
        $today = date("Y-m-d");
        //$user_id = Auth::user()->id;
        //$member_id = $request->member_id;
        $phone = $request->phone;
        //    return UserPass::where('user_id',$user_id)->whereDate('end_date','<=',$today)->with('rate')->get();
        return UserPass::where('phone', $phone)->whereDate('end_date', '>=', $today)->where('remaining_days', '>', 0)->whereNotNull('anet_transaction_id')
            ->with(array('rate' => function ($query) {
                $query->select('id', 'description');
            }))->get();
    }

    //register user
    public function createUserPassByAdmin(Request $request)
    {
        $vehicle = BlackListedVehicle::where('license_plate_number', $request->license_plate)->orderBy('id', 'desc')->first();
        if (isset($vehicle) && !empty($vehicle)) {
            throw new ApiGenericException('License Plate found in Blaklisted/BOLO List');
        }
        $facility = Facility::where('id', $request->facility_id)->first();

        if (!$facility) {
            throw new ApiGenericException("Invalid garage.");
        }
        $this->facility = $facility;

        /*$facilityFee = FacilityFee::where('facility_id',$request->facility_id)->where('name', 'processing_fee')->first();
        if(!$facilityFee){
            throw new ApiGenericException("Please add Facility Fee First."); 
        }
		$this->facilityFee = $facilityFee;	*/

        if (strtotime(date("Y-m-d", strtotime($request->start_date))) < strtotime(date("Y-m-d"))) {
            throw new ApiGenericException("Sorry! User Pass date can not be in past date.");
        }
        $passCheck = '';

        $passData = Rate::where('facility_id', $facility->id)->where('id', $request->pass_id)->first();
        //return $passData;

        if (!$passData) {
            throw new ApiGenericException("Invalid Pass Rate.");
        }
        $this->passData = $passData;
        //return $request->license_plate;

        $isVehicle = PermitVehicle::where('license_plate_number', $request->license_plate)->first();
        //return $isVehicle;

        if (isset($isVehicle) && !empty($isVehicle)) {
            $passCheck = UserPass::where('facility_id', $facility->id)->where('email', $request->email)->where('vehicle_id', $isVehicle->id)->where('end_date', '>=', $request->start_date)->first();
            if (isset($passCheck) && !empty($passCheck)) {
                if ($passCheck->remaining_days == '0') {
                } else {
                    $userdata = [
                        'error_msg' => 'A user pass is already issued for the for this user.'
                    ];
                    return $userdata;
                }
            }
        }

        // Get country Code
        $this->countryCode = QueryBuilder::appendCountryCode();
        $partner_id = Auth::user()->id;

        if ($this->request->is_generic_pass == '1' || $this->request->is_generic_pass == 1) {
            $this->request->request->add(['name' => "Passes User"]);
            $this->request->request->add(['email' => "<EMAIL>"]);
            $this->request->request->add(['phone' => $this->countryCode . "9091918181"]);
        }

        //$existPhone = User::where('email',$request->email)->where('created_by', Auth::user()->id)->first();
        $existPhone = User::where('phone', $this->countryCode . $this->request->mobile)->where('created_by', Auth::user()->id)->first();

        //$password = rand ( 10000 , 99999 ); 	
        $password = substr(str_shuffle(self::PASSWORD_PATTERN), 0, 8);
        if ($existPhone) {
            if ($existPhone->password == '' || is_null($existPhone->password)) {
                $existPhone->password = Hash::make($password);
                $existPhone->save();
            }
            $this->user = $existPhone;
            $userDetails = $existPhone;
            $userDetails['user_exists'] = 1;
            $request->request->add(['user_exists' => 1]);
        } else {
            $request->request->add(['user_exists' => 0]);
            $request->request->add(['confirm_password' => $password]);
            $this->user = User::create(
                [
                    'name' => $this->request->first_name . " " . $this->request->last_name,
                    'email' => $this->request->email,
                    'phone' => $this->countryCode . $this->request->mobile,
                    'password' => Hash::make($password),
                    'anon' => true,
                    'user_type' => '5',
                    'license_number' => $request->license_number,
                    'address' => $request->address1,
                    'address2' => $request->address2,
                    'city' => $request->city,
                    'state' => $request->state,
                    'pincode' => $request->zipcode,
                    'created_by' => Auth::user()->id,
                ]
            );
            $userDetails = $this->user;
        }

        $userexist = $request->user_exists;


        // Charge successful, save transaction relationship to it
        $authorized_anet_transaction = new AuthorizeNetTransaction();

        //generic pass code for colonial
        if ($this->request->is_generic_pass == '1' || $this->request->is_generic_pass == 1) {
            if ($this->request->total_pass > 0) {
                for ($i = 0; $i < $this->request->total_pass; $i++) {

                    $authorized_anet_transaction->sent = $this->sendAnet;
                    $authorized_anet_transaction->anonymous = $this->anonymousAnet;
                    $authorized_anet_transaction->user_id = $this->user->id;
                    $authorized_anet_transaction->ip_address = \Request::ip();
                    $authorized_anet_transaction->total = 0;
                    $authorized_anet_transaction->name = $this->user->name;
                    $authorized_anet_transaction->description = "Pass Booking";
                    $authorized_anet_transaction->response_message = "Zero amount transaction";
                    $authorized_anet_transaction->save();

                    $totalAmount = 0;
                    $processing_fee = 0;
                    $totalAmount = $this->passData->price + $processing_fee;
                    $currentDateTime = Carbon::now();
                    // Create our monthly request object
                    $PassRequest = new UserPass();
                    $PassRequest->facility_id = $this->facility->id;
                    $PassRequest->user_id = $this->user->id;
                    $PassRequest->email = $this->request->email;
                    $PassRequest->phone = $this->request->mobile;
                    $PassRequest->total = ($this->request->is_admin == '0') ? $totalAmount : 0;
                    $PassRequest->rate_id = $this->request->pass_id;
                    $PassRequest->purchased_on = $currentDateTime;
                    $PassRequest->start_date = $this->request->start_date;
                    $PassRequest->end_date = $this->request->end_date;
                    $PassRequest->start_time = $this->request->start_date . " " . $this->request->start_time;
                    $PassRequest->end_time = $this->request->end_date . " " . $this->request->end_time;
                    $PassRequest->processing_fee = ($this->request->is_admin == '0') ? $processing_fee : '0.00';
                    $PassRequest->total_days = $this->passData->total_usage;
                    $PassRequest->consume_days = 0;
                    $PassRequest->remaining_days = $this->passData->total_usage;
                    $PassRequest->partner_id = $this->user->created_by;
                    $PassRequest->is_admin = isset($this->request->is_admin) ? $this->request->is_admin : 1;
                    $PassRequest->pass_code = $this->checkQrCode();
                    $PassRequest->license_number = $this->request->license_number;
                    $PassRequest->mer_reference = isset($this->request->reference) ? $this->request->reference : '';
                    $PassRequest->user_consent = isset($this->request->user_consent) ? $this->request->user_consent : 0;
                    $PassRequest->ex_month = isset($this->request->ex_month) ? $this->request->ex_month : '';
                    $PassRequest->ex_year = isset($this->request->ex_year) ? $this->request->ex_year : '';


                    if ($authorized_anet_transaction) {
                        $PassRequest->anet_transaction_id = isset($authorized_anet_transaction->id) ? $authorized_anet_transaction->id : '';
                    }
                    $PassRequest->save();
                }
                return $PassRequest;
            } else {
                throw new ApiGenericException("Invalid total pass count.");
            }
        }

        $authorized_anet_transaction->sent = $this->sendAnet;
        $authorized_anet_transaction->anonymous = $this->anonymousAnet;
        $authorized_anet_transaction->user_id = $this->user->id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = 0;
        $authorized_anet_transaction->name = $this->user->name;
        $authorized_anet_transaction->description = "Pass Booking";
        $authorized_anet_transaction->response_message = "Zero amount transaction";
        $authorized_anet_transaction->save();

        $passRes  = $this->saveUserPassRequestData($authorized_anet_transaction);

        if ($passRes) {

            $vehicle_id = '';
            if (isset($this->request->license_plate) && !empty($this->request->license_plate)) {
                $isExist = PermitVehicle::with('vehicles')->where('license_plate_number', $this->request->license_plate)->first();
                if (isset($isExist) && !empty($isExist)) {
                    $vehicle_id = $isExist->id;
                } else {
                    $vehicle['license_plate_number'] = $this->request->license_plate;
                    $vehicle['make'] = $this->request->make;
                    $vehicle['model'] = $this->request->model;
                    if ($this->request->color) {
                        $vehicle['color'] = $this->request->color;
                    }
                    $vehicle['user_id'] = $this->user->id;
                    $vehicle = PermitVehicle::create($vehicle);
                    $vehicle_id = $vehicle->id;
                }
            }
            $passRes->vehicle_id = $vehicle_id;
            $passRes->save();
            $this->log->info("User saved: " . json_encode($request->all()));
        }

        //send email to customer

        Artisan::queue('diamonduserpassesadmin:email', ['permit_request_id' => $passRes->id, 'request' => $request->all(), 'is_admin' => 1, 'pwd' => $password, 'userexist' => $userexist]);
        $msg = "Thank you for User Pass Booking for " . $facility->full_name . ". Your User Pass #" . $passRes->pass_code . " has been created and valid for the period of " . date('jS M Y', strtotime($passRes->start_date)) . " - " . date('jS M Y', strtotime($passRes->end_date));
        $this->customeReplySms($msg, $this->countryCode . $request->mobile);
        $passRes['userDetails'] = $userDetails;
        return $passRes;
    }

    public function createHotelPass(Request $request)
    {
        //PIMS-12614 06-02-2025
        // Check if 'pass' object exists and validate its nested fields
        try {
            if ($request->has('pass.pass_type')) {
                // Define the basic validation rules
                $rules = [
                    'facility_id' => 'required', // If facility_id is required
                    'pass.license_plate' => 'required',
                    'pass.rate_id' => 'required',
                    'pass.end_time' => 'required',
                ];

                // Validate manually
                $validator = Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    throw new ApiGenericException($validator->errors()->first());
                }
            }


            $userDetails = '';
            $this->user = Auth::user();

            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
            }

            // Validate facility
            $facility = Facility::where('id', $request->facility_id)->first();
            if (!$facility) {
                throw new ApiGenericException("Invalid garage.");
            }
            $this->facility = $facility;
            $pass = $request->pass;
            $this->facilityFee = $facility->processing_fee;

            $request->request->add(['start_date' => date('Y-m-d')]);
            $request->request->add(['end_date' => $pass['end_time']]);
            $request->request->add(['start_time' => date('H:i:s')]);
            $request->request->add(['end_time' => $pass['end_time']]);

            if (strtotime(date("Y-m-d", strtotime($request->start_date))) < strtotime(date("Y-m-d"))) {
                throw new ApiGenericException("Sorry! User Pass date can not be in past date.");
            }
            $passCheck = '';
            $passData = Rate::where('facility_id', $facility->id)->first();

            if (!$passData) {
                throw new ApiGenericException("Invalid Pass Rate.");
            }
            $this->passData = $passData;

            $isVehicle = PermitVehicle::where('license_plate_number', $pass['license_plate'])->first();

            if (isset($isVehicle) && !empty($isVehicle)) {
                $passCheck = UserPass::where('facility_id', $facility->id)->where('phone', $pass['mobile'])->where('vehicle_id', $isVehicle->id)->where('end_date', '>=', $request->start_date)->first();
                if (isset($passCheck) && !empty($passCheck)) {
                    if ($passCheck->remaining_days == '0') {
                    } else {
                        $userdata = [
                            'error_msg' => 'A user pass is already issued for the for this user.'
                        ];
                        return $userdata;
                    }
                }
            }

            // Get country Code
            $this->countryCode = QueryBuilder::appendCountryCode();

            // $existPass = UserPass::where('phone', $pass['mobile'])->first();

            // if ($existPass && !empty($pass['mobile'])) 
            // {
            //     throw new ApiGenericException('Pass for this user is already exist.');
            // }
            if (!empty($pass['mobile'])) {
                $countryCode = QueryBuilder::appendCountryCode();

                $existPhone = User::where('phone', $countryCode . $pass['mobile'])->where('created_by', $this->user->created_by)->first();

                if ($existPhone) {
                    $userid = $existPhone->id;
                } else {
                    $data = User::create(
                        [
                            'name' =>  $pass['customer_name'],
                            'email' => '',
                            'phone' => $countryCode . $pass['mobile'],
                            'password' => Hash::make(str_random(60)),
                            'anon' => true,
                            'user_type' => '5',
                            'created_by' => $facility->owner_id
                        ]
                    );

                    $userid = $data->id;
                }
            }

            $request->request->add(['mobile' => $pass['mobile']]);
            $request->request->add(['email' => '']);

            if ($facility->active == 0) {
                throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
            }

            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();

            $existingRecord = DB::table('user_passes')
                ->where('license_plate', $pass['license_plate'])
                ->where('facility_id', $this->facility->id)
                ->where('user_id', $this->user->id)
                ->whereBetween(DB::raw("DATE(start_date)"), [$this->request->start_date, $this->request->end_date])
                // ->whereBetween(DB::raw("TIME(start_time)"), [$this->request->start_time, $this->request->end_time])
                ->exists();

            if ($existingRecord) {
                throw new ApiGenericException('Record already exists.');
            }

            if (isset($pass['end_time']) && !empty($pass['end_time'])) {
                //PIMS-12614 06-02-2025
                $startdate = new DateTime(); // Current date
                $enddate = new DateTime(date('Y-m-d', strtotime($pass['end_time']))); // Example end date
                $interval = $startdate->diff($enddate); // Calculate the difference between the two dates

                // Get the number of days from the interval
                $days = $interval->days;

                $passRes  = $this->saveUserPassRequestData($this->authorizeNetTransaction, $userid);
                $passRes->license_plate = $pass['license_plate'];
                $passRes->license_number = $pass['license_plate'];
                $passRes->created_by = $this->user->id;
                $passRes->total_days = $days;
                $passRes->name = $pass['customer_name'];
                $passRes->rate_id = $pass['rate_id'];
                $passRes->pass_type = isset($pass['pass_type']) ? $pass['pass_type'] : 0;
                $passRes->total = $this->passData->price * $days;
                $passRes->room_number = isset($pass['room_number']) ? $pass['room_number'] : 0;
                $passRes->name = $pass['customer_name'];
                $passRes->start_date = date('Y-m-d');
                //PIMS-12614 06-02-2025
                $passRes->end_date = date('Y-m-d', strtotime($pass['end_time']));
                $passRes->partner_id = $request->partner_id ?? ($facility->owner_id ?? null);
                $passRes->start_time = date('Y-m-d H:i:s');
                $passRes->end_time = $pass['end_time'];
                $passRes->remaining_days = $days;
                $passRes->save();
                if (isset($pass['mobile'])) {
                    Artisan::queue('pass:email', ['passId' => $passRes->id, 'common' => '1']);
                }
                $passRes['userDetails'] = $userDetails;
                return $passRes;
            } else {
                // Implement more specific logging here for later. (Iparc returned error)
                $this->errorLog->error("Could not create user Pass (User ID: {$this->user->id})");
                throw new ApiGenericException('End date not choose');
            }

            $this->userpasslog->info("Pass creation response: " . json_encode($responsePasses));
        } catch (\Exception $e) {
            $this->errorLog->error("Failed with error response" . json_encode($e->getMessage()));
            throw new ApiGenericException($e->getMessage());
        }
    }


    public function editHotelPass(Request $request)
    {
        // Retrieve the existing record
        //PIMS-12614 06-02-2025
        $editdata = UserPass::find($request->id);

        if (!$editdata) {
            throw new ApiGenericException("User Pass Not Found.");
        }

        //PIMS-12614 06-02-2025
        $startdate = new DateTime($editdata->start_time); // Current date
        $enddate = new DateTime(date('Y-m-d', strtotime($request->end_time))); // Example end date
        $interval = $startdate->diff($enddate); // Calculate the difference between the two dates
        $days = $interval->days;

        // Update fields
        $editdata->name = $request->name;
        $editdata->rate_id = $request->rate_id;
        $editdata->total_days = $days;
        $editdata->license_plate = $request->license_plate;
        $editdata->remaining_days = $days - $editdata->consume_days;

        // Calculate end date and time
        //PIMS-12614 06-02-2025
        $editdata->end_date = date('Y-m-d', strtotime($request->end_time));
        $editdata->end_time = $request->end_time;

        // Save changes
        $editdata->save();

        $existuser = User::where('id', $editdata->user_id)->first();
        $existuser->name = $request->name;
        $existuser->save();

        return $editdata;
    }



    public function extendPass(Request $request)
    {
        $this->userpasslog->info("Extend Pass request received: " . json_encode($request->all()));
        $this->user_id = Auth::user()->id;

        // Validate facility
        $facility = Facility::where('id', $request->facility_id)->first();
        if (!$facility) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid garage.',
            ], 400);
        }
        $this->facility = $facility;

        $passs = $request->pass;

        // Find the pass
        $userPass = UserPass::where('id', $passs['pass_id'])
            // ->where('facility_id', $facility->id)
            ->first();

        if (!$userPass) {
            // Pass not found for this facility
            throw new ApiGenericException("There is no existing pass data");
        }

        if (isset($passs['days']) && !empty($passs['days'])) {
            // Update total_days with the new value
            $userPass->total_days += (int)$passs['days'];
            $userPass->end_date = date('Y-m-d', strtotime('+' . $userPass->remaining_days . ' day'));
            $userPass->save();
        } else {
            throw new ApiGenericException("The number of pass days shouldn't be empty");
        }

        // Return the response
        return response()->json([
            'user_Id' => $this->user_id,
            'facility_id' => $this->facility->id,
        ], 200);
    }

    public function saveUserPassRequestData($authTransaction = null, $userid = null)
    {

        //return $this->request->reference;
        $totalAmount = 0;
        $processing_fee = 0;
        $processing_fee = isset($this->facilityFee->val) ? $this->facilityFee->val : 0;
        $totalAmount = $this->passData->price + $processing_fee;
        $currentDateTime = Carbon::now();
        // Create our monthly request object
        $PassRequest = new UserPass();
        $PassRequest->facility_id = $this->facility->id;
        $PassRequest->user_id = $userid ?? ($this->user->id ?? null);
        $PassRequest->email = $this->request->email;
        $PassRequest->phone = $this->request->mobile;
        $PassRequest->total = ($this->request->is_admin == '0') ? $totalAmount : 0;
        $PassRequest->rate_id = $this->request->pass_id;
        $PassRequest->purchased_on = $currentDateTime;
        $PassRequest->start_date = $this->request->start_date;
        $PassRequest->end_date = $this->request->end_date;
        $PassRequest->start_time = $this->request->start_time;
        $PassRequest->end_time = $this->request->end_time;
        $PassRequest->processing_fee = ($this->request->is_admin == '0') ? $processing_fee : '0.00';
        $PassRequest->total_days = $this->passData->total_usage;
        $PassRequest->consume_days = 0;
        $PassRequest->remaining_days = $this->passData->total_usage;
        $PassRequest->partner_id = $this->user->created_by;
        $PassRequest->is_admin = isset($this->request->is_admin) ? $this->request->is_admin : 1;
        $PassRequest->pass_code = $this->checkQrCode();
        $PassRequest->license_number = $this->request->license_number;
        $PassRequest->mer_reference = isset($this->request->reference) ? $this->request->reference : '';
        $PassRequest->user_consent = isset($this->request->user_consent) ? $this->request->user_consent : 0;
        $PassRequest->ex_month = isset($this->request->ex_month) ? $this->request->ex_month : '';
        $PassRequest->ex_year = isset($this->request->ex_year) ? $this->request->ex_year : '';


        if ($authTransaction) {
            $PassRequest->anet_transaction_id = isset($authTransaction->id) ? $authTransaction->id : '';
        }
        $PassRequest->save();
        return $PassRequest;
    }

    protected function checkQrCode()
    {
        $code = 'PA' . rand(10, 99) . rand(100, 999) . rand(100, 999);
        $isExist = UserPass::where('pass_code', $code)->first();
        if ($isExist) {
            $this->checkQrCode();
        }
        return $code;
    }

    public function customeReplySms($msg, $phone, $imageURL = '')
    {

        $accountSid = "**********************************";
        $authToken  = "989ab85afc8ef729dda64cf8b334d77a";

        $client = new Client($accountSid, $authToken);
        try {
            // Use the client to do fun stuff like send text messages!
            $client->messages->create(
                // the number you'd like to send the message to
                $phone,
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => env('TWILIO_PHONE'),
                    // the body of the text message you'd like to send
                    'body' => "$msg",
                )
            );
            $this->log->info("Message : {$msg} sent to $phone");
            return "success";
        } catch (RestException $e) {
            //echo "Error: " . $e->getMessage();
            $this->log->error($e->getMessage());
            return "success";
        }
    }


    public function getUserPassByid($id)
    {

        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12' || Auth::user()->user_type == '8' || Auth::user()->user_type == '10') {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        #PIMS: 11295
        #Kuldeep

        $query = UserPass::with(['user', 'transaction', 'vehicle', 'facility', 'ticket', 'permitTicket'])
            ->leftJoin('refund_transaction', 'refund_transaction.refund_ticket_id', '=', 'user_passes.id')
            ->leftJoin('rates', 'rates.id', '=', 'user_passes.rate_id')
            ->where('user_passes.id', $id)
            ->select('user_passes.*', 'rates.description as pass_name', 'refund_transaction.*');

        if (Auth::user()->user_type != '1') {
            $query->where('user_passes.partner_id', $partner_id);
        }

        $userPass = $query->first();
        // dd($query->toSql(), $query->getBindings());
        if (!$userPass) {
            throw new ApiGenericException("User Pass Not Found.");
        }
        $userPass['parking_amount'] = $userPass->total - $userPass->processing_fee - $userPass->tax_fee;
        //changes by vikrant for USM pass flow party pass
        $qrCode = UserPass::with(['mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event'])->where("id", $id)->first();
        if (isset($qrCode->mapcoQrCode[0])) {
            $userPass['qrcodes']  = $qrCode->mapcoQrCode[0]->eventCategory;
        }
        return $userPass;
    }

    public function deleteUserPass($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $is_user_pass = UserPass::find($id);
        } else {
            $is_user_pass = UserPass::where('partner_id', $partner_id)->find($id);
        }
        $is_user_pass = UserPass::find($id);
        //return $is_user_pass;
        if (!$is_user_pass) {
            throw new ApiGenericException("User Pass Not Found.");
        }
        $permitVehicle = PermitVehicle::find($is_user_pass->vehicle_id);
        if ($permitVehicle) {
            $permitVehicle->delete();
        }
        $is_user_pass->delete();

        //Artisan::queue('are:delete-userpass-email', ['id' => $id]);
        return "Data successfully deleted.";
    }

    public function getUserPassByUserid(Request $request)
    {
        // $userPass = UserPass::with(['user', 'transaction', 'vehicle', 'rate', 'facility', 'ticket' => function ($query) use ($request) {
        //     $query->where('is_checkout', '0');
        // }])->whereNotNull('anet_transaction_id')->where('user_id', Auth::user()->id)->orderBy("id", "DESC")->paginate(10);
        $userPass = UserPass::with(['user', 'transaction', 'vehicle', 'rate', 'facility', 'ticket'])->where('user_id', Auth::user()->id)->orderBy("id", "DESC")->paginate(10);
        if (!$userPass) {
            throw new ApiGenericException("User Pass Not Found.");
        }
        return $userPass;
    }

    public function userpassesBulkImportData(Request $request)
    {
        $pass_id = $request->pass_id;
        $facility_id  = $request->facility_id;
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime('+1 year'));
        $start_time = date('H:i:s');
        $end_time = date('23:59:59');

        if ($request->file('input_importfile')) {
            $inputfile = $request->file('input_importfile');
            $ext = $inputfile->getClientOriginalExtension();

            if ($ext == 'xlsx') {
                $fileName = time() . '_userpassesbulkfile.' . $ext;
                $destination_path = storage_path("import/");
                $inputfile->move($destination_path, $fileName);
                $file = storage_path('import/') . $fileName;
                $data = Excel::load($file)->get();
                $newdata = $data->toArray();

                if (!empty($newdata)) {
                    $headerCheck = Excel::selectSheetsByIndex(0)->load($file, function ($reader) {})->get()->toArray();
                    //return $headerCheck;

                    $chkfile = 0;
                    $chkfile1 = 0;
                    foreach ($headerCheck as $ex) {
                        if (isset($ex["license_plate_3_to_10_characters_only"]) && isset($ex["make"]) && isset($ex["model"]) && isset($ex["license_number"]) && isset($ex["first_name"]) && isset($ex["last_name"]) && isset($ex["email"]) && isset($ex["phone"])) {
                            if ((strlen($ex["license_plate_3_to_10_characters_only"]) >= 3) && (strlen($ex["license_plate_3_to_10_characters_only"]) <= 10)) {
                            } else {
                                $chkfile1 = 1;
                            }
                        } else {
                            $chkfile = 1;
                        }
                    }
                    if ($chkfile1 == 1) {
                        throw new ApiGenericException("License Plate Number Should be 3 to 10 Characters Only");
                    }
                    //return $chkfile;
                    $chkupload = 0;

                    if ($chkfile != 1) {
                        $passData = '';
                        $failedData = array();
                        $faileduserData = array();
                        $blackData = array();
                        foreach ($data as $key => $value) {
                            $isVehicle = PermitVehicle::select('id')->where('license_plate_number', $value['license_plate_3_to_10_characters_only'])->orderBy('id', 'DESC')->first();

                            $vehicleBlack = BlackListedVehicle::where('license_plate_number', $value['license_plate_3_to_10_characters_only'])->orderBy('id', 'desc')->first();


                            if (!empty($isVehicle)) {
                                $failedData[] = $value['license_plate_3_to_10_characters_only'];
                                continue;
                            } elseif (!empty($vehicleBlack)) {
                                $blackData[] = $value['license_plate_3_to_10_characters_only'];
                                continue;
                            } else {
                                $passCheck = UserPass::where('facility_id', $facility_id)->where('email', $value['email'])->orderBy('id', 'DESC')->first();

                                if (!empty($passCheck)) {
                                    if ($passCheck->remaining_days != '0') {
                                        $faileduserData[] = $value['email'];
                                        continue;
                                    }
                                }
                                $passData = array(
                                    "facility_id" => $facility_id,
                                    "pass_id" => $pass_id,
                                    "license_plate" => $value['license_plate_3_to_10_characters_only'],
                                    "make" => $value['make'],
                                    "model" => $value['model'],
                                    "color" => $value['color'],
                                    "license_number" => $value['license_number'],
                                    "first_name" => $value['first_name'],
                                    "last_name" => $value['last_name'],
                                    "email" => $value['email'],
                                    "mobile" => $value['phone'],
                                    "address1" => $value['address1'],
                                    "address2" => $value['address2'],
                                    "city" => $value['city'],
                                    "state" => $value['state'],
                                    "pincode" => $value['zip_code'],
                                    "start_date" => $start_date,
                                    "end_date" => $end_date,
                                    "start_time" => $start_time,
                                    "end_time" => $end_time
                                );
                                //return $passData;
                                $request->request->add($passData);
                                $result = $this->createUserPassByAdmin($request);
                            }
                        }

                        if (!empty($failedData) || !empty($faileduserData) || !empty($blackData)) {
                            $chkupload = 1;
                            $failList = implode(',', $failedData);
                            $failuserList = implode(',', $faileduserData);
                            $blackList = implode(',', $blackData);
                            $data = json_encode($failedData);
                            $failedData = json_decode($data, JSON_UNESCAPED_SLASHES);
                            $data2 = json_encode($faileduserData);
                            $faileduserData = json_decode($data2, JSON_UNESCAPED_SLASHES);

                            $blackData = json_encode($blackData);
                            $blackData = json_decode($blackData, JSON_UNESCAPED_SLASHES);

                            if ($failuserList !== '' && $failList != '')
                                $this->log_upload->error('Issue in Uploading due To User Emails: ' . $failuserList . ' License Plates: ' . $failList);
                            elseif ($failuserList !== '')
                                $this->log_upload->error('Issue in Uploading due To User Emails: ', $faileduserData);
                            elseif ($blackList !== '')
                                $this->log_upload->error('Issue in Uploading due To Blacklisted/BOLO License Plates: ', $blackData);
                            else
                                $this->log_upload->error('Issue in Uploading due To License Plates: ', $failedData);
                        } else {
                        }
                    } else {
                        unlink(storage_path('import/' . $fileName));
                        //throw new ApiGenericException("Invalid File Format");
                        throw new ApiGenericException("Some information data or Columns Missing");
                        $fileData = [
                            'error_msg' => 'Some information data or Columns Missing'
                        ];
                        return $fileData;
                    }

                    if ($chkupload == 1) {
                        if ($failuserList !== '' && $failList != '')
                            return 'Data Uploaded Successfully, Duplicate data found for License Plates: ' . $failList . ' User Emails: ' . $failuserList;
                        elseif ($failuserList !== '')
                            return 'Data Uploaded Successfully, Duplicate data found for User Emails: ' . $failuserList;
                        elseif ($blackList !== '')
                            return 'Data Uploaded Successfully, Duplicate data found for Blacklisted/Bolo: ' . $blackList;
                        else
                            return 'Data Uploaded Successfully, Duplicate data found for License Plate: ' . $failList;
                    } else {
                        return 'Data Uploaded Successfully';
                    }
                } else {
                    unlink(storage_path('import/' . $fileName));
                    throw new ApiGenericException("Empty Data in File");
                    $fileData = [
                        'error_msg' => 'Empty Data in File'
                    ];
                    //throw new ApiGenericException("Empty Data");
                    return $fileData;
                }
            }
        }
    }


    public function downloadSampleBulkUserPassesFile()
    {
        $excelSheetName = ucwords(str_replace(' ', '', 'sampleBulkUserPasses'));
        $Columns[] = [
            'License Plate (3 to 10 Characters Only)' => '',
            'Make' => '',
            'Model' => '',
            'Color' => '',
            'License Number' => '',
            'First Name' => '',
            'Last Name' => '',
            'Email' => '',
            'Phone' => '',
            'Address1' => '',
            'Address2' => '',
            'City' => '',
            'State' => '',
            'Zip Code' => '',
        ];

        Excel::create(
            $excelSheetName,
            function ($excel) use ($Columns, $excelSheetName) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('sampleBulkUserPasses')->setCompany('ParkEngage');
                $excel->setDescription('Sample File of Bulk User Passes Data');


                // Build the spreadsheet, passing in the payments array
                if (isset($Columns) && !empty($Columns)) {
                    $excel->sheet(
                        'Bulk User Passes Data',
                        function ($sheet) use ($Columns) {
                            $sheet->fromArray($Columns, null, 'A1', false, true);
                            $sheet->freezeFirstRow('A2');
                        }
                    );
                } else {
                    throw new ApiGenericException('Sorry! No Data Found.');
                }
            }
        )->store('xlsx')->download('xlsx');
    }

    public function getUserPasses(Request $request)
    {

        $result = [];
        if (!isset($request->partner_id)) {
            if (Auth::user()->user_type == '1') {
                $result  = [];
                $result["total"] = 0;
                $result["per_page"] = 20;
                $result["current_page"] = 1;
                $result["last_page"] = 1;
                $result["next_page_url"] = Null;
                $result["prev_page_url"] = Null;
                $result["from"] = Null;
                $result["to"] = Null;
                $result["data"] = [];
                return $result;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $result  = [];
                $result["total"] = 0;
                $result["per_page"] = 20;
                $result["current_page"] = 1;
                $result["last_page"] = 1;
                $result["next_page_url"] = Null;
                $result["prev_page_url"] = Null;
                $result["from"] = Null;
                $result["to"] = Null;
                $result["data"] = [];
                return $result;
            }
        } else {
            $partner_id = $request->partner_id;
        }

        $partner_id = $partner_id;

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $pass = UserPass::with('user');

        if (isset($request->search)) {
            $pass = $pass->where('id', 'like', "%$request->search%");
            $pass = $pass->orWhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('name', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%");
                }
            );
        }

        $pass = $pass->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
        if ($request->facility_id != '') {
            $pass = $pass->where('facility_id', $request->facility_id);
        }
        $pass = $pass->where(function ($query) use ($partner_id) {
            $query->where('partner_id', $partner_id);
        });

        if ($request->sort != '') {
            if ($request->sort == 'email') {
                $pass = $pass->orderBy($request->sort, $request->sortBy);
            }
        } else {
            $pass = $pass->orderBy('id', 'DESC');
        }

        $pass = $pass->paginate(20);
        if ($request->sort == 'name') {
            if (count($pass) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($pass); $i++) {
                        for ($j = $i + 1; $j < count($pass); $j++) {
                            if ($pass[$i]['user']->name > $pass[$j]['user']->name) {
                                $temp = $pass[$i];
                                $pass[$i] = $pass[$j];
                                $pass[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($pass); $i++) {
                        for ($j = $i + 1; $j < count($pass); $j++) {
                            if ($pass[$i]['user']->name < $pass[$j]['user']->name) {
                                $temp = $pass[$i];
                                $pass[$i] = $pass[$j];
                                $pass[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }

        return $pass;
    }

    //register user
    public function store(Request $request)
    {

        $userDetails = '';
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }

        $facility = Facility::where('id', $request->facility_id)->first();
        if (!$facility) {
            throw new ApiGenericException("Invalid garage.");
        }
        $this->facility = $facility;

        $facilityFee = FacilityFee::where('facility_id', $request->facility_id)->where('partner_id', $secret->partner_id)->where('name', 'processing_fee')->first();
        $this->facilityFee = $facilityFee;

        $futureDate = date('Y-m-d', strtotime('+1 year'));
        $request->request->add(['start_date' => date('Y-m-d')]);
        $request->request->add(['end_date' => $futureDate]);
        $request->request->add(['start_time' => date('H:i:s')]);
        $request->request->add(['end_time' => "23:59:59"]);


        if (strtotime(date("Y-m-d", strtotime($request->start_date))) < strtotime(date("Y-m-d"))) {
            throw new ApiGenericException("Sorry! User Pass date can not be in past date.");
        }

        $passCheck = '';

        $passData = Rate::where('facility_id', $facility->id)->where('id', $request->pass_id)->first();

        if (!$passData) {
            throw new ApiGenericException("Invalid Pass Rate.");
        }
        $this->passData = $passData;

        $isVehicle = PermitVehicle::where('license_plate_number', $request->license_plate)->first();

        if (isset($isVehicle) && !empty($isVehicle)) {
            $passCheck = UserPass::where('facility_id', $facility->id)->where('email', $request->email)->where('vehicle_id', $isVehicle->id)->where('end_date', '>=', $request->start_date)->first();
            if (isset($passCheck) && !empty($passCheck)) {
                if ($passCheck->remaining_days == '0') {
                } else {
                    $userdata = [
                        'error_msg' => 'A user pass is already issued for the for this user.'
                    ];
                    return $userdata;
                }
            }
        }

        // Get country Code
        $this->countryCode = QueryBuilder::appendCountryCode();

        $existPhone = User::where('email', $request->email)->where('created_by', $secret->partner_id)->first();

        //$password = rand ( 10000 , 99999 ); 

        if ($existPhone) {
            $existPhone->phone = isset($existPhone->phone) ? $existPhone->phone : $this->countryCode . $this->request->mobile;
            $existPhone->save();

            $userDetails['user_exists'] = 1;
            $request->request->add(['user_exists' => 1]);
            if ($request->user_id != '') {
                if ($existPhone->password == '' || is_null($existPhone->password)) {
                    $existPhone->password = Hash::make($request->confirm_password);
                    $existPhone->save();
                }
                $this->user = $existPhone;
                $userDetails = $existPhone;
            } else {
                if ($request->confirm_password != '') {
                    $request->request->add(['username' => $request->email]);
                    $request->request->add(['password' => $request->confirm_password]);
                    $request->request->add(['client_id' => 'vikrant-tyagi356560']);
                    $request->request->add(['client_secret' => '9f3Blizhmt8qhkw62Y16qTJXa']);
                    $request->request->add(['grant_type' => 'password']);
                    $userDetails = $this->loginUser($request);
                    $this->user = $existPhone;
                }
            }
        } else {
            $request->request->add(['user_exists' => 0]);
            //    $request->request->add(['confirm_password' => $password]); 
            $this->user = User::create(
                [
                    'name' => $this->request->first_name . " " . $this->request->last_name,
                    'email' => $this->request->email,
                    'phone' => $this->countryCode . $this->request->mobile,
                    'password' => Hash::make($request->confirm_password),
                    'anon' => true,
                    'user_type' => '5',
                    'license_number' => $request->license_number,
                    'address' => $request->address1,
                    'address2' => $request->address2,
                    'city' => $request->city,
                    'state' => $request->state,
                    'pincode' => $request->zipcode,
                    'created_by' => $secret->partner_id,
                ]
            );

            $request->request->add(['username' => $request->email]);
            $request->request->add(['password' => $request->confirm_password]);
            $request->request->add(['client_id' => 'vikrant-tyagi356560']);
            $request->request->add(['client_secret' => '9f3Blizhmt8qhkw62Y16qTJXa']);
            $request->request->add(['grant_type' => 'password']);
            $userDetails = $this->loginUser($request);
        }
        //$password = $request->confirm_password;
        $userexist = $request->user_exists;
        //decrypt the card
        if ($this->request->is_card_req == '1') {
            $this->setDecryptedCard();
        }
        if (!$facility->accept_cc) {
            $this->errorLog->error("Facility with id: {$facility->id} does not accept credit cards. (User ID: {$this->user->id})");
            throw new ApiGenericException("Facility does not accept credit cards");
        }

        if ($facility->active == 0) {
            throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
        }
        $accountNumber = rand(100, 999) . rand(100, 999) . rand(10, 99);
        $totalAmount = $passData->price;
        // Now we will try to create account
        $is_partner = 0;
        if ($this->user->user_type == 3) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
            $is_partner = 1;
        } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();

            if ($this->partnerPaymentDetails) {
                $is_partner = 1;
            } else {
                throw new NotFoundException('No payment gateway details found for this partner.');
            }
        } else {
            $is_partner = 0;
        }

        //need to check and subtract oversize, exotic and electric check to validate amount
        if ($this->request->is_card_req == '1') {
            $rate = "";

            $rateActiveCheck = 0;
            $rateAmountChangeCheck = 0;

            if ($passData->price == $totalAmount) {
                $rateAmountChangeCheck = 1;
                if ($passData->active == 1) {
                    $rateActiveCheck = 1;
                }
            }

            if ($rateAmountChangeCheck == 0) {
                throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
            } else {
                if ($rateActiveCheck == 0) {
                    throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
                } else {

                    if ($totalAmount > 0) {

                        // Save Card Code
                        if (!$this->request->payment_profile_id) {

                            if ($is_partner == 1) {
                                $this->cim
                                    ->setUser($this->user)
                                    ->setFacility($this->facility)
                                    ->isReservation()
                                    ->isPartner($this->partnerPaymentDetails)
                                    ->setBillingAddress($this->getBillingArray())
                                    ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                            } else {
                                $this->cim
                                    ->setUser($this->user)
                                    ->setFacility($this->facility)
                                    ->isReservation()
                                    ->setBillingAddress($this->getBillingArray())
                                    ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                            }

                            $customerProfile = $this->user->cim;
                            //return $customerProfile;
                            if (!$customerProfile) {  // Create a cim for this user so they can store their details and reuse them
                                $details = $this->cim->createCustomerProfile()->isPartner($this->partnerPaymentDetails)->executeCustomerProfileRequest();
                                //	$details = $this->cim->createCustomerProfile()->isPartner($this->partnerPaymentDetails)->executeCustomerProfileRequestAddNewMethod($this->partnerPaymentDetails);
                            } else { // If the user has a cim already, just add a new payment profile
                                $details = $this->cim->createPaymentProfile()->isPartner($this->partnerPaymentDetails)->executePaymentProfileRequest();
                                //	$details = $this->cim->createPaymentProfile()->isPartner($this->partnerPaymentDetails)->executePaymentProfileRequestAddNewMethod();

                            }
                            //return $details;
                            if (isset($details['payment_profile_id']) && (!empty($details['payment_profile_id']))) {
                                $this->request->payment_profile_id = $details['payment_profile_id'];
                            } else {
                                $returnData['account_card'] = array(
                                    'success' => false,
                                    'message' => 'Unable to add Account card  for future reservations.',
                                    'userDetails' => $userDetails
                                );
                            }
                        }


                        if (isset($this->request->payment_profile_id) && !empty($this->request->payment_profile_id)) {
                            // Use our logged in users profile
                            if ($is_partner == 1) {
                                $this->authNet
                                    ->setUser($this->user)
                                    ->setFacility($this->facility)
                                    ->isPartner($this->partnerPaymentDetails)
                                    ->setBillingAddress($this->getBillingArray());
                                $this->authNet
                                    ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                            } else {
                                $this->authNet
                                    ->setUser($this->user)
                                    ->setFacility($this->facility)
                                    ->setBillingAddress($this->getBillingArray());
                                $this->authNet
                                    ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                            }
                            $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->request->payment_profile_id);
                        } else {
                            try {
                                if ($is_partner == 1) {
                                    $this->authNet
                                        ->setUser($this->user)
                                        ->setFacility($this->facility)
                                        ->isPartner($this->partnerPaymentDetails)
                                        ->setBillingAddress($this->getBillingArray());
                                    $this->authNet
                                        ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                                } else {
                                    $this->authNet
                                        ->setUser($this->user)
                                        ->setFacility($this->facility)
                                        ->setBillingAddress($this->getBillingArray());
                                    $this->authNet
                                        ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                                }
                                // Validate their credit card manually
                                //$this->authNet->validateCardIparc("Authorization test for monthly request {$this->request->email}");
                            } catch (Exception $e) {
                                $this->errorLog->error("Could not validate the card from Auth.Net (User ID: {$this->user->id}): " . json_encode($e->getMessage()));
                                $error_message = $e->getMessage();
                                if ($error_message == "Error getting valid response from api. Check log file for error details") {
                                    throw new ApiGenericException("The system is not currently available. Please try again later.");
                                }
                                if (
                                    strpos(strtolower($error_message), 'card has expire') !== false
                                    || strpos(strtolower($error_message), 'expiration date is invalid') !== false
                                    || strpos(strtolower($error_message), 'card number is invalid') !== false
                                ) {
                                    throw new ApiGenericException("We were unable to authorize the credit card you provided. Please confirm the card info or use another card and try again.");
                                }
                                throw $e;
                            }
                        }
                    }
                }
            }
        }

        if ($accountNumber > 0) {
            if ($totalAmount > 0) {
                // Making AuthNet transaction for pending capture
                $reservationMode = (substr($request->description, 0, 6) === "Mobile") ? "Mobile Pass Booking" : "Pass Booking";
                // return $reservationMode;
                try {

                    if ($is_partner == 1) {
                        $charge =  $this->authNet->createTransaction(
                            $totalAmount,
                            "{$reservationMode}",
                            $accountNumber
                        )->isPartner($this->partnerPaymentDetails)->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur    
                    } else {

                        $charge =  $this->authNet->createTransaction(
                            $totalAmount,
                            "{$reservationMode}",
                            $accountNumber
                        )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    }


                    $this->authorizeNetTransaction = AuthorizeNetTransaction::where('anet_trans_id', $charge['anet_trans_id'])->first();

                    if (count($charge) === 0) {
                        $this->errorLog->error("Auth.Net API Failed to get valid response (User ID: {$this->user->id})");
                        throw new ApiGenericException("Error in authorize.net api.");
                    }
                } catch (\Exception $e) {
                    $this->errorLog->error("Transaction Failed with error response (User ID: {$this->user->id}): " . json_encode($e->getMessage()));
                    $userdata = [
                        'userDetails' => $userDetails,
                        'error_msg' => $e->getMessage()
                    ];
                    return $userdata;
                    // throw new ApiGenericException($e->getMessage());
                }
            }

            //save all values to monthly resuest table
            // $permitRes  = $this->savePermitRequestData($accountNumber,'',$this->authorizeNetTransaction);
            $passRes  = $this->saveUserPassRequestData($this->authorizeNetTransaction);

            if ($passRes) {

                $vehicle_id = '';
                if (isset($this->request->license_plate) && !empty($this->request->license_plate)) {
                    $isExist = PermitVehicle::with('vehicles')->where('license_plate_number', $this->request->license_plate)->first();
                    if (isset($isExist) && !empty($isExist)) {
                        $vehicle_id = $isExist->id;
                    } else {
                        $vehicle['license_plate_number'] = $this->request->license_plate;
                        $vehicle['make'] = $this->request->make;
                        $vehicle['model'] = $this->request->model;
                        if ($this->request->color) {
                            $vehicle['color'] = $this->request->color;
                        }
                        $vehicle['user_id'] = $this->user->id;
                        $vehicle = PermitVehicle::create($vehicle);
                        $vehicle_id = $vehicle->id;
                    }
                }
                $passRes->vehicle_id = $vehicle_id;
                $passRes->save();
                $this->log->info("User saved: " . json_encode($request->all()));
            }

            //send email to customer

            Artisan::queue('diamonduserpassesadmin:email', ['permit_request_id' => $passRes->id, 'request' => $request->all(), 'is_admin' => 0, 'pwd' => $request->confirm_password, 'userexist' => $userexist]);
            $msg = "Thank you for User Pass Booking for " . $facility->full_name . ". Your User Pass #" . $passRes->pass_code . " has been created and valid for the period of " . date('jS M Y', strtotime($passRes->start_date)) . " - " . date('jS M Y', strtotime($passRes->end_date));
            $this->customeReplySms($msg, $this->countryCode . $request->mobile);
            $passRes['userDetails'] = $userDetails;
            return $passRes;
        } else {
            // Implement more specific logging here for later. (Iparc returned error)
            $this->errorLog->error("Could not create user Pass (User ID: {$this->user->id})");
            throw new ApiGenericException('Something went wrong while creating the account.');
        }
    }

    public function setDecryptedCard()
    {
        if (isset($this->request->payment_profile_id) && $this->request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($this->request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4]) ? $cardData[4] : '';
        $this->request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code_on_card' => $zipCode
            ]
        );
    }

    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code_on_card ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }

    public function loginUser(Request $request)
    {
        $_SESSION['partner_id'] = '';

        if (isset($request->client_id) && $request->client_id == 'parkengage-app') {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
        }

        // Config::set('oauth2.grant_types.password.access_token_ttl', 60 * 60 * 24 * 365);

        $client = OauthClient::where('secret', $request->client_secret)->first();
        if ($client) {

            $partner = User::where('id', $client->partner_id)->first();
            // return $partner->email;
            if ($partner->user_type == '5') {
                $_SESSION['partner_id'] = $client->partner_id;
            } else {
                $_SESSION['partner_id'] = $client->partner_id;
            }
        }


        $session = Authorizer::issueAccessToken();
        $user = Auth::user();
        return array_merge($session, ['user' => $user ? $user->load('photo', 'membershipPlans')->toArray() : null]);
    }

    public function createUserPassBeforePayment(Request $request)
    {
        $this->userpasslog->info("User Pass request before payment received : " . json_encode($request->all()));

        //return $request->all();



        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }


            $vehicle = BlackListedVehicle::where('license_plate_number', $request->license_plate)->orderBy('id', 'desc')->first();
            if (isset($vehicle) && !empty($vehicle)) {
                throw new ApiGenericException('License Plate found in Blaklisted/BOLO List');
            }
            $facility = Facility::where('id', $request->facility_id)->first();
            if (!$facility) {
                throw new ApiGenericException("Invalid garage.");
            }
            $this->facility = $facility;


            $price = 0;
            $rate = Rate::where('id', $request->pass_id)->where('facility_id', $request->facility_id)->where('rate_type_id', 7)->orderBy('id', 'desc')->first();
            if (!$rate) {
                throw new ApiGenericException('Rate does not match in our database.');
            }

            $facilityFee = FacilityFee::where('facility_id', $request->facility_id)->where('name', 'processing_fee')->first();
            $this->facilityFee = $facilityFee;

            if (strtotime(date("Y-m-d", strtotime($request->start_date))) < strtotime(date("Y-m-d"))) {
                throw new ApiGenericException("Sorry! User Pass date can not be in past date.");
            }
            $passCheck = '';

            $passData = Rate::where('facility_id', $facility->id)->where('id', $request->pass_id)->first();
            if (!$passData) {
                throw new ApiGenericException("Invalid Pass Rate.");
            }
            $this->passData = $passData;

            $isVehicle = PermitVehicle::where('license_plate_number', $request->license_plate)->first();
            //return $isVehicle;

            if (isset($isVehicle) && !empty($isVehicle)) {
                $passCheck = UserPass::where('facility_id', $facility->id)->where('email', $request->email)->where('vehicle_id', $isVehicle->id)->where('end_date', '>=', $request->start_date)->first();
                if (isset($passCheck) && !empty($passCheck)) {
                    if ($passCheck->remaining_days == '0') {
                    } else {
                        throw new ApiGenericException('A user pass is already issued for this user.');
                    }
                }
            }

            // Get country Code
            $this->countryCode = QueryBuilder::appendCountryCode();

            //$existPhone = User::where('email',$request->email)->where('created_by', $facility->owner_id)->first();	

            if (isset($this->request->user_id) && !empty($this->request->user_id)) {
                $existPhone = User::where('id', $this->request->user_id)->first();
                $this->user = $existPhone;
            } else if ($this->request->mobile != '') {
                $existPhone = User::where('phone', $this->countryCode . $this->request->mobile)->where('created_by', $facility->owner_id)->first();
            } else {
                throw new ApiGenericException("Please enter valid phone number.");
            }
            //return $existPhone;
            //$this->user='';
            if ($existPhone) {
                if ($existPhone->password == '' || is_null($existPhone->password)) {
                    $existPhone->password = Hash::make($request->confirm_password);
                }
                $existPhone->city = isset($existPhone->city) ? $existPhone->city : $request->city;
                $existPhone->phone = isset($existPhone->phone) ? $existPhone->phone : $this->countryCode . $this->request->mobile;
                $existPhone->state = isset($existPhone->state) ? $existPhone->state : $request->state;
                $existPhone->save();

                //return $existPhone;
                if ($request->user_id != '') {
                    $this->user = $existPhone;
                    $userDetails = $existPhone;
                } else {
                    if ($request->confirm_password != '') {
                        $request->request->add(['username' => $request->email]);
                        $request->request->add(['password' => $request->confirm_password]);
                        $request->request->add(['client_id' => 'vikrant-tyagi356560']);
                        $request->request->add(['client_secret' => '9f3Blizhmt8qhkw62Y16qTJXa']);
                        $request->request->add(['grant_type' => 'password']);

                        $userDetails = $this->loginUser($request);
                        $this->user = $existPhone;
                    }
                }
            } else {

                $this->user = User::create(
                    [
                        'name' => $this->request->first_name . " " . $this->request->last_name,
                        'email' => $this->request->email,
                        'phone' => $this->countryCode . $this->request->mobile,
                        'password' => Hash::make($request->confirm_password),
                        'anon' => true,
                        'user_type' => '5',
                        'license_number' => $request->license_number,
                        'address' => $request->address1,
                        'address2' => $request->address2,
                        'city' => $request->city,
                        'state' => $request->state,
                        'pincode' => $request->zipcode,
                        'created_by' => $facility->owner_id,
                    ]
                );

                $request->request->add(['username' => $request->email]);
                $request->request->add(['password' => $request->confirm_password]);
                $request->request->add(['client_id' => 'vikrant-tyagi356560']);
                $request->request->add(['client_secret' => '9f3Blizhmt8qhkw62Y16qTJXa']);
                $request->request->add(['grant_type' => 'password']);
                $userDetails = $this->loginUser($request);
            }
            $passDelete = UserPass::where("user_id", $this->user->id)->whereNull("anet_transaction_id")->delete();

            //    if($userDetails->isAdmin==false)
            $this->request->is_admin = 0;

            $passRes  = $this->saveUserPassRequestData();

            if ($passRes) {

                $vehicle_id = '';
                if (isset($this->request->license_plate) && !empty($this->request->license_plate)) {
                    $isExist = PermitVehicle::with('vehicles')->where('license_plate_number', $this->request->license_plate)->first();
                    if (isset($isExist) && !empty($isExist)) {
                        $vehicle_id = $isExist->id;
                        $passRes->license_plate = $isExist->license_plate_number;
                    } else {
                        $vehicle['license_plate_number'] = $this->request->license_plate;
                        $vehicle['make'] = $this->request->make;
                        $vehicle['model'] = $this->request->model;
                        if ($this->request->color) {
                            $vehicle['color'] = $this->request->color;
                        }
                        $vehicle['user_id'] = $this->user->id;
                        $vehicle = PermitVehicle::create($vehicle);
                        $vehicle_id = $vehicle->id;
                        $passRes->license_plate = $this->request->license_plate;
                    }
                }
                $passRes->vehicle_id = $vehicle_id;
                $passRes->save();
                $this->userpasslog->info("User saved: " . json_encode($request->all()));
            }

            $passRes['userDetails'] = $userDetails;
            return $passRes;
        }
    }


    public function planetPaymentSession(Request $request)
    {


        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }

            $facility = Facility::find($request->facility_id);
            if (!$facility) {
                throw new ApiGenericException('Invalid garage.');
            }
            $price = 0;
            $rate = Rate::where('id', $request->pass_id)->where('facility_id', $request->facility_id)->where('rate_type_id', 7)->orderBy('id', 'desc')->first();
            if (!$rate) {
                throw new ApiGenericException('Rate does not match in our database.');
            }
            $facilityFee = FacilityFee::where('facility_id', $request->facility_id)->where('name', 'processing_fee')->first();

            $tax_rate = $facilityFee->val;
            $price = $rate->price + $tax_rate;
            // $price = $rate->price; 		   
            $price = number_format($price, 2);

            //return $price;


            //$posturl_success = "https://staging-api.parkengage.com/userpass-post-success";
            //$posturl_fail = "https://staging-api.parkengage.com/userpass-post-fail";
            //$payment_success = "https://staging-permit.parkengage.com/userpass-after-payment";
            //$payment_fail = "https://staging-permit.parkengage.com/userpass-payment-failed";

            //   $payment_success = "http://localhost:3000/userpass-after-payment";
            //   $payment_fail = "http://localhost:3000/userpass-payment-failed";
            $posturl_success = config('parkengage.DEMO_CUSTOMER_PASS_POST_SUCCESS_URL');
            $posturl_fail = config('parkengage.DEMO_CUSTOMER_PASS_POST_FAIL_URL');
            $payment_success = config('parkengage.DEMO_CUSTOMER_PASS_PLANET_SUCCESS_URL');
            $payment_fail = config('parkengage.DEMO_CUSTOMER_PASS_FAIL_URL');




            $cent = $price * 100;
            $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);
            $params = [
                'security_emerchant_id' => "ParkEngageTest",
                'security_validation_code' => "ParkEngageTest1!",
                'template_id' => "ParkEngage.xml",
                'trx_amount_currency_code' => "USD",
                'trx_amount_value' => "$cent",
                'trx_options' => "G",
                'posturl_success' => "$posturl_success",
                'posturl_failure' => "$posturl_fail",
                'redirect_approved' => "$payment_success",
                'redirect_declined' => "$payment_fail",
                'trx_merchant_reference' => "$reference",
                'service_action' => "pay"
            ];

            $url = "https://web2payuat.3cint.com/ipage/Service/_2019_09_v1_3_0/IPGService/Initialise";
            $ch = curl_init($url);

            $headers = array(
                "Content-Type: application/json"
            );
            curl_setopt($ch, CURLOPT_HEADER, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            curl_setopt($ch, CURLOPT_POST, 1);
            if ($params !== null) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            }
            $response = curl_exec($ch);
            $result['success'] = true;
            $explode = explode(",", json_decode($response, TRUE));
            $ipgSession = explode(":", $explode[0]);
            $new = ltrim($ipgSession[1], '"');
            $new = rtrim($new, '"');

            if ($new == 'null') {
                throw new ApiGenericException('There is some issue with payment gateway.');
            }

            $charid = strtoupper(md5(uniqid(rand(), true)));
            $hyphen = chr(45);
            $uuid = chr(123)
                . substr($charid, 0, 8) . $hyphen
                . substr($charid, 8, 4) . $hyphen
                . substr($charid, 12, 4) . $hyphen
                . substr($charid, 16, 4) . $hyphen
                . substr($charid, 20, 12)
                . chr(125);
            $data['ipgSession'] =  $new;
            $data['guid'] =  $uuid;
            $data['api_url'] =  "https://web2payuat.3cint.com/iPage/Service/_2006_05_v1_0_1/service.aspx";
            $data['tax_rate'] =  $tax_rate;
            $data['amount'] =  (float) $price;
            //$data['discounted_amount'] =  (float) $discountedAmount;
            $data['reference'] =  $reference;

            return $data;
        }
    }


    public function postSuccess(Request $request)
    {
        $this->userpasslog->info("User Pass Planet Payment Success call Back :" . json_encode($request->all()));

        if ($this->request->Amount > 0) {
            if ($this->request->TokenNo == '') {
                throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
            }

            $alreadyTransactionDone  = AuthorizeNetTransaction::where('ref_id', $this->request->ref)->first();

            if ((count($alreadyTransactionDone) == 0) && isset($this->request->TxID) && !empty($this->request->TxID)) {
                $alreadyTransactionDone  = AuthorizeNetTransaction::where('anet_trans_id', $this->request->TxID)->first();
            }

            if (count($alreadyTransactionDone) > 0) {
                $message = "Pass already created by the user.";
                return ["message" => $message];
            }



            $details = UserPass::with(['user'])->where('mer_reference', $this->request->ref)->first();

            $user = User::where('id', $details['user_id'])->first();
            if (($this->request->TokenNo != '') && isset($details) && !empty($details)) {

                $authorized_anet_transaction = new AuthorizeNetTransaction();
                $authorized_anet_transaction->sent = '1';
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->user_id = $details->user_id;
                $authorized_anet_transaction->total = $details->total;
                $authorized_anet_transaction->description = "User Pass creation Payment Done By User : " . $details->user_id;
                $authorized_anet_transaction->card_type = $this->request->CardType;
                $authorized_anet_transaction->ref_id = $this->request->ref;
                $authorized_anet_transaction->anet_trans_id = $this->request->TxID;
                $authorized_anet_transaction->method = "card";
                $authorized_anet_transaction->payment_last_four = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
                $authorized_anet_transaction->auth_code = isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
                $authorized_anet_transaction->save();

                $details->anet_transaction_id = $authorized_anet_transaction->id;
                $details->save();

                $user->session_id = $this->request->TokenNo;
                $user->save();
                if ($details->user_consent == '1') {
                    $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $details->user_id)->where('card_last_four', $this->request->card_pan_last4digits)->first();
                    if ($cardCheck) {
                        $this->log->info("Card Already Added for this user");
                    } else {
                        $data['user_id'] = $details->user_id;
                        $data['name'] = isset($user->name) ? $user->name : '';
                        $data['partner_id'] = $details->partner_id;
                        $data['card_last_four'] = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
                        $data['card_type'] = isset($this->request->CardType) ? $this->request->CardType : '0';
                        $data['card_name'] = isset($this->request->CardTypeName) ? $this->request->CardTypeName : '0';
                        $data['expiry'] = $details->ex_month . $details->ex_year;
                        $data['token'] = isset($this->request->TokenNo) ? $this->request->TokenNo : '0';
                        $data['tx_state'] = isset($this->request->TxState) ? $this->request->TxState : '0';
                        $data['currency_used'] = isset($this->request->CurrencyCode) ? $this->request->CurrencyCode : '0';

                        $result = PlanetPaymentProfile::create($data);
                    }
                }
                return 1;
            }
        }
    }

    public function postFail(Request $request)
    {
        $this->userpasslog->info("User Pass Planet Payment Fail call Back :" . json_encode($request->all()));
        $details = UserPass::where('mer_reference', $this->request->ref)->delete();
        return 1;
    }

    public function paymentSuccess(Request $request)
    {
        //return $request->all();
        $today = date('Y-m-d');
        $this->userpasslog->info("User Pass after payment request received : " . json_encode($request->all()));

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }


            $facility = Facility::where('id', $request->facility_id)->first();
            if (!$facility) {
                throw new ApiGenericException("Invalid garage.");
            }
            $this->facility = $facility;


            if ($facility->active != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($facility->is_available != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($facility->facility_booking_type == '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if (!$facility->accept_cc) {
                throw new ApiGenericException("Garage does not accept credit cards");
            }



            $details = UserPass::with(['user', 'rate.rateType', 'transaction'])->where('mer_reference', $this->request->MerchantRef)->first();
            //return $details;
            $details->user->address1 = $details->user->address;
            $details->user->zipcode =  $details->user->pincode;

            $user = User::where('id', $details['user_id'])->first();
            //return $user->phone;
            //return $user;
            if ($request->password == '' && $request->confirm_password == '') {
                $userexist = 1;
            } else {
                $userexist = 0;
            }


            if (isset($details) && !empty($details) && $this->request->payment_status == 'success') {
                Artisan::queue('diamonduserpassesadmin:email', ['permit_request_id' => $details->id, 'request' => $request->all(), 'is_admin' => 0, 'pwd' => $request->confirm_password, 'userexist' => $userexist]);
                $msg = "Thank you for User Pass Booking for " . $facility->full_name . ". Your User Pass #" . $details->pass_code . " has been created and valid for the period of " . date('jS M Y', strtotime($details->start_date)) . " - " . date('jS M Y', strtotime($details->end_date));
                $this->customeReplySms($msg, $user->phone);

                return $details;
            } else {
                //return $details;
                throw new ApiGenericException("Error in User Pass Creation.");
            }
        }
    }

    public function paymentFailure(Request $request) {}

    // Planet Payment using save Card	
    public function payment(Request $request)
    {
        $this->log->info("Are after payment request received : " . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
            if (!$checkFacility) {
                throw new NotFoundException('No garage found with this partner.');
            }
            $this->facility = $checkFacility;
            if ($checkFacility->active != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->is_available != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->facility_booking_type == '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if (!$checkFacility->accept_cc) {
                throw new ApiGenericException("Garage does not accept credit cards");
            }


            $price = 0;
            $rate = Rate::where('id', $request->pass_id)->where('facility_id', $request->facility_id)->where('rate_type_id', 7)->orderBy('id', 'desc')->first();
            if (!$rate) {
                throw new ApiGenericException('Rate does not match in our database.');
            }
            $facilityFee = FacilityFee::where('facility_id', $request->facility_id)->where('name', 'processing_fee')->first();

            $tax_rate = $facilityFee->val;
            $price = $rate->price + $tax_rate;
            // $price = $rate->price; 		   
            $price = number_format($price, 2);
            $cent = $price * 100;



            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
				  "Request": {
					  "Type": "payrequestnocardread",
					  "Version": "W2MXG520",
					  "Credentials": {
						  "ValidationID": "ParkEngageTest",
						  "ValidationCode": "ParkEngageTest1!",
						  "ValidationCodeHash": null
					  },
					  "Params": {
						"PaymentOkUrl": "",
						"CardNumber": "' . $request->TokenNo . '",/*Use token from initial CIT to process subsequent MIT payment*/
						"CardExpiryDateMMYY": "",
						"CardStartDateMMYY": "",
						"CardIssueNumber": "",
						"CardCvv2": "",
						"CardholderStreetAddress1": "",
						"CardholderCity": "",
						"CardholderState": "",
						"CardholderZipCode": "",
						"CardholderNameFirst": "",
						"CardholderNameLast": "",
						"Amount": "' . $cent . '",
						"Currency": "USD",
						"RequesterTransRefNum": "TEST MIT PAYMENT 001",
						"UserData1": "",
						"UserData2": "",
						"UserData3": "",
						"UserData4": "",
						"UserData5": "",
						"OptionFlags": "P"
					}
				  }
				}',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            //return gettype($response);
            $refundstatus = json_decode($response, TRUE);
            $request->request->add(['Amount' => $price]);
            $request->request->add(['CardType' => $refundstatus["Response"]["Params"]["CardSchemeId"]]);
            $request->request->add(['TxID' => $refundstatus["Response"]["Params"]["TxID"]]);
            $request->request->add(['card_pan_last4digits' => $refundstatus["Response"]["Params"]["CardNumberLast4"]]);

            if ($refundstatus["Response"]["Params"]['TxState'] == 'AA' || $refundstatus["Response"]["Params"]['TxState'] == 'CQ') {
                $this->postSuccess($request);
            } else {
                $this->postFail($request);
            }

            return $refundstatus;
        }
    }

    public function downloadPassQrCode($id, $facility_id = '')
    {
        $data = [];
        if ($id == "all") {
            $pass = UserPass::with('facility')->where('facility_id', $facility_id)->orderBy("id", "DESC")->get();
            //->where('is_admin', '1')
            if (count($pass) == 0) {
                throw new ApiGenericException('No pass found.');
            }
            $settings = BrandSetting::where('user_id', $pass[0]->facility->owner_id)->first();
            $data['facility_name'] = isset($pass[0]->facility->full_name) ? $pass[0]->facility->full_name : '-';
            $data['pass_code'] = $pass;
            $data['brand_setting'] = $settings;
            $data['all'] = '1';
        } else {
            $pass = UserPass::with('facility')->where('id', $id)->first();
            if (!$pass) {
                throw new ApiGenericException('No pass found.');
            }

            $data['all'] = '0';
            $settings = BrandSetting::where('user_id', $pass->facility->owner_id)->first();
            $data['facility_name'] = isset($pass->facility->full_name) ? $pass->facility->full_name : '-';
            $data['pass_code'] = $pass->pass_code;
            $data['brand_setting'] = $settings;
        }
        $pdf = (new UserPass())->generatePdf($data, Pdf::class);
        return $pdf;
    }

    public function downloadPass($id)
    {
        $data = [];
        $pass = UserPass::with(['user', 'rate'])->where('id', $id)->first();
        if (!$pass) {
            throw new NotFoundException('No pass with that ID.');
        }
        $settings = BrandSetting::where('user_id', $pass->facility->owner_id)->first();
        $data['brand_setting'] = $settings;
        $imageBarcode = $pass->generateBarcodeJpgNew();
        $imageBarcodeFileName = str_random(10) . '_barcode.png';
        Storage::put($imageBarcodeFileName, $imageBarcode);
        $data['bar_image_path'] = $imageBarcodeFileName;
        $pass['start_date'] = date("M d, Y", strtotime($pass->start_date));
        $pass['end_date'] = date("M d, Y", strtotime($pass->end_date));
        $brand_setting = BrandSetting::where('user_id', $pass->partner_id)->first();
        $data['data'] = $pass;
        $pdf = (new UserPass())->generatePdf($data, Pdf::class, '1');
        return $pdf;
    }

    public function passRefund($pass_id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }

        if (Auth::user()->user_type == '1') {
            $pass = UserPass::with(['facility.FacilityPaymentDetails', 'transaction'])->where("id", $pass_id)->first();
        } else {
            $pass = UserPass::with(['facility.FacilityPaymentDetails', 'transaction'])->where('partner_id', $partner_id)->where("id", $pass_id)->first();
        }
        if (!$pass) {
            throw new ApiGenericException("Pass not found.");
        }
        $user_id = $pass->user_id;
        if ($pass) {
            if (Auth::user()->user_type == '1') {
                $partner_id = $pass->partner_id;
            }
            if ($pass->cancelled_at != '') {
                throw new ApiGenericException("Pass is already canceled.");
            }

            if (isset($pass->facility->FacilityPaymentDetails) && ($pass->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                $grandTotal = $pass->total;
                //dd($grandTotal,$pass->pass_code,$pass->transaction->anet_trans_id);
                try {
                    $paymentResponse = HeartlandPaymentGateway::heartlandTicketPaymentRefund($grandTotal, $pass);
                    $this->log->info("Heartland Payment refund Raw #:" .  $pass->pass_code . "--" . json_encode($paymentResponse));
                    if (!isset($paymentResponse) || ($paymentResponse == NULL) || ($paymentResponse == FALSE)) {
                        throw new ApiGenericException('Refund Can not be Initiated Now.');
                    }
                    //if ($paymentResponse->responseMessage == 'Success') {
                    //if ($paymentResponse->responseCode == '00') {	 
                    if (in_array($paymentResponse->responseMessage, ["Success","APPROVAL"])) {    
                        /*
						$pass->refund_transaction_id = $paymentResponse->transactionReference->transactionId;
						$pass->refund_status = "Refunded";
						$pass->refund_amount = $grandTotal;
						$pass->refund_type = isset($request->refund_type) ? $request->refund_type : NULL;
						$pass->refund_date = date("Y-m-d H:i:s");
						$pass->refund_remarks = isset($request->refund_remarks) ? $request->refund_remarks : 'NA';
						$pass->refund_by = Auth::user()->id;
						$pass->save();
						*/
                        $this->log->info("Heartland Payment refund  to Pass Id #:" .  $pass->pass_code . "--" . json_encode($paymentResponse));
                    } else {
                        /*
						$pass->refund_transaction_id = $paymentResponse->transactionReference->transactionId;
						$pass->refund_status = "DECLINED";
						$pass->refund_amount = $grandTotal;
						$pass->refund_type = isset($request->refund_type) ? $request->refund_type : NULL;
						$pass->refund_date = date("Y-m-d H:i:s");
						$pass->refund_remarks = "Refund Failed.";
						$pass->refund_by = Auth::user()->id;
						$pass->save();
						*/
                        $this->log->info("Heartland Payment refund fail to Pass Id #:" . $pass->pass_code . "--" . json_encode($paymentResponse));
                        throw new ApiGenericException('Refund Can not be Initiated Now.');
                    }
                } catch (Exception $e) {
                    $this->log->info("Heartland Payment refund exception #:" .  $pass->pass_code . "--" . json_encode($e->getMessage()));
                    if ($e->getMessage() == "Unexpected Gateway Response: 6 - Transaction rejected because amount to be returned exceeds the original settlement amount or the return amount is zero.. ") {
                        throw new ApiGenericException('Refund Can not be Initiated Now.');
                    }
                }
                return response()->json($paymentResponse);
            }
            /*
			$pass->cancelled_at = date("Y-m-d H:i:s");
			$pass->status = 0;
			$pass->user_consent = 0; #dushyant 24-06-2024
			User::where('id', $user_id)->update(['user_consent' => '0']);
			$pass->save();
			return 'Pass booking successfully canceled.';
			*/
        }
    }

    /**
     * Alka, Date:21-11-2014
     * PIMS-11539
     */
    public function sendExcelEmail(Request $request)
    {
        set_time_limit(0);
        $result = [];
        if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } elseif (Auth::user()->user_type == '4') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                $partner_id = isset($request->partner_id) ? $request->partner_id : '';
            } else {
                $partner_id = Auth::user()->created_by;
            }
        } elseif (Auth::user()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        } elseif (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
        } else {
            $partner_id = $request->partner_id;
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date("Y-m-d", strtotime($request->from_date));
            $to_date = date("Y-m-d", strtotime($request->to_date));
            // dd($from_date,'===',$to_date);
        }
        // $pass = UserPass::with(['user', 'vehicle', 'facility'])
        //     ->leftjoin('refund_transaction', 'refund_transaction.refund_ticket_id', '=', 'user_passes.id')
        //     ->leftjoin('rates', 'rates.id', '=', 'user_passes.rate_id')
        //     ->select('user_passes.*', 'rates.description', 'refund_transaction.refund_type', 'refund_transaction.refund_amount', 'refund_transaction.refund_type', 'refund_transaction.refund_remarks', 'refund_transaction.refund_date', 'refund_transaction.refund_by', 'refund_transaction.refund_transaction_id', 'refund_transaction.refund_status');
        $pass = UserPass::with(['user', 'vehicle', 'transaction', 'facility'])
            ->leftjoin('refund_transaction', 'refund_transaction.refund_ticket_id', '=', 'user_passes.id')
            #PIMS: 11295 Start:
            #Kuldeep
            ->leftjoin('rates', 'rates.id', '=', 'user_passes.rate_id')
            ->leftjoin('anet_transactions', 'anet_transactions.id', '=', 'user_passes.anet_transaction_id')
            ->select('user_passes.*', 'rates.description', 'refund_transaction.refund_type', 'refund_transaction.refund_amount', 'refund_transaction.refund_type', 'refund_transaction.refund_remarks', 'refund_transaction.refund_date', 'refund_transaction.refund_by', 'refund_transaction.refund_transaction_id', 'refund_transaction.refund_status', 'anet_transactions.payment_last_four', 'anet_transactions.card_type', 'anet_transactions.expiration');

        if (isset($request->facility_id) && !empty($request->facility_id)) {
            $pass = $pass->where('user_passes.facility_id', $request->facility_id);
        }

        if (isset($request->search)) {
            $pass = $pass->where(function ($query) use ($request) {
                $query->where('user_passes.id', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.pass_code', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.license_plate', 'like', "%{$request->search}%")
                    ->orWhereHas('user', function ($q) use ($request) {
                        $q->where('name', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    })
                    ->orWhereHas('rate', function ($q) use ($request) {
                        $q->where('description', 'like', "%{$request->search}%");
                    });
            });
        }

        $pass = $pass->whereDate('user_passes.created_at', '>=', $from_date)->whereDate('user_passes.created_at', '<=', $to_date);

        if (isset($partner_id) && $partner_id != '') {
            $pass = $pass->where(function ($query) use ($partner_id) {
                $query->where('user_passes.partner_id', $partner_id);
            });
        }

        $pass = $pass->orderBy('user_passes.id', 'DESC')->get();

        Artisan::queue('send:user-passes-email', ['pass' => $pass, 'email' => $request->emails, 'request' => $request->all()]);
        return 'Email successfully sent to the user.';
    }

    public function sendEmailSms(Request $request){
        $phone = $request->phone;
        $email = $request->email;
        $pass_id = $request->pass_id;
        $pass = UserPass::with(['facility', 'user', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event'])->where('id', $pass_id)->first();
        if ($pass) {
            if (isset($phone) && $phone != '') {
                $countryCode = QueryBuilder::appendCountryCode();
                $phone = $countryCode . $phone;
                $msg = "Thank you for User Pass Booking for " . $pass->facility->full_name . ". Your User Pass #" . $pass->pass_code . " has been created and valid for the period of " . date('jS M Y', strtotime($pass->start_date)) . " - " . date('jS M Y', strtotime($pass->end_date));
                $pass_type = "Pass";
                if ($pass->pass_type == '3') {
                    $pass_type = "Hotel Parking Pass";
                }
                $msg = "Welcome to " . $pass->facility->full_name . "!\n\nYour " . $pass_type . " #" . $pass->pass_code . " has been successfully issued and valid until " . date('jS M Y', strtotime($pass->start_date)) . " - " . date('jS M Y', strtotime($pass->end_date)) . ".\n\nWe hope you have a wonderful stay!";
                dispatch((new SendSms($msg, $phone))->onQueue(self::QUEUE_NAME));
                return "SMS successfully sent.";
            }
            if (isset($email) && $email != '') {
                Artisan::queue('pass:email', ['passId' => $pass->id, 'common' => '1', 'email' => $email]);
                return "Email successfully sent.";
            }
        } else {
            throw new ApiGenericException('Invalid pass details.');
        }
    }

    public function getpasses(Request $request)
    {
        $query = UserPass::with(['ticket' => function ($query) {
            $query->select([
                'id', // Ensure the primary key is selected
                'user_pass_id', // Ensure foreign key is selected
                'ticket_number',
                'check_in_datetime',
                'checkout_datetime',
                'is_checkin',
                'is_checkout',
            ]);
        }])->where('created_by', Auth::user()->id);
    
        // Debugging: Log received request data
        $this->log->info('Request Data:', $request->all());
    
        // Apply date filter if both from_date and to_date are provided
        if ($request->has('from_date') && $request->has('to_date')) {
            $this->log->info('Applying Date Filter:', ['from' => $request->from_date, 'to' => $request->to_date]);
    
            $query->whereDate('start_date', '>=', $request->from_date)
                  ->whereDate('end_date', '<=', $request->to_date);
        }
        // dd($query->toSql(), $query->getBindings());
    
        $userPass = $query->paginate(20);
    
        // Debugging: Log Query Result
        $this->log->info('Query Result:', $userPass->toArray());
    
        if ($userPass->isEmpty()) {
            throw new ApiGenericException("User Pass Not Found.");
        }
    
        return $userPass;
    }
}
