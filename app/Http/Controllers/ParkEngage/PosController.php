<?php
namespace App\Http\Controllers\ParkEngage;
use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\ParkEngage\Gate;
use App\Models\Facility;

use App\Exceptions\ApiGenericException;


class PosController extends Controller
{
	

    public function index(Request $request)
    {
        $facilty ='';
        $partner_id = '';
        if($request->partner_id)
        {
            $partner_id = $request->partner_id;
        }else{
            if(Auth::user()->user_type == '4' || Auth::User()->user_type=='12'){
                $partner_id = Auth::user()->created_by;
                $facilty = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            }elseif(Auth::user()->user_type == '3'){
                $partner_id = Auth::user()->id;
            }            
        }
        $pos = ParkingDevice::with(['facility','gates','parkingDeviceType']);
        if(Auth::user()->user_type == '4' || Auth::User()->user_type=='12'){
            $pos = $pos->whereIn('facility_id',$facilty);
        }

        if($request->facility_id != ''){
            $pos = $pos->where('facility_id',$request->facility_id);
        }
       
        if($request->search !=''){
       
            $pos = $pos->where(function ($query) use ($request) {
                $query->where('terminal_id', "like", "%" . $request->search . "%");
                $query->orWhere('serial_number', "like", "%" . $request->search . "%");
                
            });
          

            $pos = $pos->orWhereHas(
              'gates', function ($query) use ($request) {
                   $query->where('gate_name', 'like', "%{$request->search}%");
                   $query->orwhere('gate_type', 'like', "%{$request->search}%");
                   
            });
            $pos = $pos->orWhereHas(
                'parkingDeviceType', function ($query) use ($request) {
                     $query->where('name', 'like', "%{$request->search}%");
                 
                });
            if(isset($partner_id) && !empty($partner_id)){
                $pos = $pos->where('partner_id',$partner_id);
            }
            
        }
        if(isset($partner_id) && !empty($partner_id)){
            $pos = $pos->where('partner_id',$partner_id);
        }
        
       
         
        if($request->sort != ''){
            if($request->sort != 'gate_name' && $request->sort != 'gate_type' && $request->sort != 'device_type'){
                $pos = $pos->orderBy($request->sort,$request->sortBy);
            }else{
                $pos = $pos->orderBy("id","DESC");
            }
        }else{
          $pos = $pos->orderBy("id","DESC");
        }
         $pos = $pos->paginate(10);

         if($request->sort == 'gate_name'){
            if(count($pos) > 0){
              if($request->sortBy == 'asc' || $request->sortBy == 'Asc'){
              for ($i = 0; $i < count($pos); $i++) {
              for ($j = $i + 1; $j < count($pos); $j++) {
                  if ($pos[$i]['gates']->gate_name > $pos[$j]['gates']->gate_name) {
                      $temp = $pos[$i];
                      $pos[$i] = $pos[$j];
                      $pos[$j] = $temp;
                  }
              }
            }
            }else{
              for ($i = 0; $i < count($pos); $i++) {
              for ($j = $i + 1; $j < count($pos); $j++) {
                  if ($pos[$i]['gates']->gate_name < $pos[$j]['gates']->gate_name) {
                      $temp = $pos[$i];
                      $pos[$i] = $pos[$j];
                      $pos[$j] = $temp;
                  }
              }
            }
            }
  
            }
          }
          if($request->sort == 'gate_type'){
            if(count($pos) > 0){
              if($request->sortBy == 'asc' || $request->sortBy == 'Asc'){
              for ($i = 0; $i < count($pos); $i++) {
              for ($j = $i + 1; $j < count($pos); $j++) {
                  if ($pos[$i]['gates']->gate_type > $pos[$j]['gates']->gate_type) {
                      $temp = $pos[$i];
                      $pos[$i] = $pos[$j];
                      $pos[$j] = $temp;
                  }
              }
            }
            }else{
              for ($i = 0; $i < count($pos); $i++) {
              for ($j = $i + 1; $j < count($pos); $j++) {
                  if ($pos[$i]['gates']->gate_type < $pos[$j]['gates']->gate_type) {
                      $temp = $pos[$i];
                      $pos[$i] = $pos[$j];
                      $pos[$j] = $temp;
                  }
              }
            }
            }
  
            }
          }
          if($request->sort == 'device_type'){
            if(count($pos) > 0){
              if($request->sortBy == 'asc' || $request->sortBy == 'Asc'){
              for ($i = 0; $i < count($pos); $i++) {
              for ($j = $i + 1; $j < count($pos); $j++) {
                  if ($pos[$i]['parkingDeviceType']->name > $pos[$j]['parkingDeviceType']->name) {
                      $temp = $pos[$i];
                      $pos[$i] = $pos[$j];
                      $pos[$j] = $temp;
                  }
              }
            }
            }else{
              for ($i = 0; $i < count($pos); $i++) {
              for ($j = $i + 1; $j < count($pos); $j++) {
                  if ($pos[$i]['parkingDeviceType']->name < $pos[$j]['parkingDeviceType']->name) {
                      $temp = $pos[$i];
                      $pos[$i] = $pos[$j];
                      $pos[$j] = $temp;
                  }
              }
            }
            }
  
            }
          }

         
         return $pos;
    }



    public function store(Request $request)
    {  
         
        //$this->validate($request, ParkingDevice::$validation);
        if(Auth::User()->user_type=='12'){
            $partner_id = Auth::user()->created_by;
        }else{
            $partner_id = Auth::user()->id;
        }        

        $posRes = ParkingDevice::where('serial_number', $request->serial_number);
        if($request->terminal_id != ''){
            $posRes = $posRes->where('terminal_id', $request->terminal_id);    
        }
        $posRes = $posRes->first();
        if($posRes){
              throw new ApiGenericException("Device serial number or terminal id already exist.");
        }
        // check ip and port number should be different. 
/*        $posRes = ParkingDevice::where(['ip'=>$request->device_ip,'port_number'=>$request->port_number])->first();
        if($posRes){
              throw new ApiGenericException("Pos with same IP and Port already exist.");
        }*/

       
        $device = \DB::table('users_payment_gateway_details')->where('user_id',$partner_id)->select('parking_device_id','no_of_device')->first();
        $no_of_device = isset($device->no_of_device)?$device->no_of_device:null;
        $deviceCount = ParkingDevice::where("facility_id", $request->facility_id)->where("partner_id", $partner_id)->count();
         if($deviceCount < $no_of_device) {
         

        $data['serial_number'] = $request->serial_number;
        $data['facility_id'] = $request->facility_id;
        $data['partner_id'] = $partner_id;
        $data['gate_id'] = $request->gate_id;
        $data['device_type_id'] = isset($device->parking_device_id)?$device->parking_device_id:null;
        
        $data['gate_type'] = $request->gate_type;
        $data['terminal_id'] = $request->terminal_id;
        $data['is_active'] = $request->terminal_status;
        $pos = ParkingDevice::create($data);
        return $pos;
        } else {

            throw new ApiGenericException("Device can't add more than $no_of_device.");
        }


    }

    public function show($id)
    {
        if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		}else {
			if (Auth::user()->user_type == '1') {
				
			} else {
				if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
					$partner_id = Auth::user()->created_by;
				}else{
					$partner_id = Auth::user()->id;
				}				
			}
		}
		if (Auth::user()->user_type == '1') {
			$pos = ParkingDevice::with(['gates','facility'])->where('id',$id)->get();
		}else{
			$pos = ParkingDevice::with(['gates','facility'])->where('id',$id)->where('partner_id',$partner_id)->get();
		}
        return $pos;
    }

    public function update(Request $request)
    {    
        // dd($request->all());
        $pos = ParkingDevice::find($request->id);
        if(!$pos){
            throw new ApiGenericException("Pos not found.");
        }

        $facility = Facility::select("owner_id")->where("id", $request->facility_id)->first();
        $pos->serial_number = $request->serial_number;
        $pos->partner_id = $facility->owner_id;
        $pos->facility_id = $request->facility_id;
        $pos->gate_id = $request->gate_id;
        $pos->gate_type = $request->gate_type;
        $pos->terminal_id = $request->terminal_id;
        $pos->is_active = $request->terminal_status;
        $pos->save();
        return $pos;
    }

    public function destroy($id) {
        if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		}else {
			if (Auth::user()->user_type == '1') {
				
			} else {
				if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
					$partner_id = Auth::user()->created_by;
				}else{
					$partner_id = Auth::user()->id;
				}				
			}
		}
		if (Auth::user()->user_type == '1') {
            $pos = ParkingDevice::find($id);
		}else{
            $pos = ParkingDevice::where('partner_id',$partner_id)->find($id);
		}
        
        if($pos){
            $pos->delete();
            return "Data successfully deleted.";
        }
      throw new ApiGenericException("Adam not found.");
    }
      

   
    public function getFacilityEntryGates($facility_id){
        $gates = \DB::table('gates')
                     ->select('id as val','gate_name as name','gate_type as type')
                     ->where('facility_id', $facility_id)
                     ->where('deleted_at', Null)
                    ->where('is_active', 1)
                    ->get();
        return $gates;
    }



      public function getParkingDeviceType()
      {
      $device = \DB::table('parking_device_types')
                     ->select('id as val','name as name')
                     ->get();
      return $device;
    }

	public function getFacilitiesEntryGates(Request $request){
		$gates = Gate::with(['facility' => function($query) use ($request) {
			$query->whereIn('id', $request->facilities_id);
			$query->where('active', 1);				  
		}])->select('id as val','gate_name as name','gate_type as type','facility_id')
                     ->whereIn('facility_id', $request->facilities_id)
                     ->where('deleted_at', Null)
                    ->where('is_active', 1)
					->orderBy('facility_id', 'desc')
                    ->get();
		return 	$gates;		
		
    }
	
	public function getFacilitiEntryGate(Request $request){
		$gates = Gate::with(['facility' => function($query) use ($request) {
			$query->whereIn('id', $request->facilities_id);
			$query->where('active', 1);				  
		}])->select('id as val','gate_name as name','gate_type as type','facility_id')
                     ->whereIn('facility_id', $request->facilities_id)
                     ->where('deleted_at', Null)
                    ->where('is_active', 1)
					->orderBy('facility_id', 'desc')
                    ->get();
		return 	$gates;		
					
	}

    public function getFacilityGate(Request $request)
    {
        $facilty ='';
        $partner_id = '';
        if($request->partner_id)
        {
            $partner_id = $request->partner_id;
        }else{
            if(Auth::user()->user_type == '4' || Auth::User()->user_type=='12'){
                $partner_id = Auth::user()->created_by;
                $facilty = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            }elseif(Auth::user()->user_type == '3'){
                $partner_id = Auth::user()->id;
            }            
        }

        $pos = Gate::where("is_active", '1');
        if($request->facility_id != ''){
            $pos = $pos->where('facility_id',$request->facility_id);
        }
       
        if(isset($partner_id) && !empty($partner_id)){
            $pos = $pos->where('partner_id',$partner_id);
        }
        
        $pos = $pos->orderBy("id","DESC");
        $pos = $pos->get();
         return $pos;
    }
  
}
