<?php
namespace App\Http\Controllers\ParkEngage;
use Illuminate\Http\Request;
use App\Http\Requests;
use Auth;
use App\Http\Controllers\Controller;
use Hash;
use Exception;
use Artisan;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Models\PermitRequest;
use App\Models\AuthorizeNetTransaction;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\User;
use Mail;
use App\Models\OauthClient;
use App\Models\BlackListedVehicle;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Response;
use Excel;







class DiamondBlacklistController extends Controller
{
    
   
    protected  $request;
    protected  $log;
    protected $errorLog;
    protected $user;


    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/are/blacklisted')->createLogger('blacklistedbulkData');
      
    }

	public function addVehicle(Request $request)
	{
        $user = User::find(Auth::user()->id);  
        $vehicle = [];
		if($request){
			$license_plate_number = explode(",",$request->license_plate_number);
			foreach($license_plate_number as $key=>$val){
				$vehicle = BlackListedVehicle::where('license_plate_number',$val)->first();
				if($vehicle){
					$userdata = [
						'error_msg' => 'Vehicle already in blacklist or BOLO List.'
					];
					return $userdata;
					/*
					$vehicle->license_plate_number = $val;
					$vehicle->make_model = $request->make_model;
					$vehicle->plate_type = isset($request->plate_type)? $request->plate_type:'';
					$vehicle->vehicle_type = isset($request->vehicle_type)? $request->vehicle_type:'';
					$vehicle->color =  isset($request->color)? $request->color:'';  
					$vehicle->status = isset($request->status)? $request->status:'1';
					$vehicle->save();
					*/
				}else{
					$vehicle['license_plate_number'] = $val;
					$vehicle['make_model'] = $request->make_model;
					$vehicle['plate_type'] = isset($request->plate_type)? $request->plate_type:'';
					$vehicle['vehicle_type'] = isset($request->vehicle_type)? $request->vehicle_type:'';
					$vehicle['vehicle_type_id'] = isset($request->vehicle_type_id)? $request->vehicle_type_id: NULL; #pims-13318
					$vehicle['color'] =  isset($request->color)? $request->color:'';  					
					$vehicle['status'] = isset($request->status)? $request->status:'1';
					$vehicle =  BlackListedVehicle::create($vehicle);
				}	
			}
							
			return $vehicle;	
        }else{
			throw new ApiGenericException("Invalid Data Format");   
		}  
          

     }

     public function getVehicleList(Request $request)
     {
        $vehicleList = BlackListedVehicle::whereIn('status',[0,1]);
		
		if ($request->search) {
			
			$vehicleList = $vehicleList->where('license_plate_number', 'like', "%$request->search%")
									   ->orWhere('make_model', 'like', "%$request->search%")
									   ->orWhere('vehicle_type', 'like', "%$request->search%");
		}
		
		if($request->plate_type){
			$vehicleList->Where('plate_type', $request->plate_type);
		}
		
		if($request->sort != ''){        
			$vehicleList = $vehicleList->orderBy($request->sort,$request->sortBy);        
		}else{
			$vehicleList = $vehicleList->orderBy('id','DESC');        
		}
		$result = $vehicleList->paginate(10);
        
		return $result;
     }

     public function getVehicleByid($id)
     {
        $is_vehicle = BlackListedVehicle::where('id',$id)->first();
        if(!$is_vehicle)
        {
            throw new ApiGenericException("Vehicle Not Found.");   
        }
         
        return $is_vehicle;

     }

     
	 public function updateVehicle(Request $request)
     {
		$vehicle = BlackListedVehicle::where('id', $request->id)->first();
        if($vehicle){			
			$vehicle->license_plate_number = $request->license_plate_number;
			$vehicle->make_model = $request->make_model;
			$vehicle->plate_type = isset($request->plate_type)? $request->plate_type:'';
			$vehicle->vehicle_type = isset($request->vehicle_type)? $request->vehicle_type:'';
			$vehicle->vehicle_type_id = isset($request->vehicle_type_id)? $request->vehicle_type_id:NULL; #pims-13318
			$vehicle->color =  isset($request->color)? $request->color:'';  
			$vehicle->status = isset($request->status)? $request->status:'1';
			$vehicle->save();
			return $vehicle;
		}else{
			throw new ApiGenericException("Sorry! Record Not Found.");  
		}
     }
 	
     public function deleteVehicle($id)
     {
        
        $is_vehicle = BlackListedVehicle::find($id);
        if(!$is_vehicle)
        {
            throw new ApiGenericException("Vehicle Not Found.");   
        }
         
        $is_vehicle->delete();
         return "Data successfully deleted.";

     }


	public function getBlacklistVehicleImportData(Request $request)
	{		
		if($request->file('input_importfile'))
		{
			$inputfile = $request->file('input_importfile');
			$ext = $inputfile->getClientOriginalExtension();
			if($ext=='csv')
			{
				$fileName = time().'_blacklistedfile.'.$ext;
				$destination_path = storage_path("import/");
                $inputfile->move($destination_path, $fileName);
				$file = storage_path('import/').$fileName;
				$data = Excel::load($file)->get();
				$newdata = $data->toArray();

				if(!empty($newdata))
				{
					$headerCheck = Excel::selectSheetsByIndex(0)->load($file, function($reader){})->get()->toArray();
					//return $headerCheck;
					
					$chkfile=0;
					$chkfile1=0;
					foreach($headerCheck as $ex){
						if(isset($ex["license_plate_3_to_10_characters_only"]) && isset($ex["makemodel"]) && isset($ex["plate_type_blacklistedbolo"]) && isset($ex["vehicle_type"]) && isset($ex["color"])){
							if((strlen($ex["license_plate_3_to_10_characters_only"])>=3) && (strlen($ex["license_plate_3_to_10_characters_only"])<=10)){
								
							}else{
								$chkfile1=1;
							}	
					    }
					    else{
						 $chkfile=1;
					    }
					}
					//return $chkfile1;
					
					if($chkfile1==1){
						throw new ApiGenericException("License Plate Number Should be 3 to 10 Characters Only");
					}
					
					if($chkfile!=1)
					{						
						$file = storage_path('import/').$fileName;		
						$data = Excel::load($file)->get();
						$vehicle = [];
						$failedData=array();
						if($data){
							foreach ($data as $key => $value) {
								$vehicle = BlackListedVehicle::where('license_plate_number',$value['license_plate_3_to_10_characters_only'])->first();
								if($vehicle){
									$resdata = [
										'error_msg' => 'Vehicle already in blacklisted or BOLO List.'
									];
									$failedData[] = $value['license_plate_3_to_10_characters_only'];
									continue;
									return $resdata;
								}else{
									$vehicle['license_plate_number'] = $value['license_plate_3_to_10_characters_only'];
									$vehicle['make_model'] = $value['makemodel'];								
									$vtype = str_replace(' ', '', $value['plate_type_blacklistedbolo']);
									$vtype = strtolower($vtype);
									
									if($vtype=='blacklisted' || $vtype=='blacklist')
									{
										$platetype = 1;
									}
									elseif($vtype=='bolo')
									{
										$platetype = 2;
									}
									else{									
											continue;									  
									}
									$value['plate_type']=$platetype;
									$vehicle['plate_type'] = isset($value['plate_type'])? $value['plate_type']:'';
									$vehicle['vehicle_type'] = isset($value['vehicle_type'])? $value['vehicle_type']:'';
									$vehicle['color'] =  isset($value['color'])? $value['color']:'';
									$vehicle['status'] = 1;
									$vehicle =  BlackListedVehicle::create($vehicle);
								}				
							}

							if (empty($failedData)) {
								return 'Data Uploaded Successfully.';
							} else {
								$failList = implode(',', $failedData);
								$data = json_encode($failedData);							
								$failedData = json_decode($data,JSON_UNESCAPED_SLASHES);
								$this->log->error('Issue in Uploading Blacklisted Upload data because duplicate data found for following License Plates:',$failedData);
								return 'Data Uploaded Successfully, Duplicate data found for License Plate '.$failList;
							}
							//return 'Data Uploaded Successfully, ';

						}else{
							//throw new ApiGenericException("Invalid Data Format"); 
							//return response(['Data'=>'Invalid Data Format','status'=>401], 401);
							//return "Invalid Data Format";
							throw new ApiGenericException("Invalid Data Format.");  
							$fileData = [
								'error_msg' => 'Invalid Data Format'
							];
							return $fileData;
						} 
					}
					else{
						//throw new ApiGenericException("Invalid File Format");
						unlink(storage_path('import/'.$fileName));
						//return "Invalid Data Format";
						throw new ApiGenericException("Invalid Data Format.");  
						$fileData = [
							'error_msg' => 'Invalid Data Format'
						];
						return $fileData;
					}

				}else{
					unlink(storage_path('import/'.$fileName));
					throw new ApiGenericException("Empty Data in File");   
					$fileData = [
						'error_msg' => 'Empty Data in File'
					];
					return $fileData;
				}

				
				



			}
			else{
				//throw new ApiGenericException("Invalid File Type");
				unlink(storage_path('import/'.$fileName));
				//return response(['Data'=>'Invalid File Type','status'=>401], 401);
				return "Invalid File Type";
			}

		}
		else{
			//throw new ApiGenericException("No any file Selected");
			//return response(['Data'=>'No File Selected','status'=>401], 401);
			return "No Any File Selected";
		}
	}



	public function downloadSampleBlacklistedFile() 
    {
		$excelSheetName = ucwords(str_replace(' ', '', 'sampleBlacklist'));
		$Columns[] = [
			'License Plate (3 to 10 Characters Only)' => '',
			'Make/Model' => '',
			'Plate Type (Blacklisted/BOLO)' => '',
			'Vehicle Type' => '',
			'Color' => '',
		];

		Excel::create(
		    $excelSheetName, function ($excel) use ($Columns, $excelSheetName) {

          // Set the spreadsheet title, creator, and description
          $excel->setTitle($excelSheetName);
          $excel->setCreator('sampleBlacklist')->setCompany('ParkEngage');
          $excel->setDescription('Sample File of Blacklisted Data');
		  // Build the spreadsheet, passing in the payments array
          if(isset($Columns) && !empty($Columns)){
            $excel->sheet(
              'Blacklisted Data', function ($sheet) use ($Columns) {
                $sheet->fromArray($Columns, null, 'A1', false, true);
				$sheet->freezeFirstRow('A2');

              }
            );
          }else{
			  throw new ApiGenericException('Sorry! No Data Found.');
		  }
        }
	    )->store('csv')->download('csv');

	}
	
	
}
