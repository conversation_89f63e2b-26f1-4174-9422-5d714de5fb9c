<?php

namespace App\Http\Controllers\ParkEngage;

use App\Http\Controllers\Controller;
use App\Http\Helpers\QueryBuilder;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Exceptions\ApiGenericException;
use App\Models\Event;
use App\Models\EventFacility;
use App\Models\Facility;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\WhitelistUser;
use App\Models\ParkEngage\WhitelistUserEvent;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

use function GuzzleHttp\json_decode;

class WhitelistUserController extends Controller
{

    const USERTYPE = 8;
	const USERTYPE_BUSINESS = 10;
	const PASSWORD_PATTERN = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%&*()';
	const SUPERADMIN = 1;
	const PARTNER = 3;
	const SUBORDINATE = 4;
	CONST REGIONAL_MANAGER = 12;

    protected $log;
    protected $request;
    protected $whitelist_users;
    protected $whitelist_users_events;
    protected $countryCode;
    protected $carbon;

    public function __construct(LoggerFactory $logFactory, Request $request)
    {
        $this->log = $logFactory->setPath('logs/user')->createLogger('whitelist');
        $this->countryCode = QueryBuilder::appendCountryCode();
        $this->whitelist_users = new WhitelistUser();
        $this->whitelist_users_events = new WhitelistUserEvent();
        $this->carbon = new Carbon();
    }

    public function index(Request $request){
        $whiteListUsers = WhitelistUser::with('WhitelistUserCard');

        $facility_id = $request->facility_id;
        $facility_id = isset($facility_id) && !empty($facility_id) ? $facility_id : '';
        if ($facility_id != '') {
            $whiteListUsers = $whiteListUsers->where("facility_id", $facility_id);
        }

        $partner_id = isset($request->partner_id) && !empty($request->partner_id) ? $request->partner_id : '';
        if ($partner_id != '') {
            $whiteListUsers = $whiteListUsers->where("partner_id", $partner_id);
        }
        $search = isset($request->search) ? $request->search : '';
        if ($search != '') {
            $whiteListUsers = $whiteListUsers->where(function ($query) use ($search) {
                $query->where('phone', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%')
                    ->orWhere('name', 'like', '%' . $search . '%');
            });
            $this->log->info("Search Value: $search");
        }
        $sortBy = isset($request->sortBy) && !empty($request->sortBy) ? $request->sortBy : 'DESC';
        return $whiteListUsers = $whiteListUsers->orderBy("id", $sortBy)->paginate(20);
    }
    
    public function indexbkp(Request $request)
    {
        DB::statement("SET SESSION group_concat_max_len = 1000000000000");
        $query = DB::table('whitelist_users');

        $facility_id = $request->facility_id;
        $facility_id = isset($facility_id) && !empty($facility_id) ? $facility_id : '';
        // if ($facility_id != '') {
        //     $query->where('whitelist_users.facility_id', $facility_id);
        //     $this->log->info("Facility Id: $facility_id");
        // }

        $partner_id = isset($request->partner_id) && !empty($request->partner_id) ? $request->partner_id : '';
        // if ($partner_id != '') {
        //     $query->where('whitelist_users.partner_id', $partner_id);
        //     $this->log->info("Partner Id: $partner_id");
        // }

        // $event_id = isset($request->event_id) && !empty($request->event_id) ? $request->event_id : '';
        // if ($event_id != '') {
        //     $query->where('whitelist_user_events.event_id', $event_id);
        //     $this->log->info("Event Id: $event_id");
        // }

        $search = isset($request->search) ? $request->search : '';
        if ($search != '') {
            $query->where(function ($query) use ($search) {
                $query->where('whitelist_users.phone', 'like', '%' . $search . '%')
                    ->orWhere('whitelist_users.email', 'like', '%' . $search . '%')
                    ->orWhere('whitelist_users.name', 'like', '%' . $search . '%');
            });
            $this->log->info("Search Value: $search");
        }

        $sortBy = isset($request->sortBy) && !empty($request->sortBy) ? $request->sortBy : 'DESC';


        // $data = $query->select('whitelist_users.id', 'whitelist_users.name', 'whitelist_users.phone', 'whitelist_users.email', 'facilities.full_name', 'events.title','events.is_active')
        //     ->leftJoin('whitelist_user_events', 'whitelist_user_events.whitelist_users_id', '=', 'whitelist_users.id')
        //     ->leftJoin('facilities', 'whitelist_users.facility_id', '=', 'facilities.id')
        //     ->leftJoin('events', 'events.id', '=', 'whitelist_user_events.event_id')
        //     ->where('events.is_active', 1)
        //     ->whereNull('whitelist_users.deleted_at')
        //     ->groupBy('whitelist_users.id')
        //     ->orderBy('whitelist_users.id', $sortBy)
        //     ->selectRaw("GROUP_CONCAT(events.title SEPARATOR ', ') as event_names")
        //     ->paginate(20);

        //     DB::statement("SET SESSION group_concat_max_len = @@group_concat_max_len");

        $data = $query->select('whitelist_users.id', 'whitelist_users.name', 'whitelist_users.phone', 'whitelist_users.email','whitelist_user_cards.card_last_four','whitelist_user_cards.expiry','whitelist_user_cards.card_type')
            ->leftJoin('whitelist_user_cards', 'whitelist_user_cards.whitelist_user_id', '=', 'whitelist_users.id')
            ->whereNull('whitelist_users.deleted_at')
            ->orderBy('whitelist_users.id', $sortBy)
            ->paginate(20);

        return $data;
    }


    public function store(Request $request)
    {
        $this->log->info("===== WhiteList Start ===== " . json_encode($request->all()));

        $this->validate($request, [
            //'email' => 'required|email',
            'phone' => 'required',
            //'facility_id' => 'required',
            'partner_id' => 'required',
            //'event_id' => 'required'
        ]);

        $this->log->info("===== Validation Passed Start ===== ");

        if(isset($request->facility_id) && $request->facility_id != ''){
            $facility_id = $request->facility_id;
            $facility_configuration = FacilityConfiguration::where('facility_id', $facility_id)->first();
            $max_whitelist_user = $facility_configuration->whitelist_users;
            if($max_whitelist_user == 0){
                $max_whitelist_user = 15;
            }
        }
        

        //$this->log->info("===== Max whitelist user: $max_whitelist_user =====");

        $successEvents = $errorEvents = [];
        $count = 0;
        if(isset($request->event_id) && $request->event_id != ''){
            foreach ($request->event_id as $key => $id) {
                $count = $this->whitelist_users_events->where(['facility_id' => $facility_id, 'event_id' => $id])->count();
                $this->log->info("===== Got from DB: $count, Max whitelist user: $max_whitelist_user =====");
                if ($count < $max_whitelist_user) {
                    $successEvents[] = $id;
                    $this->log->info("===== Allowed to add in Events ===== ". $id);
                } else {
                    $errorEvents[] = $id;
                    $this->log->info("===== Not Allowed to add in Events ===== " . $id);
                }
            }
        }
        

        $this->log->info("===== Events Filtr Passed Start ===== ");

        //$event_ids = implode(",", $successEvents);

        // $facility_id = (isset($facility_id) && !empty($facility_id)) ? $facility_id : '';
        // if ($facility_id == '')
        //     throw new ApiGenericException('facility_id is mandatory!.');

        $this->log->info("===== Reached 11 ===== ");
        
        $partner_id =  $request->partner_id;
        $partner_id = (isset($partner_id) && !empty($partner_id)) ? $partner_id : '';
        if ($partner_id == '')
            throw new ApiGenericException('partner_id is mandatory!.');

        $this->log->info("===== Reached 22 ===== ");
        
        $phone =  $this->countryCode . substr($request->phone, -10);
        $this->log->info("===== Reached 33 $phone ===== ");


        $checkPhone = $this->whitelist_users->where(['partner_id' => $partner_id, 'phone' => $phone])->first();
        if ($checkPhone) {
            $this->log->info("Phone number already taken");
            throw new ApiGenericException('Phone number already taken.');
        }

        $this->log->info("===== Reached 44 ===== ");

        //$this->whitelist_users->facility_id = $facility_id;
        $this->whitelist_users->partner_id = $partner_id;
        $this->whitelist_users->phone = $phone;
        if($request->email != ''){
            $this->whitelist_users->email = $request->email;    
        }
        //$this->whitelist_users->event_id =  $event_ids;
        $this->whitelist_users->name = $request->name;
        $this->whitelist_users->save();

        $this->log->info("===== Reached 55 ===== ");

        $entry_not_allowed_events = $entry_allowed_events = [];
        if (count($successEvents) > 0){
            $this->log->info("===== Allowed Emp to Event/Game ===== ");
            $this->whitelist_users->save();
            $last_id = $this->whitelist_users->id;

            $events = array_map(function ($successEvents) use ($last_id, $facility_id, $partner_id) {
                return ['event_id' => $successEvents, 'whitelist_users_id' => $last_id, 'facility_id' => $facility_id,'partner_id' => $partner_id];
            }, $successEvents);

            $this->whitelist_users_events->insert($events);
            $entry_allowed_events = Event::whereIn('id', $successEvents)->get()->pluck('title');
        }

        $this->log->info("===== Reached 66 ===== ");

        if (count($errorEvents) > 0) {
            $this->log->info("===== Reached 77 ===== ". json_encode($errorEvents));
            $entry_not_allowed_events = Event::whereIn('id', $errorEvents)->get()->pluck('title');
            $this->log->info("===== Not Allowed Emp to Event/Game ===== " . $entry_not_allowed_events);
            // throw new ApiGenericException('Events Not Created: ' . $event_names);
        }
        $this->log->info("===== WhiteList End ===== ");
        return $this->whitelist_users;
        //return response()->json(["entry_not_allowed_events" => $entry_not_allowed_events, 'entry_allowed_events' => $entry_allowed_events]);
    }

    public function show(WhitelistUser $whitelist)
    {
        $query = DB::table('whitelist_users');

        $facility_id = $whitelist->facility_id;
        $facility_id = isset($facility_id) && !empty($facility_id) ? $facility_id : '';
        if ($facility_id != '') {
            $query->where('whitelist_users.facility_id', $facility_id);
            $this->log->info("Facility Id: $facility_id");
        }

        $partner_id = isset($whitelist->partner_id) && !empty($whitelist->partner_id) ? $whitelist->partner_id : '';
        if ($partner_id != '') {
            $query->where('whitelist_users.partner_id', $partner_id);
            $this->log->info("Partner Id: $partner_id");
        }

        $data = $query->select('whitelist_users.id', 'whitelist_users.name', 'whitelist_users.phone', 'whitelist_users.email', 'whitelist_users.facility_id', 'facilities.full_name', 'events.title','events.is_active')
            ->leftJoin('facilities', 'whitelist_users.facility_id', '=', 'facilities.id')
            ->leftJoin('whitelist_user_events', 'whitelist_user_events.whitelist_users_id', '=', 'whitelist_users.id')
            ->leftJoin('events', 'events.id', '=', 'whitelist_user_events.event_id')
            ->where('events.is_active', 1)
            ->whereNull('whitelist_users.deleted_at')
            ->orderBy('whitelist_users.id', 'DESC')
            ->selectRaw("GROUP_CONCAT(events.title SEPARATOR ', ') as event_names")
            ->selectRaw("GROUP_CONCAT(events.id SEPARATOR ', ') as event_id")
            ->where('whitelist_users.id', $whitelist->id)
            ->first();

        $event_ids = explode(',', $data->event_id);
        $data->event_id = array_map('intval', $event_ids);

        return response()->json($data);
    }

    public function update(Request $request, WhitelistUser $whitelist)
    {
        $this->log->info("===== WhiteList Start Update===== " . json_encode($request->all()));
        $this->log->info("===== WhiteList ===== " . json_encode($whitelist));

        $this->validate($request, [
            'email' => 'required|email',
            'phone' => 'required',
            'facility_id' => 'required',
            'partner_id' => 'required',
            'event_id' => 'required'
        ]);

        $this->log->info("===== Validation Passed Start ===== ");

        $facility_id = $request->facility_id;
        $facility_configuration = FacilityConfiguration::where('facility_id', $facility_id)->first();
        $max_whitelist_user = $facility_configuration->whitelist_users;
        if($max_whitelist_user == 0){
            $max_whitelist_user = 15;
        }

        $this->log->info("===== Max whitelist user: $max_whitelist_user =====");


        $facility_id = (isset($facility_id) && !empty($facility_id)) ? $facility_id : '';
        if ($facility_id == '')
            throw new ApiGenericException('facility_id is mandatory!.');

        $partner_id =  $request->partner_id;
        $partner_id = (isset($partner_id) && !empty($partner_id)) ? $partner_id : '';
        if ($partner_id == '')
            throw new ApiGenericException('partner_id is mandatory!.');

        $phone =  $this->countryCode . substr($request->phone, -10);
        $this->log->info("===== Partner id: $partner_id, Faciliyt Id: $facility_id, Request Phone: $phone, DB phone: $whitelist->phone ===== ");

        if($phone != $whitelist->phone){
            $checkPhone = WhitelistUser::where(['partner_id' => $partner_id, 'facility_id' => $facility_id, 'phone' => $phone])->first();
            if($checkPhone){
                $this->log->info("===== Already Exists: $checkPhone->id  & Update id: $whitelist->id ===== ");
                if ($checkPhone->id != $whitelist->id) {
                    throw new ApiGenericException('Phone Number already taken.');
                }
            }
        }

        $successEvents = $errorEvents = [];
        $count = 0;
        foreach ($request->event_id as $key => $id) {
            $count = $this->whitelist_users_events->where(['facility_id' => $facility_id, 'event_id' => $id])->count();
            $this->log->info("===== Got from DB: $count, Max whitelist user: $max_whitelist_user =====");
            if ($count < $max_whitelist_user) {
                $successEvents[] = $id;
                $this->log->info("===== Allowed to add in Events ===== ". $id);
            } else {
                $errorEvents[] = $id;
                $this->log->info("===== Not Allowed to add in Events ===== " . $id);
            }
        }

        $this->log->info("===== Events Filtr Passed Start ===== ");

        $event_ids = implode(",", $successEvents);

        $whitelist->facility_id = $facility_id;
        $whitelist->partner_id = $partner_id;
        $whitelist->phone = $phone;
        $whitelist->email = $request->email;
        $whitelist->event_id = $event_ids;
        $whitelist->name = $request->name;

        $entry_not_allowed_events = $entry_allowed_events = [];
        if (count($successEvents) > 0) {
            $this->log->info("===== Allowed Emp to Event/Game ===== ");
            $whitelist->save();

            $last_id = $whitelist->id;

            $events = array_map(function ($successEvents) use ($last_id, $facility_id, $partner_id) {
                return ['event_id' => $successEvents, 'whitelist_users_id' => $last_id, 'facility_id' => $facility_id,'partner_id' => $partner_id];
            }, $successEvents);

            $this->whitelist_users_events->where('whitelist_users_id', $last_id)->forceDelete();
            $this->whitelist_users_events->insert($events);
            $entry_allowed_events = Event::whereIn('id', $successEvents)->where('is_active', 1)->get()->pluck('title');
        }

        $this->log->info("===== Reached 66 ===== ");

        if (count($errorEvents) > 0) {
            $this->log->info("===== Reached 77 ===== ". json_encode($errorEvents));
            $entry_not_allowed_events = Event::whereIn('id', $errorEvents)->where('is_active', 1)->get()->pluck('title');
            $this->log->info("===== Not Allowed Emp to Event/Game ===== " . $entry_not_allowed_events);
            // throw new ApiGenericException('Events Not Created: ' . $event_names);
        }

        $this->log->info("===== WhiteList End Update ===== ");

        return response()->json(["entry_not_allowed_events" => $entry_not_allowed_events, 'entry_allowed_events' => $entry_allowed_events]);
    }

    public function destroy(WhitelistUser $whitelist)
    {
        $this->whitelist_users_events->where('whitelist_users_id', $whitelist->id)->delete();
        $whitelist->delete();
        return $whitelist;
    }

    public function getEventDetails(Request $request)
    {
        $this->log->info("=========== Get Event Start ============");
        $facility_id = $partner_id = 0;

        if(Auth::user()->user_type==self::PARTNER){
            $partner_id = Auth::user()->id;
        }else{
            $partner_id = Auth::user()->created_by;
        }
        $this->log->info("======== Partner Id: $partner_id  ========");

        $facility_id = $request->facility_id;
        $facility_id = isset($facility_id) && !empty($facility_id) ? $facility_id : '';
        if ($facility_id != '') {
            $this->log->info("======== Facility Id: $facility_id  ========");
            $facility_id = [$facility_id];
        }else if($partner_id){
            $facility_id = Facility::where('owner_id', $partner_id)->get()->pluck('id');
            $this->log->info("======== Facility Ids:==== " .$facility_id);
        }
        
        if(count($facility_id) > 0){
            $this->log->info("======== Facility Found =========");
            $event_ids = EventFacility::whereIn('facilitY_id', $facility_id)->get()->pluck('event_id');
            $this->log->info("======== event_ids =========".$event_ids);
            $today = $this->carbon->now()->format('Y-m-d H:i:s');
            $events = Event::whereIn('id', $event_ids)->where('is_active', 1)->where('parking_end_time', '>=', $today)->get();
            $this->log->info("=========== Get Event End ============");
            return $events;

        }
        throw new ApiGenericException('No Facility found.');

    }
}
