<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use Response;
use Hash;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Models\CustomText;
use App\Models\UserPass;
use Excel;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\ParkEngage\ParkingDeviceTypes;
use App\Models\OauthClient;

use App\Models\Event;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Models\ParkEngage\UserSession;
use App\Models\HoursOfOperation;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
use App\Models\PermitTicket;
use App\Models\ParkEngage\LicensePlate;
use App\Models\ParkEngage\TransactionData;

class ClassicCheckinCheckoutController extends Controller
{
   
  protected $log; 
   protected $user;
   protected $partnerPaymentDetails;
   protected $authNet;
   protected $facility;

   const RESERVATION_THRESHOLD_TYPE = 2;
   const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE= 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO= 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR= 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    const  SHARE_TICKET_AMOUNT = 0;
    const  SHARE_PASS_AMOUNT = 0;

    const EVENT_THRESHOLD_TYPE = 1;
    const CLIENT_LIVE_CARD_SESSION_ID = "6049741011541240372";
 
    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/classic')->createLogger('classic');
    }

   
    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4])?$cardData[4]:'';
        $request->request->add(
            [
            'name_on_card' => $cardData[0],
            'card_number' => $cardData[1],
            'expiration_date' => $cardData[2],
            'security_code' => $cardData[3],
            'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }

    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


   protected function checkTicketNumber(){
      $ticket = 'PE'.rand(100,999).rand(100,999);
      $isExist = Ticket::where('ticket_number', $ticket)->first();
      if($isExist){
        return $ticket = $this->checkTicketNumber();
      }
      return $ticket;
   }

  

   public function updateRateInformationWithAvailibilty($request, Facility $facility)
   {
       $returnResultArr = array();
       
       $returnResultArr['coupon_threshold_price'] = 0;        
       $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
       $returnResultArr['availability'] = 0;
       $returnResultArr['is_coupon_threshold_applied'] = 0;
       
       $inventory = new Inventory();
       $date_time_out = Carbon::parse($request->arrival_time)->addMinutes((number_format($request->length_of_stay, 2) * 60));
       
       $realtimeWindow = $facility->realtime_window;
       $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;
       
       $timeDifference = date_diff(date_create($request->arrival_time), Carbon::now());
       
       $isAvailable = true;
   
       $thresholdAvailability = self::DEFAULT_VALUE;
       
       if ($isAvailable == true) {
           //check how many slots does entry and exit time occupies
             //$difference = date_diff(date_create($request->arrival_time), date_create($date_time_out));
           $difference = date_diff(date_create(date('Y-m-d',strtotime($request->arrival_time))), date_create(date('Y-m-d',strtotime(($date_time_out)))));
           
           if ($difference->d > 0) {
//                $dates   = $inventory->generateArrayOfDates(
//                '', date($request->arrival_time), date($date_time_out));
               
                $dates   = $inventory->generateArrayOfDates(
                ($difference->d + self::ADD_EXTRA_DAY_COUNT), date('Y-m-d H:i:s', strtotime($request->arrival_time)));
                 
               $dayDifference = $difference->d;
               
               foreach ($dates as $key => $date) {                    
                   $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();
                   
                   if ($facilityAvailability) {
                   $inventory = json_decode($facilityAvailability->availability);

                   if ($key == 0) {
                   /**
                   * because this is the first day in the dates provided
                   * we should check from each time_slot starting
                   * from the hour provided in the api call
                   */
                   $i = date('G', strtotime($request->arrival_time));
                   while ($i <= self::TWENTY_FOUR_HOURS) {
                    if(isset($inventory->{$i}))
                    {
                     if ($inventory->{$i} < 1) {
                       $isAvailable = false;
                       break;
                     }
                       if($thresholdAvailability>0)
                       {
                           if($thresholdAvailability > $inventory->{$i})
                           {
                               $thresholdAvailability = $inventory->{$i};
                           }

                       }else{
                           $thresholdAvailability = $inventory->{$i};
                       }
                    }
                     $i++;
                   }
                 } elseif ($key == $dayDifference) {
                   $i = date('G', strtotime($date_time_out));
                   $minutes = date('i', strtotime($date_time_out));
                   if ($minutes >= 30) {
                       $i++;
                   }
                   /**
                    * because this is the last day in the dates provided
                    * we should check from each time_slot starting
                    * till the hour provided in the api call
                    */
                   $j = 0;
                   while ($j < $i) {
                    if(isset($inventory->{$j}))
                    {
                     if ($inventory->{$j} < 1) {
                       $isAvailable = false;
                       break;
                     }
                     if($thresholdAvailability>0)
                       {
                           if($thresholdAvailability > $inventory->{$j})
                           {
                               $thresholdAvailability = $inventory->{$j};
                           }

                       }else{
                           $thresholdAvailability = $inventory->{$j};
                       }
                    }
                    $j++;
                   }
                 } else {
                 /**
                  * because this could be any day except first and last in
                  * the dates provided we should check from whole day
                  */
                 $k = 0;
                 while ($k <= self::TWENTY_FOUR_HOURS) {
                  if(isset($inventory->{$k}))
                  {
                   if ($inventory->{$k} < 1) {
                     $isAvailable = false;
                     break;
                   }
                    if($thresholdAvailability>0)
                       {
                           if($thresholdAvailability > $inventory->{$k})
                           {
                               $thresholdAvailability =$inventory->{$k};
                           }

                       }else{
                           $thresholdAvailability = $inventory->{$k};
                       }
                  }
                   $k++;
                 }
               }
              }
             }
            
           }else{
                $startingHour = date('G', strtotime($request->arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                   ['facility_id' => $facility->id, 'date' => date('Y-m-d',strtotime($request->arrival_time))])->first();
              
                if ($facilityAvailability) {
                 $availability = json_decode($facilityAvailability->availability, true);
                
                 while ($startingHour <= $endingHour) {
                  if(isset($availability[$startingHour]))
                  {
                   if (($availability[$startingHour] < 1)) {
                     $isAvailable = false;
                   }
                   if($thresholdAvailability>0)
                   {
                       if($thresholdAvailability > $availability[$startingHour])
                       {
                           $thresholdAvailability = $availability[$startingHour];
                       }
                       
                   }else{
                       $thresholdAvailability = $availability[$startingHour];
                   }
                  }
                   $startingHour++;
                 }
               }
               
           } 
           
           if($thresholdAvailability < self::DEFAULT_VALUE)
           {
               $thresholdAvailability = self::DEFAULT_VALUE;  
           }
           
           if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {  
               
             $dateIn = date('Y-m-d', strtotime($request->arrival_time));
             $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();
             
             if ($facilityAvailability) {
               //$availabilities = json_decode($facilityAvailability->availability, true);
          
               if ($thresholdAvailability >= 0) {
                   
                  $couponThresholdsNew = $facility->facilityCouponThreshold;
                  $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');
                  
                   $thresholds       = array();
                   if ($couponThresholdsNew) {
                       $thresholdCounter = self::DEFAULT_VALUE;
                     foreach ($couponThresholdsNew as $couponThreshold) {
                         
                       if($couponThreshold->uptick_type !=='deleted')
                       {
                          $thresholds[$thresholdCounter] =
                           ['threshold'=>$couponThreshold->threshold,'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                           $thresholdCounter++;
                       }
                      }
                   }
                   $thresholdPrice = 0;
                   $currentAvailability = $thresholdAvailability;
                   foreach($thresholds as $key => $threshold) {
                   if ($thresholdAvailability <= $threshold['threshold']) {
                     if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                       if ($threshold['uptick_type'] == 'price') {
                          $thresholdPrice = $threshold['uptick'];
                                                 
                          $returnResultArr['is_coupon_threshold_applied'] = 1;                           
                          break;
                       } else if ($threshold['uptick_type'] == 'percentage') {
                         $thresholdPrice =  $threshold['uptick'];
                         $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG; 
                        
                         $returnResultArr['is_coupon_threshold_applied'] = 1;
                         break;
                       }
                     }
                   }
                 }
                 $returnResultArr['coupon_threshold_price'] = $thresholdPrice;        
                      
                 $returnResultArr['availability'] = $currentAvailability;
               }
             }
           }
           
       }
       
       //check realtime availability
       /*if ($timeDifference->h <= $realtimeWindow) {

           if($facility->realtime_minimum_availability > $returnResultArr['availability']){
             $returnResultArr['coupon_threshold_price'] = 0;        
             $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
             $returnResultArr['availability'] = 0;
             $returnResultArr['is_coupon_threshold_applied'] = 0;
           }
       }*/
       return $returnResultArr;
      
        
   }

   
    




    public function getAllFacilityGates(Request $request){
      $gate = Gate::select('gate', 'gate_name', 'gate_type')->where('facility_id' , $request->facility_id)->orderBy('gate_name', "ASC")->get();
      return $gate;
    }


    


  // device config code here

  public  function getDeviceConfig(Request $request){
    
    $parkingData =[];
    $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
    if(!$secret){
       throw new ApiGenericException('Invalid partner.');
    }
    if($request->header('X-ClientSecret') != ''  && $request->device_serial_number!= '')
    { 
     
     
     // $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
     $facility = Facility::where('owner_id', $secret->partner_id)->first();
     $parkingdevice = ParkingDevice::with('gate')->where('serial_number', $request->device_serial_number)->where('facility_id', $facility->id)->first();
     if(isset($parkingdevice->is_active)== '1'){

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
           $diff_in_hours = 24;
        }
        
        $isMember=0;
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
          if($rate['price'] > 0){
            $rate['price'] = number_format($rate['facility']['base_rate'] + $rate['facility']['processing_fee'],2);
          }
          $rate['facility']['polling_interval'] = (int) $rate['facility']['polling_interval'];

          /*if($request->device_serial_number == "1640009093"){
            $rate['facility']['check_vehicle_enabled'] = '0';
          }*/

          $today = date("Y-m-d");
          $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
          if(!$todayEvent){
            $rate['is_event_started'] =  '0';
            //return $data;      
          }
          if($todayEvent){
            $parkingNowTime = date("Y-m-d H:i:s");
            $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
            $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
            $eventStartTime = $todayEvent->start_time;
            $eventEndTime = $todayEvent->end_time;
            if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
            $rate['event_rate'] =  number_format($todayEvent->event_rate + $facility->processing_fee, 2);
            $rate['is_event_started'] =  '1';
            //return $data;
            }else{
              $rate['is_event_started'] =  '0';
              //return $data;      
            }  
          }
          $data['rate'] = $rate;
          $data['parkindata'] = $parkingdevice;
          return $data;

      } else {
        throw new ApiGenericException('This Device is not active, Please contact with admin');
  
        }
    }  
     
       

     // $parkingdevice = ParkingDevice::with(['user','citation'])->where('license_plate', $license_plate)
   //   ->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();


  }


 



    public function getSessionTicketPaymentDetails(Request $request){

        $user = User::where("session_id", $request->session_id)->first();
        if(!$user){
          throw new ApiGenericException('Invalid user.');    
        }
        $checkinData = Ticket::where("user_id", $user->id)->orderBy("id", "DESC")->first();
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
           $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
        }
        $facility = Facility::find($checkinData->facility_id);
        $isMember = 0;
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE,$isMember);
        if($rate == false){
            throw new ApiGenericException('Garage is not available.');    
        }
        $data = [];
        $data ['session_id'] = $request->session_id;
        $data ['rate'] = $rate['price'];
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $data['diff_in_days'] = $startDate->diffInDays($endDate);
        $data['diff_in_hours'] = $startDate->copy()->addDays($data['diff_in_days'])->diffInRealHours($endDate);
        $data['diff_in_minutes'] = $startDate->copy()->addDays($data['diff_in_days'])->addHours($data['diff_in_hours'])->diffInRealMinutes($endDate);
        
        return $data;
    }

    public function generateBarcodeJpgNew($encrypt)
    {
        $html = $this->generateBarcodeHtml($encrypt);

        $image = app()->make(Image::class);
        $image->setOption('width', '700');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeHtml($encrypt)
    {
        return '<img src="data:image/png;base64,{{\DNS2D::getBarcodePNG($encrypt, "QRCODE", "200","200")}}"  align="center" border="0" alt="barcode" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: 0;"/>';
    }


    public function customeReplySms($msg, $phone, $imageURL = ''){

      try{
        if($phone == ''){
          return "success";
        }
        $this->log->info("sms about to send");
        $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
        $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
        $client = new Client($accountSid, $authToken);
        try
            {

              /*$imageBarcode = $this->generateBarcodeJpgNew("111");

              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
            Storage::put($imageBarcodeFileName, $imageBarcode);
              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
              Storage::put($imageBarcodeFileName, $imageBarcode);
              dd($imageBarcodeFileName, $imageBarcode);
              $data['bar_image_path'] = $imageBarcodeFileName;*/

                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                // the number you'd like to send the message to
                    $phone,
               array(
                     // A Twilio phone number you purchased at twilio.com/console
                     'from' => config('parkengage.TWILIO_PHONE'),
                     // the body of the text message you'd like to send
                     //'body' => "Fine"
                     'body' => "$msg",
                     //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
                     
                 )
             );
            $this->log->info("Message : {$msg} sent to $phone");
            return "success";
        }catch (RestException $e)
        {
            //echo "Error: " . $e->getMessage();
            $this->log->error($e->getMessage());
            return "success";
        }
      }catch (RestException $e)
      {
          //echo "Error: " . $e->getMessage();
          $this->log->error($e->getMessage());
          return "success";
      }
    }


    public function qrImage($image)
    {
        $file = Storage::disk('local')->get($image) ?: null;
        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path("app/" . $image));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }



    public function isParkEngageGateAvailable($facility_id, $gate, $phone = ''){

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if($gateDetails){
            if($gateDetails->host !=''){
                $params = ['gate_id'=>$gate->gate];
                $this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params ".json_encode($params));
                $response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response". json_encode($response));
                if($response['success'] == false){
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg; 
                }
                if(isset($response['data'][0]) && $response['data'][0] == "true"){
                    return true;
                    /*$cmd_params = ['gate_id'=>$gate->gate];
                    $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params". json_encode($cmd_params));
                    $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                    $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response". json_encode($command_response));
                    if($command_response['success'] == true){
                        if($command_response['data'][0] == "true"){
                            return true;
                        }else{
                            $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                            return $msg;                            
                        }                        
                    }else{
                        $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                        return $msg; 
                    }*/
                }else{

                    $msg = "Your vehicle must be in the garage in front of the ".ucwords($gate->gate_type)." gate.";
                    return $msg; 
                }

            }
        }
    }

    public function isParkEngageGateOpen($facility_id, $gate, $phone = ''){

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if($gateDetails){
            if($gateDetails->host !=''){
                $params = ['gate_id'=>$gate->gate];
                $this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params ".json_encode($params));
                /*$response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response". json_encode($response));
                if($response['success'] == false){
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg; 
                }*/
                //if(isset($response['data'][0]) && $response['data'][0] == "true"){
                    $cmd_params = ['gate_id'=>$gate->gate];
                    $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params". json_encode($cmd_params));
                    $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                    $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response". json_encode($command_response));
                    if($command_response['success'] == true){
                        if($command_response['data'][0] == "true"){
                            return true;
                        }else{
                            $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                            return $msg;                            
                        }                        
                    }else{
                        $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                        return $msg; 
                    }
                /*}else{

                    $msg = "Your vehicle must be in the garage in front of the ".ucwords($gate->gate_type)." gate.";
                    return $msg; 
                }*/

            }
        }
    }

    public function setCustomTimezone($facility_id){
      $facility = Facility::find($facility_id);
      $secret = OauthClient::where('partner_id', $facility->owner_id)->first();        
      $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
      if($partnerTimezone){
        if($facility->timezone !=''){
          config(['app.timezone' => $facility->timezone]);
          date_default_timezone_set($facility->timezone);
        }else if($partnerTimezone->timezone != ''){
          config(['app.timezone' => $partnerTimezone->timezone]);
          date_default_timezone_set($partnerTimezone->timezone);
        }          
      }
    }

    public function ticketSessionCheckinCheckout(Request $request){
      
      $this->setCustomTimezone($request->facility_id);
      $this->log->info("Request received --".json_encode($request->all()));
        
      $facility = Facility::find($request->facility_id);
      if(!$facility){
        throw new ApiGenericException('Sorry,Invalid garage.');    
      }
      $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 

      if(!$gate){
        throw new ApiGenericException("The system is not currently available. Please try again later.");
      }

      if(isset($gate) && $gate->active == '0'){
        throw new ApiGenericException('The system is not currently available. Please try again later.');    
      }

      if($request->session_id == ''){
          throw new ApiGenericException('Card info not found. Please try again.');    
        }
        

         //check gate api
        if($facility->check_vehicle_enabled == '1'){  
          $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }
        }
        
        $userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
        if($userSessionExist){
          $user = User::where('id', $userSessionExist->user_id)->first();  
        }else{
            $user = User::create(
                [
                'name' => '',
                'email' => '',
                'phone' => '',
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5',
                'created_by' => $facility->owner_id,
                'session_id' => $request->session_id,
                ]
            );

            $userSession = new UserSession();
            $userSession->user_id = $user->id;
            $userSession->partner_id = $user->created_by;
            $userSession->session_id = $request->session_id;
            $userSession->save();

        }

        if(isset($gate) && $gate->gate_type == "entry"){

          $arrival_time = date("Y-m-d H:i:s");
          
          $length_of_stay = .01;
          
          $existHoursOfOperation=HoursOfOperation::where('facility_id',$facility->id)->get();
          if(count($existHoursOfOperation) > 0){
            $weekday = date('N', strtotime($arrival_time));
            $time = date('H:i:s', strtotime($arrival_time));
            if($weekday>6)
            {
                $weekday=7-$weekday;
            }
            $hoursOfOperation=HoursOfOperation::where('day_of_week',$weekday)->where('facility_id',$facility->id)->get();
    
            if(count($hoursOfOperation)<=0)
            {
              throw new ApiGenericException('Garage is currently closed.');
            }else{
                foreach($hoursOfOperation as $key=>$value){
                    if($value->open_time <= $time && $value->close_time >= $time){

                    }else{
                      throw new ApiGenericException('Garage is currently closed.');
                    }
                }


            }
          }
         
          $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          if($ticket){
              throw new ApiGenericException('You have already checked-in.');    
          }  
        $reservation = Reservation::where('user_id', $user->id)->orderBy("id", "DESC")->first();  
        if(!$reservation){
          if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
            throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
          }
          if($facility->facility_booking_type == '0'){
            throw new ApiGenericException('Sorry, Drive-Up booking is not allowed.');
          }
              if($facility->is_prepaid_first == '2'){
                $today = date("Y-m-d");
                $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                if(!$todayEvent){                
                      $data['user_id'] = $user->id;
                      $data['checkin_gate'] = $request->gate_id;
                      $data['facility_id'] = $request->facility_id;
                      $data['is_checkin'] = 1;
                      $data['ticket_number'] = $this->checkTicketNumber();
                      $data['checkin_time'] = date('Y-m-d H:i:s');
                      $data['ticket_security_code'] = rand(1000, 9999);
                      $data['partner_id'] = $facility->owner_id;
                      $data['check_in_datetime'] = date('Y-m-d H:i:s');
                      $data['vp_device_checkin'] = '1';

                      $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                      $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                      if(isset($request->CardType)){
                        $card_type = '';
                        if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                          $card_type = 'VISA';
                        }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                          $card_type = 'MASTERCARD';
                        }else if(strtolower($request->CardType) == "jcb"){
                          $card_type = 'JCB';
                        }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                          $card_type = 'AMEX';
                        }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                          $card_type = 'DISCOVER';
                        }else{
                          $card_type = $request->CardType;
                        }
                        $data['card_type'] = $card_type;
                      }
                      
                      $result = Ticket::create($data);
                      
                      $facilityName = ucwords($facility->full_name);

                      $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                      //check gate api
                      if($facility->open_gate_enabled == '1'){
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                        }
                      }
                      //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
                      $msg =  "WELCOME. #$result->ticket_number";
                      if($user->phone != ''){
                        //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        $url = env('RECEIPT_URL');
                        $sms_msg = "Welcome to ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$result->ticket_number;
                        $this->customeReplySms($sms_msg, $user->phone);
                      }
                      
                      $data = ['msg' => $msg];
                      $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                      $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                      $this->log->info("Checkin done, response sent.");
                      return $data;
                  }
                  $parkingNowTime = date("Y-m-d H:i:s");
                  $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                  $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                  $eventStartTime = $todayEvent->start_time;
                  $eventEndTime = $todayEvent->end_time;

                  //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                  if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                    //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                    $driveupRate = 0;
                    if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                      $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                    }else{
                      $driveupRate = $facility->base_rate + $facility->processing_fee;
                    }
                    $data['price'] =  $driveupRate;
                    
                    return $data;
                  }else{
                    
                    $data['user_id'] = $user->id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['partner_id'] = $facility->owner_id;
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    $data['vp_device_checkin'] = '1';

                    $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                    $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                    if(isset($request->CardType)){
                      $card_type = '';
                      if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                        $card_type = 'VISA';
                      }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                        $card_type = 'MASTERCARD';
                      }else if(strtolower($request->CardType) == "jcb"){
                        $card_type = 'JCB';
                      }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                        $card_type = 'AMEX';
                      }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                        $card_type = 'DISCOVER';
                      }else{
                        $card_type = $request->CardType;
                      }
                      $data['card_type'] = $card_type;
                    }

                    $result = Ticket::create($data);
                    
                    $facilityName = ucwords($facility->full_name);

                    $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                    //check gate api
                    if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                      }
                    }
                    //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
                    $msg =  "WELCOME. #$result->ticket_number";
            
                    $data = ['msg' => $msg];
                    $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                    $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                    $this->log->info("Checkin done, response sent.");
                    return $data;

                  }
                  
              }if($facility->is_prepaid_first == '0'){
                $today = date("Y-m-d");
                $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                if(!$todayEvent){                
                      $data['user_id'] = $user->id;
                      $data['checkin_gate'] = $request->gate_id;
                      $data['facility_id'] = $request->facility_id;
                      $data['is_checkin'] = 1;
                      $data['ticket_number'] = $this->checkTicketNumber();
                      $data['checkin_time'] = date('Y-m-d H:i:s');
                      $data['ticket_security_code'] = rand(1000, 9999);
                      $data['partner_id'] = $facility->owner_id;
                      $data['check_in_datetime'] = date('Y-m-d H:i:s');
                      $data['vp_device_checkin'] = '1';

                      $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                      $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                      if(isset($request->CardType)){
                        $card_type = '';
                        if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                          $card_type = 'VISA';
                        }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                          $card_type = 'MASTERCARD';
                        }else if(strtolower($request->CardType) == "jcb"){
                          $card_type = 'JCB';
                        }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                          $card_type = 'AMEX';
                        }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                          $card_type = 'DISCOVER';
                        }else{
                          $card_type = $request->CardType;
                        }
                        $data['card_type'] = $card_type;
                      }

                      $result = Ticket::create($data);
                      
                      $facilityName = ucwords($facility->full_name);
  
                      $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                      //check gate api
                      if($facility->open_gate_enabled == '1'){
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                        }
                      }
                      //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
                      $msg =  "WELCOME. #$result->ticket_number";
            
                      $data = ['msg' => $msg];
                      $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                      $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                      $this->log->info("Checkin done, response sent.");
                      return $data;
                  }
                      $data['user_id'] = $user->id;
                      $data['checkin_gate'] = $request->gate_id;
                      $data['facility_id'] = $request->facility_id;
                      $data['is_checkin'] = 1;
                      $data['ticket_number'] = $this->checkTicketNumber();
                      $data['checkin_time'] = date('Y-m-d H:i:s');
                      $data['ticket_security_code'] = rand(1000, 9999);
                      $data['partner_id'] = $facility->owner_id;
                      $data['check_in_datetime'] = date('Y-m-d H:i:s');
                      $data['event_id'] = $todayEvent->id;
                      $data['vp_device_checkin'] = '1';

                      $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                      $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                      if(isset($request->CardType)){
                        $card_type = '';
                        if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                          $card_type = 'VISA';
                        }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                          $card_type = 'MASTERCARD';
                        }else if(strtolower($request->CardType) == "jcb"){
                          $card_type = 'JCB';
                        }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                          $card_type = 'AMEX';
                        }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                          $card_type = 'DISCOVER';
                        }else{
                          $card_type = $request->CardType;
                        }
                        $data['card_type'] = $card_type;
                      }

                      $result = Ticket::create($data);
                      
                      $facilityName = ucwords($facility->full_name);
  
                      $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                      //check gate api
                      if($facility->open_gate_enabled == '1'){
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                        }
                      }
                      //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                      $msg =  "WELCOME. #$result->ticket_number";
            
                      $data = ['msg' => $msg];
                      $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                      $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                      $this->log->info("Checkin done, response sent.");
                      return $data;
                  
            }if($facility->is_prepaid_first == '1'){
              $today = date("Y-m-d");
              $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
              if(!$todayEvent){                
                    $data['user_id'] = $user->id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['partner_id'] = $facility->owner_id;
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    $data['vp_device_checkin'] = '1';

                    $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                    $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                    if(isset($request->CardType)){
                      $card_type = '';
                      if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                        $card_type = 'VISA';
                      }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                        $card_type = 'MASTERCARD';
                      }else if(strtolower($request->CardType) == "jcb"){
                        $card_type = 'JCB';
                      }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                        $card_type = 'AMEX';
                      }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                        $card_type = 'DISCOVER';
                      }else{
                        $card_type = $request->CardType;
                      }
                      $data['card_type'] = $card_type;
                    }

                    $result = Ticket::create($data);
                    
                    $facilityName = ucwords($facility->full_name);

                    $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                    //check gate api
                    if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                      }
                    }
                    //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                    $msg =  "WELCOME. #$result->ticket_number";
            
                    $data = ['msg' => $msg];
                    $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                    $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                    $this->log->info("Checkin done, response sent.");
                    return $data;
                }
                $parkingNowTime = date("Y-m-d H:i:s");
                $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                $eventStartTime = $todayEvent->start_time;
                $eventEndTime = $todayEvent->end_time;

                //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                  //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                  $driveupRate = 0;
                    if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                      $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                    }else{
                      $driveupRate = $facility->base_rate + $facility->processing_fee;
                    }
                    $data['price'] =  $driveupRate;
                  return $data;
                }else{
                  
                  $data['user_id'] = $user->id;
                  $data['checkin_gate'] = $request->gate_id;
                  $data['facility_id'] = $request->facility_id;
                  $data['is_checkin'] = 1;
                  $data['ticket_number'] = $this->checkTicketNumber();
                  $data['checkin_time'] = date('Y-m-d H:i:s');
                  $data['ticket_security_code'] = rand(1000, 9999);
                  $data['partner_id'] = $facility->owner_id;
                  $data['check_in_datetime'] = date('Y-m-d H:i:s');
                  $data['vp_device_checkin'] = '1';

                  $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                  $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                  if(isset($request->CardType)){
                    $card_type = '';
                    if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                      $card_type = 'VISA';
                    }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                      $card_type = 'MASTERCARD';
                    }else if(strtolower($request->CardType) == "jcb"){
                      $card_type = 'JCB';
                    }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                      $card_type = 'AMEX';
                    }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                      $card_type = 'DISCOVER';
                    }else{
                      $card_type = $request->CardType;
                    }
                    $data['card_type'] = $card_type;
                  }

                  $result = Ticket::create($data);
                  
                  $facilityName = ucwords($facility->full_name);

                  $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                  //check gate api
                  if($facility->open_gate_enabled == '1'){
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if($gateStatus == "true"){}else{
                      throw new ApiGenericException($gateStatus);
                    }
                  }
                  //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
                  $msg =  "WELCOME. #$result->ticket_number";
            
                  $data = ['msg' => $msg];
                  $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                  $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                  $this->log->info("Checkin done, response sent.");
                  return $data;

                }
                
            }
          
        }

        if($reservation->cancelled_at != ''){
          throw new ApiGenericException("Sorry, Your booking has been canceled.");
        }

        $qrCode = MapcoQrcode::with(['event','eventCategory.eventCategoryEvent.event'])->where("reservation_id", $reservation->id)->where("remain_usage", '>', '0')->get();
        if(count($qrCode) == 0){
              if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
              }
              $ticket = Ticket::where('reservation_id', $reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
              if($ticket){
                  throw new ApiGenericException('Sorry, You have already checked-in.');    
              }

              if($facility->is_prepaid_first == '2'){
                $today = date("Y-m-d");
                $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
                if(!$todayEvent){                
                      $data['user_id'] = $user->id;
                      $data['checkin_gate'] = $request->gate_id;
                      $data['facility_id'] = $request->facility_id;
                      $data['is_checkin'] = 1;
                      $data['ticket_number'] = $this->checkTicketNumber();
                      $data['checkin_time'] = date('Y-m-d H:i:s');
                      $data['ticket_security_code'] = rand(1000, 9999);
                      $data['partner_id'] = $facility->owner_id;
                      $data['check_in_datetime'] = date('Y-m-d H:i:s');
                      $data['vp_device_checkin'] = '1';

                      $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                      $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                      if(isset($request->CardType)){
                        $card_type = '';
                        if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                          $card_type = 'VISA';
                        }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                          $card_type = 'MASTERCARD';
                        }else if(strtolower($request->CardType) == "jcb"){
                          $card_type = 'JCB';
                        }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                          $card_type = 'AMEX';
                        }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                          $card_type = 'DISCOVER';
                        }else{
                          $card_type = $request->CardType;
                        }
                        $data['card_type'] = $card_type;
                      }

                      $result = Ticket::create($data);
                      
                      $facilityName = ucwords($facility->full_name);
  
                      $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                      //check gate api
                      if($facility->open_gate_enabled == '1'){
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                        }
                      }

                      //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                      $msg =  "WELCOME. #$result->ticket_number";
            
                      $data = ['msg' => $msg];
                      $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                      $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                      $this->log->info("Checkin done, response sent.");
                      return $data;
                  }
                  $parkingNowTime = date("Y-m-d H:i:s");
                  $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                  $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                  $eventStartTime = $todayEvent->start_time;
                  $eventEndTime = $todayEvent->end_time;
  
                  //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                  if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                    //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                    $driveupRate = 0;
                    if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                      $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                    }else{
                      $driveupRate = $facility->base_rate + $facility->processing_fee;
                    }
                    $data['price'] =  $driveupRate;
                    return $data;
                  }else{
                    $data['user_id'] = $user->id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['partner_id'] = $facility->owner_id;
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    $data['vp_device_checkin'] = '1';

                    $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                    $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                    if(isset($request->CardType)){
                      $card_type = '';
                      if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                        $card_type = 'VISA';
                      }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                        $card_type = 'MASTERCARD';
                      }else if(strtolower($request->CardType) == "jcb"){
                        $card_type = 'JCB';
                      }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                        $card_type = 'AMEX';
                      }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                        $card_type = 'DISCOVER';
                      }else{
                        $card_type = $request->CardType;
                      }
                      $data['card_type'] = $card_type;
                    }

                    $result = Ticket::create($data);
                    
                    $facilityName = ucwords($facility->full_name);

                    $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                    //check gate api
                    if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                      }
                    }

                    //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                    $msg =  "WELCOME. #$result->ticket_number";
                    
                    $data = ['msg' => $msg];
                    $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                    $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                    $this->log->info("Checkin done, response sent.");
                    return $data;
                  }
                  
            }if($facility->is_prepaid_first == '0'){
              $today = date("Y-m-d");
              $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
              if(!$todayEvent){                
                    $data['user_id'] = $user->id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['partner_id'] = $facility->owner_id;
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['vp_device_checkin'] = '1';

                    $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                    $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                    if(isset($request->CardType)){
                      $card_type = '';
                      if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                        $card_type = 'VISA';
                      }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                        $card_type = 'MASTERCARD';
                      }else if(strtolower($request->CardType) == "jcb"){
                        $card_type = 'JCB';
                      }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                        $card_type = 'AMEX';
                      }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                        $card_type = 'DISCOVER';
                      }else{
                        $card_type = $request->CardType;
                      }
                      $data['card_type'] = $card_type;
                    }

                    $result = Ticket::create($data);
                    
                    $facilityName = ucwords($facility->full_name);

                    $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                    //check gate api
                    if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                      }
                    }

                    //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                    $msg =  "WELCOME. #$result->ticket_number";
                    
                    $data = ['msg' => $msg];
                    $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                    $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                    $this->log->info("Checkin done, response sent.");
                    return $data;
                }
                    $data['user_id'] = $user->id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['partner_id'] = $facility->owner_id;
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    $data['event_id'] = $todayEvent->id;
                    $data['vp_device_checkin'] = '1';

                    $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                    $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                    if(isset($request->CardType)){
                      $card_type = '';
                      if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                        $card_type = 'VISA';
                      }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                        $card_type = 'MASTERCARD';
                      }else if(strtolower($request->CardType) == "jcb"){
                        $card_type = 'JCB';
                      }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                        $card_type = 'AMEX';
                      }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                        $card_type = 'DISCOVER';
                      }else{
                        $card_type = $request->CardType;
                      }
                      $data['card_type'] = $card_type;
                    }

                    $result = Ticket::create($data);
                    
                    $facilityName = ucwords($facility->full_name);

                    $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                    //check gate api
                    if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                      }
                    }

                    //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                    $msg =  "WELCOME. #$result->ticket_number";
                    
                    $data = ['msg' => $msg];
                    $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                    $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                    $this->log->info("Checkin done, response sent.");
                    return $data;
                
          }if($facility->is_prepaid_first == '1'){
            $today = date("Y-m-d");
            $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
            if(!$todayEvent){                
                  $data['user_id'] = $user->id;
                  $data['checkin_gate'] = $request->gate_id;
                  $data['facility_id'] = $request->facility_id;
                  $data['is_checkin'] = 1;
                  $data['ticket_number'] = $this->checkTicketNumber();
                  $data['ticket_security_code'] = rand(1000, 9999);
                  $data['partner_id'] = $facility->owner_id;
                  $data['check_in_datetime'] = date('Y-m-d H:i:s');
                  $data['checkin_time'] = date('Y-m-d H:i:s');
                  $data['vp_device_checkin'] = '1';

                  $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                  $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                  if(isset($request->CardType)){
                    $card_type = '';
                    if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                      $card_type = 'VISA';
                    }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                      $card_type = 'MASTERCARD';
                    }else if(strtolower($request->CardType) == "jcb"){
                      $card_type = 'JCB';
                    }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                      $card_type = 'AMEX';
                    }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                      $card_type = 'DISCOVER';
                    }else{
                      $card_type = $request->CardType;
                    }
                    $data['card_type'] = $card_type;
                  }

                  $result = Ticket::create($data);
                  
                  $facilityName = ucwords($facility->full_name);

                  $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                  //check gate api
                  if($facility->open_gate_enabled == '1'){
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if($gateStatus == "true"){}else{
                      throw new ApiGenericException($gateStatus);
                    }
                  }

                  //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                  $msg =  "WELCOME. #$result->ticket_number";
                    
                  $data = ['msg' => $msg];
                  $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                  $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                  $this->log->info("Checkin done, response sent.");
                  return $data;
              }
              $parkingNowTime = date("Y-m-d H:i:s");
              $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
              $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
              $eventStartTime = $todayEvent->start_time;
              $eventEndTime = $todayEvent->end_time;

              //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
              if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                $driveupRate = 0;
                    if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                      $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                    }else{
                      $driveupRate = $facility->base_rate + $facility->processing_fee;
                    }
                    $data['price'] =  $driveupRate;
                return $data;
              }else{
                
                $data['user_id'] = $user->id;
                $data['checkin_gate'] = $request->gate_id;
                $data['facility_id'] = $request->facility_id;
                $data['is_checkin'] = 1;
                $data['ticket_number'] = $this->checkTicketNumber();
                $data['ticket_security_code'] = rand(1000, 9999);
                $data['partner_id'] = $facility->owner_id;
                $data['check_in_datetime'] = date('Y-m-d H:i:s');
                $data['checkin_time'] = date('Y-m-d H:i:s');
                $data['vp_device_checkin'] = '1';

                $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                if(isset($request->CardType)){
                  $card_type = '';
                  if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                    $card_type = 'VISA';
                  }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                    $card_type = 'MASTERCARD';
                  }else if(strtolower($request->CardType) == "jcb"){
                    $card_type = 'JCB';
                  }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                    $card_type = 'AMEX';
                  }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                    $card_type = 'DISCOVER';
                  }else{
                    $card_type = $request->CardType;
                  }
                  $data['card_type'] = $card_type;
                }

                $result = Ticket::create($data);
                
                $facilityName = ucwords($facility->full_name);

                $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                //check gate api
                if($facility->open_gate_enabled == '1'){
                  $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                  if($gateStatus == "true"){}else{
                    throw new ApiGenericException($gateStatus);
                  }
                }

                //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                $msg =  "WELCOME. #$result->ticket_number";
                    
                $data = ['msg' => $msg];
                $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                $this->log->info("Checkin done, response sent.");
                return $data;

              }
              
          }

              

          }else{
            
              $today = date("Y-m-d");
              if($qrCode[0]->event_id != '0'){
                  
                  //$event = Event::whereDate("start_time", '>=', $today)->whereDate("end_time", '<=', $today)->where('id',$qrCode[0]->event_id)->first();
                  $event = Event::whereDate("start_time", '=', $today)->where('id',$qrCode[0]->event_id)->where("is_active", '1')->first();                  
                  if(!$event){
                      //throw new ApiGenericException('Sorry, No event found.');    
                      if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                        throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                      }
                      
                      
                      if($facility->is_prepaid_first == '2'){
                        $today = date("Y-m-d");
                        $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                        if(!$todayEvent){                
                              $data['user_id'] = $user->id;
                              $data['checkin_gate'] = $request->gate_id;
                              $data['facility_id'] = $request->facility_id;
                              $data['is_checkin'] = 1;
                              $data['ticket_number'] = $this->checkTicketNumber();
                              $data['ticket_security_code'] = rand(1000, 9999);
                              $data['partner_id'] = $facility->owner_id;
                              $data['check_in_datetime'] = date('Y-m-d H:i:s');
                              $data['checkin_time'] = date('Y-m-d H:i:s');

                              $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                              $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                              if(isset($request->CardType)){
                                $card_type = '';
                                if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                                  $card_type = 'VISA';
                                }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                                  $card_type = 'MASTERCARD';
                                }else if(strtolower($request->CardType) == "jcb"){
                                  $card_type = 'JCB';
                                }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                                  $card_type = 'AMEX';
                                }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                                  $card_type = 'DISCOVER';
                                }else{
                                  $card_type = $request->CardType;
                                }
                                $data['card_type'] = $card_type;
                              }

                              $result = Ticket::create($data);
                              
                              $facilityName = ucwords($facility->full_name);
          
                              $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                              //check gate api
                              if($facility->open_gate_enabled == '1'){
                                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                                if($gateStatus == "true"){}else{
                                  throw new ApiGenericException($gateStatus);
                                }
                              }

                              //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                              $msg =  "WELCOME. #$result->ticket_number";
                    
                              $data = ['msg' => $msg];
                              $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                              $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                              $this->log->info("Checkin done, response sent.");
                              return $data;
                          }
                          $parkingNowTime = date("Y-m-d H:i:s");
                          $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                          $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                          $eventStartTime = $todayEvent->start_time;
                          $eventEndTime = $todayEvent->end_time;
          
                          //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                          if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                            //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                            $driveupRate = 0;
                            if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                              $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                            }else{
                              $driveupRate = $facility->base_rate + $facility->processing_fee;
                            }
                            $data['price'] =  $driveupRate;
                            return $data;
                          }else{
                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                            $from = Carbon::createFromFormat('Y-m-d H:i:s', $eventEndTime);
                            if($todayEvent->base_event_hours != ''){
                              $diff_in_hours = $todayEvent->base_event_hours;
                            }else{
                              $diff_in_hours = $arrival_time->diffInRealHours($from);
                              if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                                $diff_in_hours = self::EVENT_THRESHOLD_TYPE;     
                              }
                            }
                          
                            /** this function is used to get Availability Information for respective facility **/
                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);
                            $data['price'] =  $rate['price'] + $facility->processing_fee;
                            return $data;
                          }
                          
                    }if($facility->is_prepaid_first == '0'){
                      $today = date("Y-m-d");
                      $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                      if(!$todayEvent){                
                            $data['user_id'] = $user->id;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkTicketNumber();
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['partner_id'] = $facility->owner_id;
                            $data['check_in_datetime'] = date('Y-m-d H:i:s');
                            $data['vp_device_checkin'] = '1';

                            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                            if(isset($request->CardType)){
                              $card_type = '';
                              if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                                $card_type = 'VISA';
                              }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                                $card_type = 'MASTERCARD';
                              }else if(strtolower($request->CardType) == "jcb"){
                                $card_type = 'JCB';
                              }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                                $card_type = 'AMEX';
                              }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                                $card_type = 'DISCOVER';
                              }else{
                                $card_type = $request->CardType;
                              }
                              $data['card_type'] = $card_type;
                            }

                            $result = Ticket::create($data);
                            
                            $facilityName = ucwords($facility->full_name);
        
                            $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                            //check gate api
                            if($facility->open_gate_enabled == '1'){
                              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                              if($gateStatus == "true"){}else{
                                throw new ApiGenericException($gateStatus);
                              }
                            }

                            //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                            $msg =  "WELCOME. #$result->ticket_number";
                    
                            $data = ['msg' => $msg];
                            $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                            $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                            $this->log->info("Checkin done, response sent.");
                            return $data;
                        }
                            $data['user_id'] = $user->id;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkTicketNumber();
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['partner_id'] = $facility->owner_id;
                            $data['check_in_datetime'] = date('Y-m-d H:i:s');
                            $data['event_id'] = $todayEvent->id;
                            $data['vp_device_checkin'] = '1';

                            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                            if(isset($request->CardType)){
                              $card_type = '';
                              if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                                $card_type = 'VISA';
                              }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                                $card_type = 'MASTERCARD';
                              }else if(strtolower($request->CardType) == "jcb"){
                                $card_type = 'JCB';
                              }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                                $card_type = 'AMEX';
                              }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                                $card_type = 'DISCOVER';
                              }else{
                                $card_type = $request->CardType;
                              }
                              $data['card_type'] = $card_type;
                            }

                            $result = Ticket::create($data);
                            
                            $facilityName = ucwords($facility->full_name);
        
                            $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                            //check gate api
                            if($facility->open_gate_enabled == '1'){
                              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                              if($gateStatus == "true"){}else{
                                throw new ApiGenericException($gateStatus);
                              }
                            }

                            //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                            $msg =  "WELCOME. #$result->ticket_number";
                    
                            $data = ['msg' => $msg];
                            $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                            $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                            $this->log->info("Checkin done, response sent.");
                            return $data;
                        
                  }if($facility->is_prepaid_first == '1'){
                    $today = date("Y-m-d");
                    $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                    if(!$todayEvent){                
                          $data['user_id'] = $user->id;
                          $data['checkin_gate'] = $request->gate_id;
                          $data['facility_id'] = $request->facility_id;
                          $data['is_checkin'] = 1;
                          $data['ticket_number'] = $this->checkTicketNumber();
                          $data['checkin_time'] = date('Y-m-d H:i:s');
                          $data['ticket_security_code'] = rand(1000, 9999);
                          $data['partner_id'] = $facility->owner_id;
                          $data['check_in_datetime'] = date('Y-m-d H:i:s');
                          $data['vp_device_checkin'] = '1';

                          $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                          $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                          if(isset($request->CardType)){
                            $card_type = '';
                            if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                              $card_type = 'VISA';
                            }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                              $card_type = 'MASTERCARD';
                            }else if(strtolower($request->CardType) == "jcb"){
                              $card_type = 'JCB';
                            }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                              $card_type = 'AMEX';
                            }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                              $card_type = 'DISCOVER';
                            }else{
                              $card_type = $request->CardType;
                            }
                            $data['card_type'] = $card_type;
                          }

                          $result = Ticket::create($data);
                          
                          $facilityName = ucwords($facility->full_name);
        
                          $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                          //check gate api
                          if($facility->open_gate_enabled == '1'){
                            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                            if($gateStatus == "true"){}else{
                              throw new ApiGenericException($gateStatus);
                            }
                          }

                          //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                          $msg =  "WELCOME. #$result->ticket_number";
                    
                          $data = ['msg' => $msg];
                          $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                          $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                          $this->log->info("Checkin done, response sent.");
                          return $data;
                      }
                      $parkingNowTime = date("Y-m-d H:i:s");
                      $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                      $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                      $eventStartTime = $todayEvent->start_time;
                      $eventEndTime = $todayEvent->end_time;
        
                      //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                      if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                        //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                          $driveupRate = 0;
                          if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                            $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                          }else{
                            $driveupRate = $facility->base_rate + $facility->processing_fee;
                          }
                          $data['price'] =  $driveupRate;
                          return $data;
                      }else{
                        
                        $data['user_id'] = $user->id;
                        $data['checkin_gate'] = $request->gate_id;
                        $data['facility_id'] = $request->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = $this->checkTicketNumber();
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['partner_id'] = $facility->owner_id;
                        $data['check_in_datetime'] = date('Y-m-d H:i:s');
                        $data['vp_device_checkin'] = '1';

                        $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                        $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                        if(isset($request->CardType)){
                          $card_type = '';
                          if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                            $card_type = 'VISA';
                          }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                            $card_type = 'MASTERCARD';
                          }else if(strtolower($request->CardType) == "jcb"){
                            $card_type = 'JCB';
                          }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                            $card_type = 'AMEX';
                          }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                            $card_type = 'DISCOVER';
                          }else{
                            $card_type = $request->CardType;
                          }
                          $data['card_type'] = $card_type;
                        }

                        $result = Ticket::create($data);
                        
                        $facilityName = ucwords($facility->full_name);
        
                        $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                        //check gate api
                        if($facility->open_gate_enabled == '1'){
                          $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                          if($gateStatus == "true"){}else{
                            throw new ApiGenericException($gateStatus);
                          }
                        }

                        //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                        $msg =  "WELCOME. #$result->ticket_number";
                    
                        $data = ['msg' => $msg];
                        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        $this->log->info("Checkin done, response sent.");
                        return $data;
        
                      }
                      
                  }
                      
                  }
                  $event_id = $qrCode[0]->event_id;
              }if($qrCode[0]->event_id == '0' && ($qrCode[0]->event_category_id != '0' || $qrCode[0]->event_category_id != '')){
                  //$today = date("Y-m-d");
                  //$today = "2022-12-07";
                  $eventCategoryEvent = EventCategoryEvent::leftJoin('events' , 'events.id' , '=' , 'event_category_events.event_id')
                  ->select('events.id','events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode[0]->event_category_id)->first();
                  if(!isset($eventCategoryEvent->start_time)){
                    //throw new ApiGenericException('Sorry, No event found.');    
                    if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                      throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                    }
                    
                    
                    if($facility->is_prepaid_first == '2'){
                      $today = date("Y-m-d");
                      $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                      if(!$todayEvent){                
                            $data['user_id'] = $user->id;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkTicketNumber();
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['partner_id'] = $facility->owner_id;
                            $data['check_in_datetime'] = date('Y-m-d H:i:s');
                            
                            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                            if(isset($request->CardType)){
                              $card_type = '';
                              if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                                $card_type = 'VISA';
                              }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                                $card_type = 'MASTERCARD';
                              }else if(strtolower($request->CardType) == "jcb"){
                                $card_type = 'JCB';
                              }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                                $card_type = 'AMEX';
                              }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                                $card_type = 'DISCOVER';
                              }else{
                                $card_type = $request->CardType;
                              }
                              $data['card_type'] = $card_type;
                            }

                            $result = Ticket::create($data);
                            
                            $facilityName = ucwords($facility->full_name);
        
                            $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                            //check gate api
                            if($facility->open_gate_enabled == '1'){
                              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                              if($gateStatus == "true"){}else{
                                throw new ApiGenericException($gateStatus);
                              }
                            }

                            //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                            $msg =  "WELCOME. #$result->ticket_number";
                    
                            $data = ['msg' => $msg];
                            $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                            $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                            $this->log->info("Checkin done, response sent.");
                            return $data;
                        }
                        $parkingNowTime = date("Y-m-d H:i:s");
                        $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                        $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                        $eventStartTime = $todayEvent->start_time;
                        $eventEndTime = $todayEvent->end_time;
        
                        //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                        if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                          //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                            $driveupRate = 0;
                            if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                              $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                            }else{
                              $driveupRate = $facility->base_rate + $facility->processing_fee;
                            }
                            $data['price'] =  $driveupRate;
                          return $data;
                        }else{
                          $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                          $from = Carbon::createFromFormat('Y-m-d H:i:s', $eventEndTime);
                          if($todayEvent->base_event_hours != ''){
                            $diff_in_hours = $todayEvent->base_event_hours;
                          }else{
                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                            if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                              $diff_in_hours = self::EVENT_THRESHOLD_TYPE;     
                            }
                          }
                        
                          /** this function is used to get Availability Information for respective facility **/
                          $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);
                          $data['price'] =  $rate['price'] + $facility->processing_fee;
                          return $data;
                        }
                        
                  }if($facility->is_prepaid_first == '0'){
                    $today = date("Y-m-d");
                    $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                    if(!$todayEvent){                
                          $data['user_id'] = $user->id;
                          $data['checkin_gate'] = $request->gate_id;
                          $data['facility_id'] = $request->facility_id;
                          $data['is_checkin'] = 1;
                          $data['ticket_number'] = $this->checkTicketNumber();
                          $data['checkin_time'] = date('Y-m-d H:i:s');
                          $data['ticket_security_code'] = rand(1000, 9999);
                          $data['partner_id'] = $facility->owner_id;
                          $data['check_in_datetime'] = date('Y-m-d H:i:s');
                          $data['vp_device_checkin'] = '1';

                          $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                          $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                          if(isset($request->CardType)){
                            $card_type = '';
                            if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                              $card_type = 'VISA';
                            }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                              $card_type = 'MASTERCARD';
                            }else if(strtolower($request->CardType) == "jcb"){
                              $card_type = 'JCB';
                            }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                              $card_type = 'AMEX';
                            }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                              $card_type = 'DISCOVER';
                            }else{
                              $card_type = $request->CardType;
                            }
                            $data['card_type'] = $card_type;
                          }

                          $result = Ticket::create($data);
                          
                          $facilityName = ucwords($facility->full_name);
      
                          $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                          //check gate api
                          if($facility->open_gate_enabled == '1'){
                            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                            if($gateStatus == "true"){}else{
                              throw new ApiGenericException($gateStatus);
                            }
                          }

                          //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                          $msg =  "WELCOME. #$result->ticket_number";
                    
                          $data = ['msg' => $msg];
                          $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                          $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                          $this->log->info("Checkin done, response sent.");
                          return $data;
                      }
                          $data['user_id'] = $user->id;
                          $data['checkin_gate'] = $request->gate_id;
                          $data['facility_id'] = $request->facility_id;
                          $data['is_checkin'] = 1;
                          $data['ticket_number'] = $this->checkTicketNumber();
                          $data['checkin_time'] = date('Y-m-d H:i:s');
                          $data['ticket_security_code'] = rand(1000, 9999);
                          $data['partner_id'] = $facility->owner_id;
                          $data['check_in_datetime'] = date('Y-m-d H:i:s');
                          $data['event_id'] = $todayEvent->id;
                          $data['vp_device_checkin'] = '1';

                          $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                          $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                          if(isset($request->CardType)){
                            $card_type = '';
                            if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                              $card_type = 'VISA';
                            }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                              $card_type = 'MASTERCARD';
                            }else if(strtolower($request->CardType) == "jcb"){
                              $card_type = 'JCB';
                            }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                              $card_type = 'AMEX';
                            }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                              $card_type = 'DISCOVER';
                            }else{
                              $card_type = $request->CardType;
                            }
                            $data['card_type'] = $card_type;
                          }

                          $result = Ticket::create($data);
                          
                          $facilityName = ucwords($facility->full_name);
      
                          $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                          //check gate api
                          if($facility->open_gate_enabled == '1'){
                            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                            if($gateStatus == "true"){}else{
                              throw new ApiGenericException($gateStatus);
                            }
                          }

                          //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                          $msg =  "WELCOME. #$result->ticket_number";
                    
                          $data = ['msg' => $msg];
                          $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                          $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                          $this->log->info("Checkin done, response sent.");
                          return $data;
                      
                }if($facility->is_prepaid_first == '1'){
                  $today = date("Y-m-d");
                  $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                  if(!$todayEvent){                
                        $data['user_id'] = $user->id;
                        $data['checkin_gate'] = $request->gate_id;
                        $data['facility_id'] = $request->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = $this->checkTicketNumber();
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['partner_id'] = $facility->owner_id;
                        $data['check_in_datetime'] = date('Y-m-d H:i:s');
                        $data['vp_device_checkin'] = '1';

                        $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                        $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                        if(isset($request->CardType)){
                          $card_type = '';
                          if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                            $card_type = 'VISA';
                          }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                            $card_type = 'MASTERCARD';
                          }else if(strtolower($request->CardType) == "jcb"){
                            $card_type = 'JCB';
                          }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                            $card_type = 'AMEX';
                          }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                            $card_type = 'DISCOVER';
                          }else{
                            $card_type = $request->CardType;
                          }
                          $data['card_type'] = $card_type;
                        }

                        $result = Ticket::create($data);
                        
                        $facilityName = ucwords($facility->full_name);
      
                        $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                        //check gate api
                        if($facility->open_gate_enabled == '1'){
                          $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                          if($gateStatus == "true"){}else{
                            throw new ApiGenericException($gateStatus);
                          }
                        }
                        
                        $msg =  "WELCOME. #$result->ticket_number";
                    
                        $data = ['msg' => $msg];
                        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        $this->log->info("Checkin done, response sent.");
                        return $data;
                    }
                    $parkingNowTime = date("Y-m-d H:i:s");
                    $parkingStartTime = date("Y-m-d")." ". $todayEvent->parking_start_time;
                    $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
                    $eventStartTime = $todayEvent->start_time;
                    $eventEndTime = $todayEvent->end_time;
      
                    //dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
                    if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
                      //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                      $driveupRate = 0;
                      if($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0){
                        $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
                      }else{
                        $driveupRate = $facility->base_rate + $facility->processing_fee;
                      }
                      $data['price'] =  $driveupRate;
                      return $data;
                    }else{
                      
                      $data['user_id'] = $user->id;
                      $data['checkin_gate'] = $request->gate_id;
                      $data['facility_id'] = $request->facility_id;
                      $data['is_checkin'] = 1;
                      $data['ticket_number'] = $this->checkTicketNumber();
                      $data['checkin_time'] = date('Y-m-d H:i:s');
                      $data['ticket_security_code'] = rand(1000, 9999);
                      $data['partner_id'] = $facility->owner_id;
                      $data['check_in_datetime'] = date('Y-m-d H:i:s');
                      $data['vp_device_checkin'] = '1';

                      $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                      $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                      if(isset($request->CardType)){
                        $card_type = '';
                        if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                          $card_type = 'VISA';
                        }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                          $card_type = 'MASTERCARD';
                        }else if(strtolower($request->CardType) == "jcb"){
                          $card_type = 'JCB';
                        }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                          $card_type = 'AMEX';
                        }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                          $card_type = 'DISCOVER';
                        }else{
                          $card_type = $request->CardType;
                        }
                        $data['card_type'] = $card_type;
                      }

                      $result = Ticket::create($data);
                      
                      $facilityName = ucwords($facility->full_name);
      
                      $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                      //check gate api
                      if($facility->open_gate_enabled == '1'){
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                        }
                      }

                      //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));

                      $msg =  "WELCOME. #$result->ticket_number";
                    
                      $data = ['msg' => $msg];
                      $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                      $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                      $this->log->info("Checkin done, response sent.");
                      return $data;
      
                    }
                    
                }

                  }
                  $event_id = $eventCategoryEvent->id;          
                }
                
            $user = $reservation->user;          
            $data['user_id'] = $user->id;
            $data['checkin_gate'] = $request->gate_id;
            $data['facility_id'] = $request->facility_id;
            $data['is_checkin'] = 1;
            $data['ticket_number'] = $this->checkTicketNumber();
            $data['checkin_time'] = date('Y-m-d H:i:s');
            $data['ticket_security_code'] = rand(1000, 9999);
            $data['partner_id'] = $facility->owner_id;
          //if($reservation){
            $data['reservation_id'] = $reservation->id;
            $data['anet_transaction_id'] = $reservation->anet_transaction_id;
            $data['check_in_datetime'] = $reservation->start_timestamp;
            $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
            $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
            $data['total'] = $reservation->total;
            $data['grand_total'] = $reservation->total;
            $data['length'] = $reservation->length;
            $data['event_id'] = $event_id;

            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
            if(isset($request->CardType)){
              $card_type = '';
              if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                $card_type = 'VISA';
              }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                $card_type = 'MASTERCARD';
              }else if(strtolower($request->CardType) == "jcb"){
                $card_type = 'JCB';
              }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                $card_type = 'AMEX';
              }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                $card_type = 'DISCOVER';
              }else{
                $card_type = $request->CardType;
              }
              $data['card_type'] = $card_type;
            }

            $result = Ticket::create($data);
            
            $qrCode[0]->remain_usage = $qrCode[0]->remain_usage == 0 ? 0 : $qrCode[0]->remain_usage - 1;
            $qrCode[0]->save();

            //$reservation->is_ticket = '1';
            //$reservation->save();            

        //}
          /*$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          if($ticket){
            throw new ApiGenericException('You have already checked-in.');
          }*/

            
          }

          $facilityName = ucwords($facility->full_name);

          //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
          //$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
          //$this->customeReplySms($msg, $user->phone);
          
         $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


         //check gate api
        if($facility->open_gate_enabled == '1'){
          $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }
        }
        $msg =  "WELCOME. #$result->ticket_number";
                    
        $data = ['msg' => $msg];
        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
        $this->log->info("Checkin done, response sent.");
        return $data;
         
        }
        elseif(isset($gate) && $gate->gate_type == "exit"){
          $ticket = Ticket::where('user_id', $user->id)->where('is_checkout', '0')->orderBy("id", "Desc")->first();
          if(!$ticket){
              throw new ApiGenericException('Sorry, No Check-In found. Please contact to attendant.');

              $today = date("Y-m-d");
              $todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
              if(!$todayEvent){
                $data['price'] =  $facility->base_rate + $facility->processing_fee;
                return $data;
              }

              $reservation = Reservation::where('user_id', $user->id)->orderBy("id", "DESC")->first();  
              if(!$reservation){
                if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                  throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                }
                $data['price'] =  $facility->base_rate + $facility->processing_fee;
                return $data;
                /*$today = date("Y-m-d");
                $todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
                if(!$todayEvent){
                    //throw new ApiGenericException("Sorry, No event found.");
                    $data['price'] =  $facility->base_rate + $facility->processing_fee;
                    return $data;
                }
                if($todayEvent->event_rate == 0 || $todayEvent->event_rate == ''){
                    $data['price'] =  $facility->base_event_rate + $facility->processing_fee;
                }else{
                    $data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
                }
                $data['id'] = $todayEvent->id;
                return $data;*/

                //
              }

              if($reservation->cancelled_at != ''){
                throw new ApiGenericException("Sorry, Your booking has been canceled.");
              }


              $qrCode = MapcoQrcode::with(['event','eventCategory.eventCategoryEvent.event'])->where("reservation_id", $reservation->id)->where("remain_usage", '>', '0')->get();
              if(count($qrCode) == 0){
                if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                  throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                }
                  $data['price'] =  $facility->base_rate + $facility->processing_fee;
                  return $data;
                    /*$today = date("Y-m-d");
                    $todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
                    if(!$todayEvent){
                        throw new ApiGenericException("Sorry, No event found.");
                    }
                    if($todayEvent->event_rate == 0 || $todayEvent->event_rate == ''){
                        $data['price'] =  $facility->base_event_rate + $facility->processing_fee;
                    }else{
                        $data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
                    }
                    $data['id'] = $todayEvent->id;
                    return $data;*/

                }else{
                    
                    $today = date("Y-m-d");
                    if($qrCode[0]->event_id != '0'){
                        
                        //$event = Event::whereDate("start_time", '>=', $today)->whereDate("end_time", '<=', $today)->where('id',$qrCode[0]->event_id)->first();
                        $event = Event::whereDate("start_time", '=', $today)->where('id',$qrCode[0]->event_id)->first();                  
                        if(!$event){
                          if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                            throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                          }
                          $data['price'] =  $facility->base_rate + $facility->processing_fee;
                          return $data;
                            //throw new ApiGenericException('Sorry, No event found.');    
                        }
                        $event_id = $qrCode[0]->event_id;
                    }if($qrCode[0]->event_id == '0' && ($qrCode[0]->event_category_id != '0' || $qrCode[0]->event_category_id != '')){
                        //$today = date("Y-m-d");
                        //$today = "2022-12-07";
                        $eventCategoryEvent = EventCategoryEvent::leftJoin('events' , 'events.id' , '=' , 'event_category_events.event_id')
                        ->select('events.id','events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode[0]->event_category_id)->first();
                        if(!isset($eventCategoryEvent->start_time)){
                          if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
                            throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                          }
                          $data['price'] =  $facility->base_rate + $facility->processing_fee;
                          return $data;
                          //throw new ApiGenericException('Sorry, No event found.');    
                        }
                        $event_id = $eventCategoryEvent->id;          
                      }
          
                    $user = $reservation->user;          
                    $data['user_id'] = $user->id;
                  $data['checkout_gate'] = $request->gate_id;
                  $data['facility_id'] = $request->facility_id;
                  $data['is_checkout'] = 1;
                  $data['ticket_number'] = $this->checkTicketNumber();
                  //$data['checkin_time'] = date('Y-m-d H:i:s');
                  $data['ticket_security_code'] = rand(1000, 9999);
                  $data['partner_id'] = $facility->owner_id;
                //if($reservation){
                  $data['reservation_id'] = $reservation->id;
                  $data['anet_transaction_id'] = $reservation->anet_transaction_id;
                  $data['check_in_datetime'] = $reservation->start_timestamp;
                  $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                  $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                  $data['total'] = $reservation->total;
                  $data['grand_total'] = $reservation->total;
                  $data['length'] = $reservation->length;
                  $data['checkout_time'] = date('Y-m-d H:i:s');
                  $data['event_id'] = $event_id;
                  $data['checkout_without_checkin'] = '1';
                  $result = Ticket::create($data);
                  
                  $qrCode[0]->remain_usage = $qrCode[0]->remain_usage == 0 ? 0 : $qrCode[0]->remain_usage - 1;
                  $qrCode[0]->save();

                  //$reservation->is_ticket = '1';
                  //$reservation->save();            

              //}
                /*$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                if($ticket){
                  throw new ApiGenericException('You have already checked-in.');
                }*/

                //check gate api
                if($facility->open_gate_enabled == '1'){
                  $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                  if($gateStatus == "true"){}else{
                      throw new ApiGenericException($gateStatus);
                  }
                }

                $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
                $msg = "Thank you for visiting ".$facilityName.".";
                $this->customeReplySms($msg, $user->phone);
                //return $msg;
                $data = ['msg' => $msg];
                return $data;  
                }


                $data['price'] =  $facility->base_rate + $facility->processing_fee;
                return $data;
              /*if($todayEvent->event_rate == 0 || $todayEvent->event_rate == ''){
                  $data['price'] =  $facility->base_event_rate + $facility->processing_fee;
              }else{
                  $data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
              }
              $data['id'] = $todayEvent->id;
              return $data;*/
              //$data['price'] =  $facility->base_rate + $facility->processing_fee;
              //return $data;
          }
          if($ticket->anet_transaction_id == '' && $ticket->reservation_id == ''){
            $today = date("Y-m-d");
            $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
            if(!$todayEvent){
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
              $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
              $diff_in_hours = $arrival_time->diffInRealHours($from);
              if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
              }
              $checkinData['length'] = $diff_in_hours;
              /** this function is used to get Availability Information for respective facility **/
              $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);

              if($rate){
                if($rate['price'] == 0 || $rate['price'] == "0.00"){
                  $ticket->is_checkout= '1';
                  $ticket->checkout_gate = $request->gate_id;
                  $ticket->checkout_time = date("Y-m-d H:i:s");
                  $ticket->checkout_datetime = date("Y-m-d H:i:s");
                  $ticket->total= 0.00;
                  $ticket->grand_total= 0.00;
                  $ticket->length= $diff_in_hours;
                  $ticket->save();
                  
                  //check gate api
                  if($facility->open_gate_enabled == '1'){
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                    }
                  }

                  $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
                  $msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";


                  $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                  $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                  $url = env('RECEIPT_URL');
                  $sms_msg = "Thank you for visiting ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
                            
                  $this->customeReplySms($sms_msg, $user->phone);
                  $this->log->info("SMS sent, response sent");
                  $data = ['msg' => $msg];
                  $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                  $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                  return $data;
                }
              }

              return $rate;
              
            }
              $parkingNowTime = date("Y-m-d H:i:s");
              $parkingStartTime = date("Y-m-d", strtotime($todayEvent->start_time))." ".$todayEvent->parking_start_time;
              $parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time))." ".$todayEvent->parking_end_time;
              $eventStartTime = $todayEvent->start_time;
              $eventEndTime = $todayEvent->end_time;

              $checkinTime = $ticket->checkin_time;

              //dd($parkingNowTime, $parkingStartTime, $parkingEndTime, $ticket->checkin_time);
              if(strtotime($checkinTime) >= strtotime($parkingStartTime) && strtotime($checkinTime) <= strtotime($parkingEndTime)){
                $data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                return $data;
              }else{
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
                if($facility->is_prepaid_first == '0'){
                  $diff_in_hours = $arrival_time->diffInRealHours($from);
                    if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                      $diff_in_hours = self::EVENT_THRESHOLD_TYPE;     
                    }
                }else{
                  /*if($todayEvent->base_event_hours != ''){
                    $diff_in_hours = $todayEvent->base_event_hours;
                  }else{*/
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    
                    if($diff_in_mins > 0){
                      $diff_in_hours =number_format($diff_in_mins/60, 2);     
                    }
                    if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                      $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                      $diff_in_hours = number_format($diff_in_mins/60, 2);     
                    }
                  //}
                }

                //dd($arrival_time, $diff_in_hours, $ticket->checkin_time);
                /** this function is used to get Availability Information for respective facility **/
                $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);

                if($rate){
                  if($rate['price'] == 0 || $rate['price'] == "0.00"){
                    $ticket->is_checkout= '1';
                    $ticket->checkout_gate = $request->gate_id;
                    $ticket->checkout_time = date("Y-m-d H:i:s");
                    $ticket->checkout_datetime = date("Y-m-d H:i:s");
                    $ticket->total= 0.00;
                    $ticket->grand_total= 0.00;
                    $ticket->length= $diff_in_hours;
                    $ticket->save();
                    
                    //check gate api
                    if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                      }
                    }
  
                    $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
                  $msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";


                  $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                  $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                  $url = env('RECEIPT_URL');
                  $sms_msg = "Thank you for visiting ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
                            
                  $this->customeReplySms($sms_msg, $user->phone);
                  $this->log->info("SMS sent, response sent");
                  $data = ['msg' => $msg];
                  $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                  $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                  return $data;
                  }
                }
                
                $data['price'] =  $rate['price'] + $facility->processing_fee;
                return $data;
              }
          }elseif($ticket->anet_transaction_id != ''){

             if($ticket->event_id != '' || $ticket->event_id != '0') {

              $parkingNowTime = date("Y-m-d H:i:s");
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
              $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
              
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                if($diff_in_mins > 0){
                  $diff_in_hours =number_format($diff_in_mins/60, 2);     
                }
                if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                  $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                  $diff_in_hours = number_format($diff_in_mins/60, 2);     
                }
                //dd($from,  $ticket->checkin_time, $diff_in_hours, $diff_in_mins);  
                $todayEvent = Event::find($ticket->event_id);
                //dd($todayEvent->base_event_hours, $diff_in_hours);
                if($todayEvent->base_event_hours >= $diff_in_hours){
                  
                }else{
                  $new_arrival_time = $arrival_time->addHours($todayEvent->base_event_hours);
                  $diff_in_hours = $diff_in_hours - $todayEvent->base_event_hours;
                  //$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
                  /** this function is used to get Availability Information for respective facility **/
                  $rate = $facility->rateForReservation($new_arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);
                  $data['price'] =  $rate['price'];
                  $data['is_overstay'] =  '1';
                  return $data;
                }

             }

          }

          $ticket->is_checkout= '1';
          $ticket->checkout_gate = $request->gate_id;
          $ticket->checkout_time = date("Y-m-d H:i:s");
          $ticket->save();
          
          //check gate api
          if($facility->open_gate_enabled == '1'){
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
            }
          }

          $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
          $msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";


          $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
          $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
          $url = env('RECEIPT_URL');
          $sms_msg = "Thank you for visiting ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
                    
          $this->customeReplySms($sms_msg, $user->phone);
          $this->log->info("SMS sent, response sent");
          $data = ['msg' => $msg];
          $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
          $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
          return $data;
        }else{
          throw new ApiGenericException('Sorry! Invalid gate details.');
        }
      //return "Success";
    }


    public function ticketDriveupCheckinCheckout(Request $request){
        
      $this->log->info("Driveup Request received --".json_encode($request->all()));
      $this->setCustomTimezone($request->facility_id);
        $facility = Facility::find($request->facility_id);
        
        if(!$facility){
          throw new ApiGenericException("Invalid garage.");
        }
        
        $facilityName =  ucwords($facility->full_name);
        $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 
        if(!$gate){
          $this->saveAnetTransaction($request);
          throw new ApiGenericException("The system is not currently available. Please try again later.");
        }

        if(isset($gate) && $gate->active == '0'){
          $this->saveAnetTransaction($request);
          throw new ApiGenericException('The system is not currently available. Please try again later.');    
        }

        if($facility->check_vehicle_enabled == '1'){  
        $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }
        }


        if($request->session_id == ''){
        throw new ApiGenericException('Card info not found. Please try again.');    
      }

      $userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
      if($userSessionExist){
        $user = User::where('id', $userSessionExist->user_id)->first();  
      }else{
        throw new ApiGenericException('Invalid user.');
      }
      //$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
      if(!$user){
        $user = User::create(
            [
            'name' => '',
            'email' => '',
            'phone' => '',
            'password' => Hash::make(str_random(60)),
            'anon' => true,
            'user_type' => '5',
            'created_by' => $facility->owner_id,
            'session_id' => $request->session_id,
            ]
        );
      }
        
        if(isset($gate) && $gate->gate_type == "entry"){

          $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          if($ticket){
              $this->saveAnetTransaction($request);
              throw new ApiGenericException('You have already checked-in.');    
          }
          $today = date("Y-m-d");
          $event = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();

          $processingFee = 0;
          $taxFee = 0;
          if(!$event){
                    
            $rate['price'] = $facility->base_rate;
            $taxRate = $facility->getTaxRate($rate);          // to get tax price                
            $driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
            $processingFee = $facility->processing_fee;
            $taxFee = $taxRate;
            $data['length'] = 24;
          }else{
            $parkingNowTime = date("Y-m-d H:i:s");
            $parkingStartTime = date("Y-m-d")." ". $event->parking_start_time;
            $parkingEndTime = date("Y-m-d", strtotime($event->end_time))." ".$event->parking_end_time;
            $eventStartTime = $event->start_time;
            $eventEndTime = $event->end_time;
  
            if(strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)){
              $driveupRate = 0;
              if($event->driveup_event_rate != '' && $event->driveup_event_rate > 0){
                $rate['price'] = $event->driveup_event_rate;
                $taxRate = $facility->getTaxRate($rate);          // to get tax price                
                $driveupRate = $event->driveup_event_rate + $facility->drive_up_processing_fee + $taxRate;
                $processingFee = $facility->drive_up_processing_fee;
                $taxFee = $taxRate;
              }else{
                $rate['price'] = $facility->base_rate;
                $taxRate = $facility->getTaxRate($rate);          // to get tax price                
                $driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
                $processingFee = $facility->processing_fee;
                $taxFee = $taxRate;
              }
            }
          }
          
          
          $data['user_id'] = $user->id;
          $data['checkin_gate'] = $request->gate_id;
          $data['facility_id'] = $request->facility_id;
          $data['is_checkin'] = 1;
          $data['ticket_number'] = $this->checkTicketNumber();
          $data['checkin_time'] = date('Y-m-d H:i:s');
          if($event){
            $data['check_in_datetime'] = $event->start_time;
            $data['estimated_checkout'] = $event->end_time;
            $data['checkout_datetime'] = $event->end_time;
            $data['checkout_time'] = $event->end_time;
            $data['event_id'] = $event->id;
          }else{
            $ticketStartDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $ticketEndDate = $ticketStartDate->addHours(24);
            $data['check_in_datetime'] = date('Y-m-d H:i:s');
            $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($ticketEndDate));
            $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($ticketEndDate));
            $data['checkout_time'] = date("Y-m-d H:i:s", strtotime($ticketEndDate));
          }
          $data['ticket_security_code'] = rand(1000, 9999);
          $data['vp_device_checkin'] = '1';
          $data['partner_id'] = $facility->owner_id;
          $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
          $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
          if(isset($request->CardType)){
            $card_type = '';
            if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
              $card_type = 'VISA';
            }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
              $card_type = 'MASTERCARD';
            }else if(strtolower($request->CardType) == "jcb"){
              $card_type = 'JCB';
            }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
              $card_type = 'AMEX';
            }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
              $card_type = 'DISCOVER';
            }else{
              $card_type = $request->CardType;
            }
            $data['card_type'] = $card_type;
          }

          $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $data['checkin_time']);
          if($data['checkin_time'] != ''){
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $data['checkout_time']);
          }else{
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
          }
          
          $result = Ticket::create($data);
          $paidAmount = '';
          if(isset($request->payment_details)){
              $ticket = Ticket::where('id', $result->id)->first();
              $this->log->info("payment details request --".json_encode($request->payment_details));
              $authorized_anet_transaction=new AuthorizeNetTransaction();           
              $authorized_anet_transaction->sent='1';
              $authorized_anet_transaction->user_id=$ticket->user_id;
              $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
              $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
              $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
              $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
              $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
              $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
              $authorized_anet_transaction->ref_id=isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
              $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
              $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
              $authorized_anet_transaction->method="card";
              $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
              $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
              $authorized_anet_transaction->status_code=isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
              $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
              $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
              $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
              $authorized_anet_transaction->save();
              $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              
              $ticket->anet_transaction_id = $authorized_anet_transaction->id;
              $ticket->total = $request->payment_details['TransactionAmount'];
              $ticket->grand_total = $request->payment_details['TransactionAmount'];
              $ticket->terminal_id = $request->payment_details['TerminalID'];
              $ticket->payment_date = date("Y-m-d H:i:s");
              $ticket->tax_fee = $taxFee;
              $ticket->processing_fee = $processingFee;
              $ticket->parking_amount = $request->payment_details['TransactionAmount'] - ($processingFee + $taxFee);
              $ticket->save();
              $paidAmount = $request->payment_details['TransactionAmount'];
            }
          $this->log->info("driveup checkin done --".json_encode($result));
          
          if($result){

            //check gate api
            if($facility->open_gate_enabled == '1'){  
              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
              if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
              }
            }
            if($paidAmount == ''){
              $msg =  "WELCOME. #$result->ticket_number";
            }else{
              $msg =  "WELCOME CHARGED: $$paidAmount. #$result->ticket_number";
            }
            $data['msg'] = $msg;
            $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
            $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
            $data['booking_type'] = "driveup";
            $data['ticket_id'] = $result->ticket_number;
            $data['amount'] = $paidAmount == '' ? "0.00" : number_format($paidAmount,2);
            $data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
            if($ticket->checkout_time != ''){
              $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);
            }else{
              $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
            }
            

            $length_of_stay = '';
            $diff_in_days = $startDate->diffInDays($endDate);
            $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
            $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);
            if ($diff_in_days > 0) {
              if ($diff_in_days == 1) {
                $length_of_stay .= $diff_in_days . " day ";
              } else {
                $length_of_stay .= $diff_in_days . " days ";
              }
            }
            if ($diff_in_hours > 0) {
              $length_of_stay .= $diff_in_hours . " hr ";
            }
            
            if ($diff_in_minutes > 0) {
              $length_of_stay .= $diff_in_minutes . " min";
            }
            if($ticket->length == ''){
              $ticket->length = $diff_in_hours;
              $ticket->save();
            }
            
            $data['length_of_stay'] = $length_of_stay;
            ////Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
            if($user->phone != ''){
              //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
              $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
              $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
              $url = env('RECEIPT_URL');
              $sms_msg = "Welcome to ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$result->ticket_number;
              $this->customeReplySms($sms_msg, $user->phone);
            }
            return $data;
          }else{
            throw new ApiGenericException('Something wrong.');    
          }
          
        }

        if(isset($gate) && $gate->gate_type == "exit"){
            $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            if(!$ticket){
              
              if(isset($request->payment_details)){

                $data['user_id'] = $user->id;
                $data['checkout_gate'] = $request->gate_id;
                $data['facility_id'] = $request->facility_id;
                $data['is_checkout'] = 1;
                $data['ticket_number'] = $this->checkTicketNumber();
                $data['ticket_security_code'] = rand(1000, 9999);
                $data['partner_id'] = $facility->owner_id;
                //$data['reservation_id'] = $qrCode->reservation_id;
                //$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
                //$data['check_in_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->start_time));
                //$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
                //$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
                //$data['total'] = $qrCode->reservation->total;
                //$data['grand_total'] = $qrCode->reservation->total;
                //$data['length'] = $qrCode->reservation->length;
                //$data['checkin_time'] = date('Y-m-d H:i:s');
                $data['checkout_time'] = date('Y-m-d H:i:s');
                //$data['event_id'] = $eventData->id;
                $data['checkout_without_checkin'] = '1';
                //dd($data);
                $ticket = Ticket::create($data);
                $this->log->info("payment details request --".json_encode($request->payment_details));
                $authorized_anet_transaction=new AuthorizeNetTransaction();           
                $authorized_anet_transaction->sent='1';
                $authorized_anet_transaction->user_id=$user->id;
                $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
                $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
                $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
                $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
                $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
                $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
                $authorized_anet_transaction->ref_id=isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
                $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
                $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
                $authorized_anet_transaction->method="card";
                $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
                $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
                $authorized_anet_transaction->status_code=isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
                $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
                $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
                $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
                $authorized_anet_transaction->save();
                $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              
                $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                $ticket->total = $request->payment_details['TransactionAmount'];
                $ticket->grand_total = $request->payment_details['TransactionAmount'];
                $ticket->terminal_id = $request->payment_details['TerminalID'];
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->processing_fee = $facility->drive_up_processing_fee;
                $ticket->save();
              }
              //$user = $qrCode->reservation->user;

              
              
              //$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
              //$qrCode->save();
              
              /*$facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
              $msg = "Thank you for visiting ".$facilityName.".";
              $this->customeReplySms($msg, $user->phone);
              */
              //check gate api
              if($facility->open_gate_enabled == '1'){  
                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                if($gateStatus == "true"){}else{
                  throw new ApiGenericException($gateStatus);
                }
              }
              $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
              $msg = "THANK YOU FOR VISITING CHARGED: $".number_format($request->payment_details['TransactionAmount'],2)." #$ticket->ticket_number";
              $this->customeReplySms($msg, $user->phone);

              $data = ['msg' => $msg];
              $this->log->info("SMS sent, driveup response sent");
              return $data;

              //  throw new ApiGenericException('No checkin found for this user.');    
                
            }
            /*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/

            $ticketNumber = $ticket->ticket_number;

            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $diff_in_hours = $arrival_time->diffInRealHours($from);
            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
               $diff_in_hours = 2;     
            }
            $ticket->checkout_gate = $request->gate_id;
            $ticket->is_checkout = 1;
            $ticket->length = $diff_in_hours;
            $ticket->checkout_time = date('Y-m-d H:i:s');
            $paidAmount = '';
            if(isset($request->payment_details)){
              $this->log->info("payment details request --".json_encode($request->payment_details));
              $authorized_anet_transaction=new AuthorizeNetTransaction();           
              $authorized_anet_transaction->sent='1';
              $authorized_anet_transaction->user_id=$ticket->user_id;
              $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
              $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
              $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
              $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
              $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
              $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
              $authorized_anet_transaction->ref_id=isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
              $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
              $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
              $authorized_anet_transaction->method="card";
              $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
              $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
              $authorized_anet_transaction->status_code=isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
              $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
              $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
              $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
              $authorized_anet_transaction->save();
              $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();

              if($request->is_overstay == '1'){
                
                $parkingNowTime = date("Y-m-d H:i:s");
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                
                $diff_in_hours =number_format($diff_in_mins/60, 2);
                $todayEvent = Event::find($ticket->event_id);
                $diff_in_hours = $diff_in_hours - $todayEvent->base_event_hours;
                
                
                $overstay = new OverstayTicket();
                $overstay->user_id = $ticket->user_id;
                $overstay->facility_id = $ticket->facility_id;
                //$overstay->length = $this->request->length;
                $overstay->total = $request->payment_details['TransactionAmount'];
                $overstay->ticket_number = $ticket->ticket_number;
                $overstay->is_checkin = '1';
                $overstay->is_checkout = '1';
                $overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($ticket->checkin_time));
                $overstay->checkout_datetime = date('Y-m-d H:i:s');
                $overstay->partner_id = $ticket->partner_id;
                $overstay->ticket_id = $ticket->id;
                $overstay->anet_transaction_id = $authorized_anet_transaction->id;
                $overstay->length = $diff_in_hours;
                $overstay->save();

                $ticket->grand_total = $ticket->grand_total + $request->payment_details['TransactionAmount'];
                $ticket->is_checkout = '1';
                $ticket->is_overstay = '1';
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->save();
              }else{
                $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                $ticket->total = $request->payment_details['TransactionAmount'];
                $ticket->grand_total = $request->payment_details['TransactionAmount'];
                $ticket->terminal_id = $request->payment_details['TerminalID'];
                $paidAmount =  $request->payment_details['TransactionAmount'];
              }
              
            }
            
            if($ticket->vp_device_checkin == '1'){
            $ticket->checkout_datetime = date('Y-m-d H:i:s');
          }
          $ticket->processing_fee = $facility->processing_fee;
          $result = $ticket->save();

          $this->log->info("checkout VP6800 done --".json_encode($ticket));
          if($result){
          //check gate api
          if($facility->open_gate_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }  
          }

          if($request->is_overstay == '1'){
            $response = ["msg" => "Thank you for visiting ".$facilityName.". Extra $". $request->payment_details['TransactionAmount'] ." is charged for overstay."];
          }else{

            if($paidAmount == ''){
              $response = ["msg" => "THANK YOU FOR VISITING. #".$ticketNumber];
            }else{
              $response = ["msg" => "THANK YOU FOR VISITING CHARGED: $".number_format($paidAmount,2)." #".$ticketNumber];
            }
            
          }
          
          $response['is_phone_linked'] = $user->phone != '' ? '1' : '0';
          $response['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
          $response['booking_type'] = "driveup";
          $response['ticket_id'] = $ticketNumber;
          $response['amount'] = $paidAmount == '' ? "0.00" : number_format($paidAmount,2);
          $response['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
          $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
          $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

          $length_of_stay = '';
          $diff_in_days = $startDate->diffInDays($endDate);
          $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
          $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);
          if($diff_in_hours > 0){
            $length_of_stay .= $diff_in_hours."hr ";
          }
          if($diff_in_minutes <= 0){
            $length_of_stay .= "0 min";
          }
          if($diff_in_minutes > 0){
            $length_of_stay .= $diff_in_minutes."min";
          }
          $response['length_of_stay'] = $length_of_stay;


          $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
          $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
          $url = env('RECEIPT_URL');
          $sms_msg = "Thank you for visiting ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticketNumber;
                            
          $this->customeReplySms($sms_msg, $user->phone);
          $this->log->info("SMS sent, driveup response sent");
          return $response;
          }else{
            throw new ApiGenericException('Something wrong.');    
          }
        }
        
    }


  public function ticketCheckinCheckout(Request $request){

    $this->log->info("Request received --".json_encode($request->all()));
    $facility = Facility::find($request->facility_id);
    if(!$facility){
      throw new ApiGenericException('Sorry,Invalid garage.');    
    }
    $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 

    if(!$gate){
      throw new ApiGenericException("The system is not currently available. Please try again later.");
    }

    if(isset($gate) && $gate->active == '0'){
      throw new ApiGenericException('The system is not currently available. Please try again later.');    
    }
       //check gate api
      if($facility->check_vehicle_enabled == '1'){ 
        $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }
      }

      if(isset($gate) && $gate->gate_type == "entry"){
      
        if($facility->facility_booking_type == '1'){
          throw new ApiGenericException('Prepaid booking is not allowed.');    
        }
      $reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
      $userPass = [];
      if(!$reservation){

        $reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
        if($reservation){
          if($reservation->is_ticket == '1'){
            throw new ApiGenericException('You have already checked-in.');    
          }else if($reservation->is_ticket == '2'){
            throw new ApiGenericException('You have already checkout.');    
          }else{

          }
        }
        $userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
        if(!$userPass){
          throw new ApiGenericException('No prepaid booking or pass found.');    
        }
        $ticket = Ticket::where("user_pass_id", $userPass->id)->get();
          if($userPass->total_days == count($ticket)){
            throw new ApiGenericException('No prepaid booking or pass found.');
          }
  
          $reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
        if($reservation){
          if($reservation->is_ticket == '1'){
            throw new ApiGenericException('You have already checked-in.');    
          }else if($reservation->is_ticket == '2'){
            throw new ApiGenericException('You have already checkout.');    
          }else{

          }
          $userPass = [];
          $user = $reservation->user;          
        }else{
          $user = $userPass->user;
        }
        

      }else{
        $user = $reservation->user;          
      }

      $config = Configuration::where('field_name','prepaid-checkin-time')->where("facility_id", $facility->id)->first();
        if(count($config) > 0){
            $prepaidCheckinTime = $config->field_value;
        }else{
            $prepaidCheckinTime = 15;
        }
        $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
        $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
        $reservationEndDate = $reservationstartDate->addHours($reservation->length);
        if(strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)){
            $data['is_check_in_ontime'] = '0';
            $data['booking_number'] = $reservation->ticketech_code;
            $data['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
            $data['booking_type'] = 'reservation';
            $data['msg'] = 'EARLY FOR RESERVATION';
            return $data;
            
        }

        if(strtotime($today) < strtotime($reservation->start_timestamp)){
          $data['is_check_in_ontime'] = '0';
          $data['booking_number'] = $reservation->ticketech_code;
          $data['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
          $data['booking_type'] = 'reservation';
          $data['msg'] = 'EARLY FOR RESERVATION';
          return $data;
        }


        $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if($ticket){
          throw new ApiGenericException('You have already checked-in.');
        }

          $data['user_id'] = $user->id;
          $data['checkin_gate'] = $request->gate_id;
          $data['facility_id'] = $request->facility_id;
          $data['is_checkin'] = 1;
          $data['ticket_number'] = $this->checkTicketNumber();
          $data['checkin_time'] = date('Y-m-d H:i:s');
          $data['ticket_security_code'] = rand(1000, 9999);
          $data['partner_id'] = $facility->owner_id;
        if($reservation){                  
          $data['reservation_id'] = $reservation->id;
          $data['check_in_datetime'] = $reservation->start_timestamp;
          $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
          $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
          $data['total'] = $reservation->total;
          $data['grand_total'] = $reservation->total;
          $data['length'] = $reservation->length;
          $data['user_pass_id'] = $reservation->user_pass_id;
          $data['checkin_time'] = date('Y-m-d H:i:s');
          $data['license_plate'] = $reservation->license_plate;
          $reservation->is_ticket = '1';
          $reservation->save();            
        }

        if(count($userPass) > 0){                  
          $data['check_in_datetime'] = date('Y-m-d H:i:s');
          $data['checkout_datetime'] = date('Y-m-d H:i:s');
          $data['user_pass_id'] = $userPass->id;
          
          $userPass->consume_days = $userPass->consume_days + 1;
          $userPass->remaining_days = $userPass->remaining_days - 1;
          $userPass->save();                              
        }

        $result = Ticket::create($data);
        $facilityName = ucwords($facility->full_name);
        if($reservation){
          Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
        }else{
          Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
          $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
          $this->customeReplySms($msg, $user->phone);
        }

       $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


       //check gate api
       if($facility->open_gate_enabled == '1'){  
        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }
      }

        //$msg =  "Welcome to $facilityName. #$result->ticket_number.";
        $msg =  "WELCOME. #$result->ticket_number";
        $data = ['msg' => $msg];
        $data['booking_number'] = $reservation->ticketech_code;
        $data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
        $data['booking_exit_time'] = date("g:i A", strtotime($result->checkout_datetime));
        $data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));        
        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
        $data['booking_type'] = 'reservation';
        $data['is_check_in_ontime'] = '1';
        return $data;
       
      }else if(isset($gate) && $gate->gate_type == "exit"){
         $reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')/*->where('is_ticket', '0')*/->first();
        if(!$reservation){
          $userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))/*->where('remaining_days','>',0)*/->orderBy('id', 'DESC')->first();
          if(!$userPass){
            throw new ApiGenericException('No prepaid booking or pass found.');    
          }
          $passTicket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
          if(!$passTicket){
            
            if($userPass->remaining_days == 0){
              throw new ApiGenericException('No prepaid booking or pass found.');
            }
            $data['user_id'] = $userPass->user_id;
            $data['facility_id'] = $request->facility_id;
            $data['ticket_number'] = $this->checkTicketNumber();
            //$checkout['checkin_time'] = date('Y-m-d H:i:s');
            $data['ticket_security_code'] = rand(1000, 9999);
            $data['partner_id'] = $facility->owner_id;
            $data['check_in_datetime'] = date("Y-m-d H:i:s");
            $data['checkout_datetime'] = date("Y-m-d H:i:s");
            $data['estimated_checkout'] = date("Y-m-d H:i:s");
            $data['checkout_gate'] = $request->gate_id;
            $data['user_pass_id'] = $userPass->id;
            $data['is_checkout'] = 1;
            $data['checkout_time'] = date("Y-m-d H:i:s");
            $data['checkout_without_checkin'] = '1';
            Ticket::create($data);

            $userPass->consume_days = $userPass->consume_days + 1;
            $userPass->remaining_days = $userPass->remaining_days - 1;
            $userPass->save();                              

          }else{
            $passTicket->is_checkout= '1';
            $passTicket->checkout_gate = $request->gate_id;
            $passTicket->checkout_time = date("Y-m-d H:i:s");
            $passTicket->save();
          }
          $user = $userPass->user;

          /*$ticket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          if(!$ticket){
            throw new ApiGenericException('Sorry, No checkin found against this mobile number.');
          }*/

          $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
          $msg = "Thank you for visiting ".$facilityName.".";
          $this->customeReplySms($msg, $user->phone);

          //check gate api
          //if($facility->open_gate_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }
          //}
          $data = ['msg' => $msg];
          return $data;
        }else{
          
          if($facility->facility_booking_type == '1'){
            throw new ApiGenericException('Prepaid booking is not allowed.');    
          }
          if($reservation->is_ticket == '2'){
             throw new ApiGenericException('No prepaid booking or pass found.');
          }
          $user = $reservation->user;
          $ticket = Ticket::where('reservation_id', $reservation->id)->orderBy("id", "DESC")->first();
          if(!$ticket){
              $data['user_id'] = $user->id;
              $data['checkout_gate'] = $request->gate_id;
              $data['facility_id'] = $request->facility_id;
              //$data['is_checkin'] = 1;
              $data['ticket_number'] = $this->checkTicketNumber();
              //$data['checkin_time'] = date('Y-m-d H:i:s');
              $data['ticket_security_code'] = rand(1000, 9999);
              $data['partner_id'] = $facility->owner_id;
              if($reservation){                  
              $data['reservation_id'] = $reservation->id;
              $data['check_in_datetime'] = $reservation->start_timestamp;
              $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
              $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
              $data['total'] = $reservation->total;
              $data['grand_total'] = $reservation->total;
              $data['length'] = $reservation->length;
              $data['user_pass_id'] = $reservation->user_pass_id;
              $data['is_checkout'] = '1';
              $data['checkout_time'] = date('Y-m-d H:i:s');
              $checkout['checkout_without_checkin'] = '1';

              Ticket::create($data);
              $reservation->is_ticket = '2';
              $reservation->save();
              $facilityName = ucwords($facility->full_name);
              $msg = "Thank you for visiting ".$facilityName.".";

              //check gate api
              //if($facility->open_gate_enabled == '1'){  
                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                if($gateStatus == "true"){}else{
                  throw new ApiGenericException($gateStatus);
                }
              //}
              $this->customeReplySms($msg, $user->phone);
              //return "Success";
              $data = ['msg' => $msg];
              return $data;  
              //return $msg;            
          }            
        }else if($ticket->is_checkout == '0'){
          if($facility->facility_booking_type == '1'){
            throw new ApiGenericException('Prepaid booking is not allowed.');    
          }
            $facilityName = ucwords($facility->full_name);
            $ticket->is_checkout = '1';
            $ticket->checkout_time = date('Y-m-d H:i:s');
            $ticket->checkout_gate = $request->gate_id;
            $ticket->save();

            if($reservation){
              $reservation->is_ticket = '2';
              $reservation->save();
            }
            //$msg = "Thank you for visiting ".$facilityName.".";
            //$this->customeReplySms($msg, $user->phone);
            //check gate api
            if($facility->open_gate_enabled == '1'){
              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
              if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
              }
            }

            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
            $url = env('RECEIPT_URL');
            $sms_msg = "Thank you for visiting ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
                              
            $this->customeReplySms($sms_msg, $user->phone);
            $this->log->info("SMS sent, driveup response sent");
            
            $msg = "THANK YOU FOR VISITING. #".$ticket->ticket_number;
            $data = ['msg' => $msg];
            $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
            $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
            $data['booking_type'] = 'reservation';
            $data['is_check_in_ontime'] = '1';
            return $data;
            //return $msg;
        }else{
          $msg = "No prepaid booking or pass found.";
          $this->customeReplySms($msg, $user->phone);
          throw new ApiGenericException('No prepaid booking or pass found.');    
        }
        return "Success";  
      }/*else{
        throw new ApiGenericException("Invalid request.");
      }*/
      return "Success";
    }
    return "Success";
  }


     // save Declined Trans
     public function saveDeclinedTransaction(Request $request)
     {
       //return $request->session_id;
       $this->log->info("Request received for payment declined --".json_encode($request));
       $facility = Facility::find($request->facility_id);
       if(!$facility){
             throw new ApiGenericException("Invalid garage.");
           }
         $user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
         if(!$user){
               throw new ApiGenericException("User Not Found.");
           }  
           if(isset($request->payment_details)){
                 $user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
                 $this->log->info("Decline payment details request --".json_encode($request->payment_details));
                 $authorized_anet_transaction=new AuthorizeNetTransaction();           
                 $authorized_anet_transaction->sent='1';
                 $authorized_anet_transaction->user_id=$user->id;
                 $authorized_anet_transaction->total= isset($request->payment_details['TransactionAmount']) ? $request->payment_details['TransactionAmount']:'';
                 $authorized_anet_transaction->name= isset($request->payment_details['MerchantName']) ? $request->payment_details['MerchantName'] :'';              
                 $authorized_anet_transaction->description="Payment declined";
                 $authorized_anet_transaction->response_message=isset($request->payment_details['ProcessorMessage']) ? $request->payment_details['ProcessorMessage'] :'';
                 $authorized_anet_transaction->expiration= isset($request->payment_details['expiry']) ? $request->payment_details['expiry'] : '';
                 $authorized_anet_transaction->card_type= isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : '';
                 $authorized_anet_transaction->anet_trans_hash=$request->payment_details['processorReference'];
                 $authorized_anet_transaction->ref_id=$request->payment_details['RequesterTransRefNum'];
                 $authorized_anet_transaction->anet_trans_id= isset($request->payment_details['TransactionID']) ? $request->payment_details['TransactionID']:'';
                 $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
                 $authorized_anet_transaction->method="card";
                 $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
                 $authorized_anet_transaction->status_code=$request->payment_details['StatusCode'];
                 $authorized_anet_transaction->status_type=$request->payment_details['StatusType'];
                 $authorized_anet_transaction->status_message=$request->payment_details['StatusMessage'];
                 $authorized_anet_transaction->name=$request->payment_details['CardHolderName'];
                 $authorized_anet_transaction->save();
                 return "Payment declined data save successfull !";
                 
               }else {
                   throw new ApiGenericException("Payment Details Not Found.");
               }
             $this->log->info("Decline payment transaction done --".json_encode($result));
   
     }



     public function datacapTicketCheckinCheckout(Request $request){

      $facility = Facility::find($request->facility_id);
      if(!$facility){
        throw new ApiGenericException('Sorry,Invalid garage.');    
      }
      $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 
  
      if(!$gate){
        throw new ApiGenericException("The system is not currently available. Please try again later.");
      }
  
      if(isset($gate) && $gate->active == '0'){
        throw new ApiGenericException('The system is not currently available. Please try again later.');    
      }
         //check gate api
        //if($facility->check_vehicle_enabled == '1'){ 
          $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }
        //}
  
        if(isset($gate) && $gate->gate_type == "entry"){
        
          if($facility->facility_booking_type == '1'){
            throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');    
          }
        $reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
        $userPass = [];
        if(!$reservation){
  
          $reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
          if($reservation){
            if($reservation->is_ticket == '1'){
              throw new ApiGenericException('You have already checked-in.');    
            }else if($reservation->is_ticket == '2'){
              throw new ApiGenericException('You have already checkout.');    
            }else{
  
            }
          }
          $userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
          if(!$userPass){
            throw new ApiGenericException('Sorry, No prepaid booking or pass found.');    
          }
          $ticket = Ticket::where("user_pass_id", $userPass->id)->get();
            if($userPass->total_days == count($ticket)){
              throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
            }
    
            $reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
          //$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days','>',0)->orderBy('id', 'DESC')->first();
          //$reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->first();
          if($reservation){
            if($reservation->is_ticket == '1'){
              throw new ApiGenericException('You have already checked-in.');    
            }else if($reservation->is_ticket == '2'){
              throw new ApiGenericException('You have already checkout.');    
            }else{
  
            }
            $userPass = [];
            $user = $reservation->user;          
          }else{
            $user = $userPass->user;
          }
          
  
        }else{
          $user = $reservation->user;          
        }
          $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          if($ticket){
            throw new ApiGenericException('You have already checked-in.');
          }
  
            $data['user_id'] = $user->id;
            $data['checkin_gate'] = $request->gate_id;
            $data['facility_id'] = $request->facility_id;
            $data['is_checkin'] = 1;
            $data['ticket_number'] = $this->checkTicketNumber();
            $data['checkin_time'] = date('Y-m-d H:i:s');
            $data['ticket_security_code'] = rand(1000, 9999);
            $data['partner_id'] = $facility->owner_id;
          if($reservation){                  
            $data['reservation_id'] = $reservation->id;
            $data['check_in_datetime'] = $reservation->start_timestamp;
            $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
            $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
            $data['total'] = $reservation->total;
            $data['grand_total'] = $reservation->total;
            $data['length'] = $reservation->length;
            $data['user_pass_id'] = $reservation->user_pass_id;
            $data['checkin_time'] = date('Y-m-d H:i:s');
            $reservation->is_ticket = '1';
            $reservation->save();            
          }
  
          if(count($userPass) > 0){                  
            $data['check_in_datetime'] = date('Y-m-d H:i:s');
            $data['checkout_datetime'] = date('Y-m-d H:i:s');
            $data['user_pass_id'] = $userPass->id;
            
            $userPass->consume_days = $userPass->consume_days + 1;
            $userPass->remaining_days = $userPass->remaining_days - 1;
            $userPass->save();                              
          }
  
          $result = Ticket::create($data);
          $facilityName = ucwords($facility->full_name);
          if($reservation){
            Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
          }else{
            Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
            $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
            $this->customeReplySms($msg, $user->phone);
          }
  
         $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
  
  
         //check gate api
         //if($facility->open_gate_enabled == '1'){  
          $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }
        //}
  
        $msg =  "Welcome to $facilityName. #$result->ticket_number.";
         $data = ['msg' => $msg];
                return $data;
         
        }else if(isset($gate) && $gate->gate_type == "exit"){
           $reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')/*->where('is_ticket', '0')*/->first();
          if(!$reservation){
            $userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))/*->where('remaining_days','>',0)*/->orderBy('id', 'DESC')->first();
            if(!$userPass){
              throw new ApiGenericException('Sorry, No prepaid booking or pass found.');    
            }
            $passTicket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
            if(!$passTicket){
              
              if($userPass->remaining_days == 0){
                throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
              }
              $data['user_id'] = $userPass->user_id;
              $data['facility_id'] = $request->facility_id;
              $data['ticket_number'] = $this->checkTicketNumber();
              //$checkout['checkin_time'] = date('Y-m-d H:i:s');
              $data['ticket_security_code'] = rand(1000, 9999);
              $data['partner_id'] = $facility->owner_id;
              $data['check_in_datetime'] = date("Y-m-d H:i:s");
              $data['checkout_datetime'] = date("Y-m-d H:i:s");
              $data['estimated_checkout'] = date("Y-m-d H:i:s");
              $data['checkout_gate'] = $request->gate_id;
              $data['user_pass_id'] = $userPass->id;
              $data['is_checkout'] = 1;
              $data['checkout_time'] = date("Y-m-d H:i:s");
              $data['checkout_without_checkin'] = '1';
              Ticket::create($data);
  
              $userPass->consume_days = $userPass->consume_days + 1;
              $userPass->remaining_days = $userPass->remaining_days - 1;
              $userPass->save();                              
  
            }else{
              $passTicket->is_checkout= '1';
              $passTicket->checkout_gate = $request->gate_id;
              $passTicket->checkout_time = date("Y-m-d H:i:s");
              $passTicket->save();
            }
            $user = $userPass->user;
  
            /*$ticket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            if(!$ticket){
              throw new ApiGenericException('Sorry, No checkin found against this mobile number.');
            }*/
  
            $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
            $msg = "Thank you for visiting ".$facilityName.".";
            $this->customeReplySms($msg, $user->phone);
  
            //check gate api
            //if($facility->open_gate_enabled == '1'){  
              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
              if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
              }
            //}
            $data = ['msg' => $msg];
            return $data;
          }else{
            
            if($facility->facility_booking_type == '1'){
              throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');    
            }
  
            if($reservation->is_ticket == '2'){
               throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
            }
            $user = $reservation->user;
            $ticket = Ticket::where('reservation_id', $reservation->id)->first();
            if(!$ticket){
                $data['user_id'] = $user->id;
                $data['checkout_gate'] = $request->gate_id;
                $data['facility_id'] = $request->facility_id;
                //$data['is_checkin'] = 1;
                $data['ticket_number'] = $this->checkTicketNumber();
                //$data['checkin_time'] = date('Y-m-d H:i:s');
                $data['ticket_security_code'] = rand(1000, 9999);
                $data['partner_id'] = $facility->owner_id;
                if($reservation){                  
                $data['reservation_id'] = $reservation->id;
                $data['check_in_datetime'] = $reservation->start_timestamp;
                $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                $data['total'] = $reservation->total;
                $data['grand_total'] = $reservation->total;
                $data['length'] = $reservation->length;
                $data['user_pass_id'] = $reservation->user_pass_id;
                $data['is_checkout'] = '1';
                $data['checkout_time'] = date('Y-m-d H:i:s');
                $checkout['checkout_without_checkin'] = '1';
  
                Ticket::create($data);
                $reservation->is_ticket = '2';
                $reservation->save();
                $facilityName = ucwords($facility->full_name);
                $msg = "Thank you for visiting ".$facilityName.".";
  
                //check gate api
                //if($facility->open_gate_enabled == '1'){  
                  $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                  if($gateStatus == "true"){}else{
                    throw new ApiGenericException($gateStatus);
                  }
                //}
                $this->customeReplySms($msg, $user->phone);
                //return "Success";
                $data = ['msg' => $msg];
                return $data;  
                //return $msg;            
            }            
          }else if($ticket->is_checkout == '0'){
  
            if($facility->facility_booking_type == '1'){
              throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');    
            }
              $facilityName = ucwords($facility->full_name);
              $ticket->is_checkout = '1';
              $ticket->checkout_time = date('Y-m-d H:i:s');
              $ticket->checkout_gate = $request->gate_id;
              $ticket->save();
  
              if($reservation){
                $reservation->is_ticket = '2';
                $reservation->save();
              }
              $msg = "Thank you for visiting ".$facilityName.".";
              $this->customeReplySms($msg, $user->phone);
              //check gate api
              //if($facility->open_gate_enabled == '1'){
                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                if($gateStatus == "true"){}else{
                  throw new ApiGenericException($gateStatus);
                }
              //}
              $data = ['msg' => $msg];
              return $data;
              //return $msg;
          }else{
            $msg = "Sorry, No prepaid booking or pass found.";
            $this->customeReplySms($msg, $user->phone);
            throw new ApiGenericException('Sorry, No prepaid booking or pass found.');    
          }
          return "Success";  
        }/*else{
          throw new ApiGenericException("Invalid request.");
        }*/
        return "Success";
      }
      return "Success";
    }
  
    
    public function datacapTicketDriveupCheckinCheckout(Request $request){
      $this->log->info("Request received --".json_encode($request));
      $facility = Facility::find($request->facility_id);
      if(!$facility){
        throw new ApiGenericException("Invalid garage.");
      }
      $facilityName =  ucwords($facility->full_name);
      $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 
      if(!$gate){
        throw new ApiGenericException("The system is not currently available. Please try again later.");
      }

      if(isset($gate) && $gate->active == '0'){
        throw new ApiGenericException('The system is not currently available. Please try again later.');    
      }

      $userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
      if($userSessionExist){
        $user = User::where('id', $userSessionExist->user_id)->first();  
      }else{
        throw new ApiGenericException('Invalid user.');
      }
      //$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
      if(!$user){
        $user = User::create(
            [
            'name' => '',
            'email' => '',
            'phone' => '',
            'password' => Hash::make(str_random(60)),
            'anon' => true,
            'user_type' => '5',
            'created_by' => $facility->owner_id,
            'session_id' => $request->session_id,
            ]
        );
      }

      $isMember = 0;
      if($user->member_user_id != ''){
        $isMember = 1;
      }
      
      if(isset($gate) && $gate->gate_type == "entry"){

        $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if($ticket){
            throw new ApiGenericException('You have already checked-in.');    
        }

        if($facility->check_vehicle_enabled == '1'){  
        $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }
        }
        $data['user_id'] = $user->id;
        $data['checkin_gate'] = $request->gate_id;
        $data['facility_id'] = $request->facility_id;
        $data['is_checkin'] = 1;
        $data['ticket_number'] = $this->checkTicketNumber();
        $data['checkin_time'] = date('Y-m-d H:i:s');
        $data['check_in_datetime'] = date('Y-m-d H:i:s');
        $data['ticket_security_code'] = rand(1000, 9999);
        $data['vp_device_checkin'] = '1';
        $data['partner_id'] = $facility->owner_id;

        $result = Ticket::create($data);
        $paidAmount = '';
        if(isset($request->payment_details)){
            $ticket = Ticket::where('id', $result->id)->first();
            $this->log->info("payment details request --".json_encode($request->payment_details));
            $authorized_anet_transaction=new AuthorizeNetTransaction();           
            $authorized_anet_transaction->sent='1';
            $authorized_anet_transaction->user_id=$ticket->user_id;
            $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
            $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
            $authorized_anet_transaction->description="VP6800 Drive-Up Payment Done Ticket ID : ".$ticket->id;
            $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
            $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
            $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
              $authorized_anet_transaction->ref_id=isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
              $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
              $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
              $authorized_anet_transaction->method="card";
              $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
              $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
              $authorized_anet_transaction->status_code=isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
              $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
              $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
              $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
            $authorized_anet_transaction->save();
            $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              
            $ticket->anet_transaction_id = $authorized_anet_transaction->id;
            $ticket->total = $request->payment_details['TransactionAmount'];
            $ticket->grand_total = $request->payment_details['TransactionAmount'];
            $ticket->terminal_id = $request->payment_details['TerminalID'];
            $ticket->save();

            $paidAmount = isset($request->payment_details['TransactionAmount']) ? $request->payment_details['TransactionAmount'] : '';
          }
        $this->log->info("New VP6800 checkin done --".json_encode($result));
        
        if($result){

          //check gate api
          if($facility->open_gate_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }
          }
          if($paidAmount == ''){
            $msg =  "Welcome to $facilityName. #$result->ticket_number.";
          }else{
            $msg =  "Welcome to $facilityName $$paidAmount charged. #$result->ticket_number.";
          }
          $data = ['msg' => $msg];
          return $data;
        }else{
          throw new ApiGenericException('Something wrong.');    
        }
        
      }

      if(isset($gate) && $gate->gate_type == "exit"){
          $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          
          
          if(!$ticket){
              
            if(isset($request->payment_details)){

              $data['user_id'] = $user->id;
              $data['checkout_gate'] = $request->gate_id;
              $data['facility_id'] = $request->facility_id;
              $data['is_checkout'] = 1;
              $data['ticket_number'] = $this->checkTicketNumber();
              $data['ticket_security_code'] = rand(1000, 9999);
              $data['partner_id'] = $facility->owner_id;
              //$data['reservation_id'] = $qrCode->reservation_id;
              //$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
              //$data['check_in_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->start_time));
              //$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
              //$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
              //$data['total'] = $qrCode->reservation->total;
              //$data['grand_total'] = $qrCode->reservation->total;
              //$data['length'] = $qrCode->reservation->length;
              //$data['checkin_time'] = date('Y-m-d H:i:s');
              $data['checkout_time'] = date('Y-m-d H:i:s');
              //$data['event_id'] = $eventData->id;
              $data['checkout_without_checkin'] = '1';
              //dd($data);
              $ticket = Ticket::create($data);
              $this->log->info("payment details request --".json_encode($request->payment_details));
              $authorized_anet_transaction=new AuthorizeNetTransaction();           
              $authorized_anet_transaction->sent='1';
              $authorized_anet_transaction->user_id=$user->id;
              $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
              $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
              $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
              $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
              $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
              $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
              $authorized_anet_transaction->ref_id=isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
              $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
              $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
              $authorized_anet_transaction->method="card";
              $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
              $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
              $authorized_anet_transaction->status_code=isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
              $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
              $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
              $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
              $authorized_anet_transaction->save();
              $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              
              $ticket->anet_transaction_id = $authorized_anet_transaction->id;
              $ticket->total = $request->payment_details['TransactionAmount'];
              $ticket->grand_total = $request->payment_details['TransactionAmount'];
              $ticket->terminal_id = $request->payment_details['TerminalID'];
              $ticket->save();
            }
            //$user = $qrCode->reservation->user;

            
            
            //$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
            //$qrCode->save();
            
            $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
            $msg = "Thank you for visiting ".$facilityName." $".$request->payment_details['TransactionAmount']." charged.";
            $this->customeReplySms($msg, $user->phone);

            $data = ['msg' => $msg];
            return $data;

            //  throw new ApiGenericException('No checkin found for this user.');    
              
          }


          
          
                
          if($facility->check_vehicle_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }
          }
          $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
          $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
          $diff_in_hours = $arrival_time->diffInRealHours($from);
          if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
             $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
          }
          $ticket->checkout_gate = $request->gate_id;
          $ticket->is_checkout = 1;
          $ticket->length = $diff_in_hours;
          $ticket->checkout_time = date('Y-m-d H:i:s');
          $paidAmount = '';
          if(isset($request->payment_details)){
            $this->log->info("payment details request --".json_encode($request->payment_details));
            $authorized_anet_transaction=new AuthorizeNetTransaction();           
            $authorized_anet_transaction->sent='1';
            $authorized_anet_transaction->user_id=$ticket->user_id;
            $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
            $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
            $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
            $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
            $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
            $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
              $authorized_anet_transaction->ref_id=isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
              $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
              $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
              $authorized_anet_transaction->method="card";
              $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
              $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
              $authorized_anet_transaction->status_code=isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
              $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
              $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
              $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
            $authorized_anet_transaction->save();
            $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              


            if($request->is_overstay == '1'){
                
              $checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
              $checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time))." 23:59:59";
              $parkingNowTime = date("Y-m-d H:i:s");
              $parkingStartTime = date("Y-m-d")." 00:00:00";
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
              $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
              $diff_in_hours = $arrival_time->diffInRealHours($from);
              $diff_in_mins = $arrival_time->diffInRealMinutes($from);
              
              $diff_in_hours =number_format($diff_in_mins/60, 2);
              
              
              $overstay = new OverstayTicket();
              $overstay->user_id = $ticket->user_id;
              $overstay->facility_id = $ticket->facility_id;
              $overstay->total = $request->payment_details['TransactionAmount'];
              $overstay->ticket_number = $ticket->ticket_number;
              $overstay->is_checkin = '1';
              $overstay->is_checkout = '1';
              $overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($parkingStartTime));
              $overstay->checkout_datetime = date('Y-m-d H:i:s');
              $overstay->partner_id = $ticket->partner_id;
              $overstay->ticket_id = $ticket->id;
              $overstay->anet_transaction_id = $authorized_anet_transaction->id;
              $overstay->length = $diff_in_hours;
              $overstay->save();

              $ticket->grand_total = $ticket->grand_total + $request->payment_details['TransactionAmount'];
              $ticket->is_checkout = '1';
              $ticket->is_overstay = '1';
              $ticket->save();
            }else{
              $ticket->anet_transaction_id = $authorized_anet_transaction->id;
              $ticket->total = $request->payment_details['TransactionAmount'];
              $ticket->grand_total = $request->payment_details['TransactionAmount'];
              $ticket->terminal_id = $request->payment_details['TerminalID'];
              $paidAmount =  $request->payment_details['TransactionAmount'];
            }

            
          }
          
          if($ticket->vp_device_checkin == '1'){
            $ticket->checkout_datetime = date('Y-m-d H:i:s');
          }
          $result = $ticket->save();
          $this->log->info("checkout VP6800 done --".json_encode($ticket));
          if($result){
          //check gate api
          if($facility->open_gate_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }  
          }

          if($request->is_overstay == '1'){
            $response = ["msg" => "Thank you for visiting ".$facilityName.". Extra $". $request->payment_details['TransactionAmount'] ." is charged for overstay."];
          }else{

            if($paidAmount == ''){
              $response = ["msg" => "Thank you for visiting ".$facilityName."."];
            }else{
              $response = ["msg" => "Thank you for visiting ".$facilityName." $$paidAmount charged."];
            }
            
          }
          
          return $response;
          /*$msg =  "Thank you for visiting ".$facilityName.".";
          $data = ['msg' => $msg];
          return $data;*/
              
        }else{
          throw new ApiGenericException('Something wrong.');    
        }
      }
      
  }


  public function datacapTicketSessionCheckinCheckout(Request $request){
    //try{
      $this->setCustomTimezone($request->facility_id);
      $this->log->info("Request received --".json_encode($request->all()));
      $this->log->info("System Not available error test - 1 -");
      $facility = Facility::find($request->facility_id);
      if(!$facility){
        throw new ApiGenericException("Invalid garage.");
      }
      $facilityName =  ucwords($facility->full_name);
      $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 
      if(!$gate){
        throw new ApiGenericException("The system is not currently available. Please try again later.");
      }

      if(isset($gate) && $gate->active == '0'){
        throw new ApiGenericException('The system is not currently available. Please try again later.');    
      }

      $userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
      if($userSessionExist){
        $user = User::where('id', $userSessionExist->user_id)->first();  
      }else{
        //if(!$user){
          $user = User::create(
              [
              'name' => '',
              'email' => '',
              'phone' => '',
              'password' => Hash::make(str_random(60)),
              'anon' => true,
              'user_type' => '5',
              'created_by' => $facility->owner_id,
              'session_id' => $request->session_id,
              ]
          );

          $userSession = new UserSession();
          $userSession->user_id = $user->id;
          $userSession->partner_id = $user->created_by;
          $userSession->session_id = $request->session_id;
          $userSession->save();


        //}
      }
      
      
      $this->log->info("System Not available error test - 2 -");
      $isMember = 0;
      if($user->member_user_id != ''){
        $isMember = 1;
      }
      
      
      if(isset($gate) && $gate->gate_type == "entry"){
       
        $arrival_time = date("Y-m-d H:i:s");
        //$arrival_time = "2023-03-31 08:00:00";
        $length_of_stay = .01;
        //$request->request->add(['arrival_time' => $arrival_time]);
        //$request->request->add(['length_of_stay' => $length_of_stay]);
        //check if garage is closed
        //$rateData = $this->updateRateInformationWithAvailibilty($request, $facility);
        /*$rate = $facility->rateForReservationOnMarker($arrival_time, $length_of_stay, false, false, null, false, false,false,'0');
        if(isset($rate['price'])){
          if($rate['price'] == "N/A"){
            throw new ApiGenericException('Garage is currently closed.');
          }
        }*/

        $existHoursOfOperation=HoursOfOperation::where('facility_id',$facility->id)->get();
        if(count($existHoursOfOperation) > 0){
          $weekday = date('N', strtotime($arrival_time));
          $time = date('H:i:s', strtotime($arrival_time));
          if($weekday>6)
          {
              $weekday=7-$weekday;
          }
          $hoursOfOperation=HoursOfOperation::where('day_of_week',$weekday)->where('facility_id',$facility->id)->get();
  
          if(count($hoursOfOperation)<=0)
          {
            throw new ApiGenericException('Garage is currently closed.');
          }else{
              foreach($hoursOfOperation as $key=>$value){
                  if($value->open_time <= $time && $value->close_time >= $time){

                  }else{
                    throw new ApiGenericException('Garage is currently closed.');
                  }
              }


          }
        }
        $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if($ticket){
            throw new ApiGenericException('You have already checked-in.');    
        }
      $reservation = Reservation::with('user')->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
      $userPass = [];
      if(!$reservation){

       
        if($facility->facility_booking_type == '0'){
          throw new ApiGenericException('Sorry, Drive-Up booking is not allowed.');
        }
        
        $userPass = UserPass::with('user')->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days','>',0)->orderBy('id', 'DESC')->first();
        if(!$userPass){
          if(isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1'){
            throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
          }

          $this->log->info("System Not available error test - 3 -");
          if($facility->is_prepaid_first == '0'){
                        
                  $data['user_id'] = $user->id;
                  $data['checkin_gate'] = $request->gate_id;
                  $data['facility_id'] = $request->facility_id;
                  $data['is_checkin'] = 1;
                  $data['ticket_number'] = $this->checkTicketNumber();
                  $data['checkin_time'] = date('Y-m-d H:i:s');
                  $data['ticket_security_code'] = rand(1000, 9999);
                  $data['partner_id'] = $facility->owner_id;
                  $data['check_in_datetime'] = date('Y-m-d H:i:s');
                  $data['vp_device_checkin'] = '1';
                  
                  $data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
                  $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                  $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                  if(isset($request->CardType)){
                    $card_type = '';
                    if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                      $card_type = 'VISA';
                    }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                      $card_type = 'MASTERCARD';
                    }else if(strtolower($request->CardType) == "jcb"){
                      $card_type = 'JCB';
                    }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                      $card_type = 'AMEX';
                    }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                      $card_type = 'DISCOVER';
                    }else{
                      $card_type = $request->CardType;
                    }
                    $data['card_type'] = $card_type;
                  }
                  $result = Ticket::create($data);
                  
                  $facilityName = ucwords($facility->full_name);

                  $this->log->info("session user checkedin with ticket number {$result->ticket_number}");
                  //check gate api
                  if($facility->open_gate_enabled == '1'){
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if($gateStatus == "true"){}else{
                      throw new ApiGenericException($gateStatus);
                    }
                  }
                  if($user->email != ''){
                    Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
                  }
                  $msg = "Welcome to $facilityName.";
                  if($user->phone != ''){
                    $this->customeReplySms($msg, $user->phone);
                  }
                  
                  $data = ['msg' => $msg];
                  return $data;
              
          }
          $this->log->info("System Not available error test - 4 -");
          $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
          $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
          $diff_in_hours = $arrival_time->diffInRealHours($from);
          if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
            $diff_in_hours = 24;
          }
          
          //$isMember = 0;
          $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0,$isMember);
          if($rate == false){
              throw new ApiGenericException('Garage is not available.');    
          }
          $data = [];
          //$data ['session_id'] = $request->session_id;
          //$data ['price'] = $rate['price']  + $facility->processing_fee;
          $data ['price'] = $rate['price']  + $facility->processing_fee;
          return $data;
        }

        
        $this->log->info("System Not available error test - 5 -");
      }

      if($facility->facility_booking_type == '1'){
        throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');    
      }

        if($facility->check_vehicle_enabled == '1'){  
        $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }
        }
        $data['user_id'] = $user->id;
        $data['checkin_gate'] = $request->gate_id;
        $data['facility_id'] = $request->facility_id;
        $data['is_checkin'] = 1;
        $data['ticket_number'] = $this->checkTicketNumber();
        $data['checkin_time'] = date('Y-m-d H:i:s');
        $data['check_in_datetime'] = date('Y-m-d H:i:s');
        $data['ticket_security_code'] = rand(1000, 9999);
        $data['vp_device_checkin'] = '1';
        $data['partner_id'] = $facility->owner_id;
        $this->log->info("System Not available error test - 6 -");
        if($reservation){                  
          $data['reservation_id'] = $reservation->id;
          $data['check_in_datetime'] = $reservation->start_timestamp;
          $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
          $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
          $data['total'] = $reservation->total;
          $data['grand_total'] = $reservation->total;
          $data['length'] = $reservation->length;
          $data['user_pass_id'] = $reservation->user_pass_id;
          $data['checkin_time'] = date('Y-m-d H:i:s');
          $data['vp_device_checkin'] = '0';
          $reservation->is_ticket = '1';
          $reservation->save();            
        }
        $this->log->info("System Not available error test - 7 -");
        if(count($userPass) > 0){                  
          $data['check_in_datetime'] = date('Y-m-d H:i:s');
          $data['checkout_datetime'] = date('Y-m-d H:i:s');
          $data['user_pass_id'] = $userPass->id;
          $data['vp_device_checkin'] = '0';
          
          $userPass->consume_days = $userPass->consume_days + 1;
          $userPass->remaining_days = $userPass->remaining_days - 1;
          $userPass->save();                              
        }

        $data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
        $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
        $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
        if(isset($request->CardType)){
          $card_type = '';
          if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
            $card_type = 'VISA';
          }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
            $card_type = 'MASTERCARD';
          }else if(strtolower($request->CardType) == "jcb"){
            $card_type = 'JCB';
          }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
            $card_type = 'AMEX';
          }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
            $card_type = 'DISCOVER';
          }else{
            $card_type = $request->CardType;
          }
          $data['card_type'] = $card_type;
        }

        $result = Ticket::create($data);

        $this->log->info("Zoo session checkin done --".json_encode($result));
        $this->log->info("System Not available error test - 8 -");
        if($result){
          $this->log->info("System Not available error test - 21 -");
          $facilityName = ucwords($facility->full_name);
          if($reservation){
            $this->log->info("System Not available error test - 22 -");
            Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
          }else{
            try{
            $this->log->info("System Not available error test - 23 -");
            Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
            $this->log->info("System Not available error test - 26 -");
            $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
            $this->log->info("System Not available error test - 27 -");
            $this->customeReplySms($msg, $user->phone);
            $this->log->info("System Not available error test - 28 -");
            }catch(\Exception $e){
              $this->log->error("System exception -". $e->getMessage());
            }
          }
          $this->log->info("System Not available error test - 24 -");
          $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
          //check gate api
          if($facility->open_gate_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }
          }
          $this->log->info("System Not available error test - 25 -");
          //return "Welcome to $facilityName. #$result->ticket_number.";
          $msg =  "Welcome to $facilityName. #$result->ticket_number.";
          $data = ['msg' => $msg];
          $this->log->info("System Not available error test - 9 -");
          return $data;
        }else{
          throw new ApiGenericException('Something wrong.');    
        }
        
      }
      $this->log->info("System Not available error test - 10 -");
      if(isset($gate) && $gate->gate_type == "exit"){
          $ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          
          if(!$ticket){

            $this->log->info("System Not available error test - 11 -");
            $reservation = Reservation::with('user')->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
            $userPass = [];
            if(!$reservation){
      
              $userPass = UserPass::with('user')->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days','>',0)->orderBy('id', 'DESC')->first();
              if(!$userPass){
                throw new ApiGenericException('Sorry, No check-in ticket found for this credit card');
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                  $diff_in_hours = 24;
                }
                
                //$isMember = 0;
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                if($rate == false){
                    throw new ApiGenericException('Garage is not available.');    
                }
                $data = [];
                //$data ['session_id'] = $request->session_id;
                $data ['price'] = $rate['price']  + $facility->processing_fee;
                return $data;
              }
      
              
              
      
            }
            if($facility->facility_booking_type == '1'){
              throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');    
            }
            $this->log->info("System Not available error test - 12 -");
              $data['user_id'] = $user->id;
              $data['checkout_gate'] = $request->gate_id;
              $data['facility_id'] = $request->facility_id;
              //$data['is_checkin'] = 1;
              $data['ticket_number'] = $this->checkTicketNumber();
              $data['ticket_security_code'] = rand(1000, 9999);
              $data['vp_device_checkin'] = '0';
              $data['partner_id'] = $facility->owner_id;
              $data['is_checkout'] = 1;
              $data['checkout_without_checkin'] = '1';
              $data['checkout_time'] = date('Y-m-d H:i:s');
              if($reservation){                  
                $data['reservation_id'] = $reservation->id;
                $data['check_in_datetime'] = $reservation->start_timestamp;
                $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                $data['total'] = $reservation->total;
                $data['grand_total'] = $reservation->total;
                $data['length'] = $reservation->length;
                $data['user_pass_id'] = $reservation->user_pass_id;
                //$data['checkin_time'] = date('Y-m-d H:i:s');
                $reservation->is_ticket = '1';
                $reservation->save();            
              }
              $this->log->info("System Not available error test - 3 -");
              if(count($userPass) > 0){                  
                $data['check_in_datetime'] = date('Y-m-d H:i:s');
                $data['checkout_datetime'] = date('Y-m-d H:i:s');
                $data['user_pass_id'] = $userPass->id;
                
                $userPass->consume_days = $userPass->consume_days + 1;
                $userPass->remaining_days = $userPass->remaining_days - 1;
                $userPass->save();                              
              }
              $result = Ticket::create($data);
      
              $this->log->info("Zoo session checkin done --".json_encode($result));
              
              if($result){
      
                $facilityName = ucwords($facility->full_name);
                
      
                $this->log->info("direct checkout SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
                //check gate api
                
                //return "Welcome to $facilityName. #$result->ticket_number.";
                $msg =  "Thank you for visiting ".$facilityName.".";
                $this->customeReplySms($msg, $user->phone);
                $data = ['msg' => $msg];
                $this->log->info("System Not available error test - 14 -");
                return $data;
              }else{
                throw new ApiGenericException('Something wrong.');    
              }
            
             
          }

          /*if($facility->facility_booking_type == '1'){
            throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');    
          }*/
          
          if($ticket->anet_transaction_id == ''){
            
            if($ticket->user_pass_id != '' || $ticket->reservation_id != ''){
              $this->log->info("System Not available error test - 15 -");
              $checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
              $checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time))." 23:59:59";
              $parkingNowTime = date("Y-m-d H:i:s");
              $parkingStartTime = date("Y-m-d")." 00:00:00";
              //dd($checkinTime, $checkoutTime, $parkingNowTime, $parkingStartTime);
              if(strtotime($parkingNowTime) > strtotime($checkoutTime)){
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
              $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);                
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                if($diff_in_mins > 0){
                  $diff_in_hours =number_format($diff_in_mins/60, 2);     
                }
                if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                  $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                  $diff_in_hours = number_format($diff_in_mins/60, 2);     
                }
              //  dd($arrival_time, $diff_in_hours);
                /** this function is used to get Availability Information for respective facility **/
              $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, 0, $isMember);
              $data['price'] =  $rate['price']  + $facility->processing_fee;
              $data['is_overstay'] =  '1';
              return $data;
            }

            }else{
              $this->log->info("System Not available error test - 16 -");
              $checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
              $parkingNowTime = date("Y-m-d H:i:s");

              //$checkinTime = "2023-02-07 00:48:54";
              //$parkingNowTime = "2023-02-07 01:06:19";
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinTime);
              $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);                

                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                $diff_in_secs = $arrival_time->diffInSeconds($from);

                //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
                if($diff_in_mins < 60){
                  $diff_in_hours = $diff_in_mins/100;
                }
                if($diff_in_mins > 59){
                  $diff_in_hours = number_format($diff_in_mins/60,2);
                }
                if($diff_in_secs < 60){
                  $diff_in_hours = .01;
                }
                
                
                /*if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                  $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                  $diff_in_hours = number_format($diff_in_mins/60, 2);     
                }*/
                //dd(($diff_in_hours));
                /** this function is used to get Availability Information for respective facility **/
              //  $member_id = 0;
              
              //$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
              $rate = $facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
              if($rate){
                if($rate['price'] == "N/A"){
                  $rate = $facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
                  $result['price'] =  $rate['price'] + $facility->processing_fee;
                  return $result;
                }elseif($rate['price'] == 0 || $rate['price'] == "0.00"){
                  $ticket->is_checkout= '1';
                  $ticket->checkout_gate = $request->gate_id;
                  $ticket->checkout_time = date("Y-m-d H:i:s");
                  $ticket->checkout_datetime = date("Y-m-d H:i:s");
                  $ticket->total= 0.00;
                  $ticket->grand_total= 0.00;
                  $ticket->length= $diff_in_hours;
                  $ticket->save();
                  
                  //check gate api
                  if($facility->open_gate_enabled == '1'){
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if($gateStatus == "true"){}else{
                        throw new ApiGenericException($gateStatus);
                    }
                  }

                  $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
                  $msg = "Thank you for visiting ".$facilityName.".";
                  $this->customeReplySms($msg, $user->phone);
                  $data = ['msg' => $msg];
                  return $data;
                }
              }
              //$result['price'] =  $rate['price']  + $facility->processing_fee;
              $result['price'] =  $rate['price'] + $facility->processing_fee;
              return $result;
            }

          }
          if($ticket->anet_transaction_id != ''){
            $this->log->info("System Not available error test - 17 -");
            $checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
            $checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time))." 23:59:59";
            $parkingNowTime = date("Y-m-d H:i:s");
            $parkingStartTime = date("Y-m-d")." 00:00:00";
            //dd($checkinTime, $checkoutTime, $parkingNowTime, $parkingStartTime);
            if(strtotime($parkingNowTime) > strtotime($checkoutTime)){
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
              $from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);                
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                if($diff_in_mins > 0){
                  $diff_in_hours =number_format($diff_in_mins/60, 2);     
                }
                if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                  $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                  $diff_in_hours = number_format($diff_in_mins/60, 2);     
                }
                //dd($diff_in_hours);
                /** this function is used to get Availability Information for respective facility **/
              $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
              $data['price'] =  $rate['price'] + $facility->processing_fee;
              $data['is_overstay'] =  '1';
              return $data;
            }
            
          }

          if($facility->check_vehicle_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }
          }
          $this->log->info("System Not available error test - 18 -");
          $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
          $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
          $diff_in_hours = $arrival_time->diffInRealHours($from);
          if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
             $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
          }
          $ticket->checkout_gate = $request->gate_id;
          $ticket->is_checkout = 1;
          $ticket->length = $diff_in_hours;
          $ticket->checkout_time = date('Y-m-d H:i:s');
          if(isset($request->payment_details)){
            $this->log->info("System Not available error test - 19 -");
            $this->log->info("payment details request --".json_encode($request->payment_details));
            $authorized_anet_transaction=new AuthorizeNetTransaction();           
            $authorized_anet_transaction->sent='1';
            $authorized_anet_transaction->user_id=$ticket->user_id;
            $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
            $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
            $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
            $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
            $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
            $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
            $authorized_anet_transaction->ref_id=$request->payment_details['processorReference'];
            $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
            $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
            $authorized_anet_transaction->method="card";
            $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
            $authorized_anet_transaction->save();
            $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              
            $ticket->anet_transaction_id = $authorized_anet_transaction->id;
            $ticket->total = $request->payment_details['TransactionAmount'];
            $ticket->grand_total = $request->payment_details['TransactionAmount'];
            $ticket->terminal_id = $request->payment_details['TerminalID'];
          }
          
          $result = $ticket->save();
          $this->log->info("checkout VP6800 done --".json_encode($ticket));
          if($result){
          //check gate api
          if($facility->open_gate_enabled == '1'){  
            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }  
          }
          //return "Thank you for visiting ".$facilityName.".";
          $msg =  "Thank you for visiting ".$facilityName.".";
          $data = ['msg' => $msg];
          $this->log->info("System Not available error test - 20 -");
          return $data;
        }else{
          throw new ApiGenericException('Something wrong.');    
        }
      }

    /*}catch(\Exception $e){
      $this->log->error("error received --".$e->getMessage());  
      throw new ApiGenericException("The system is not currently available. Please try again later.");
    }*/
      
  }
   
	public function parkengageLicensePlateCheckinCheckout(Request $request){
        //try{
            $this->log->info("Request received --".json_encode($request->all()));
            $this->log->info("System Not available error test - 1 -");
            $facility = Facility::find($request->facility_id);
            if(!$facility){
            throw new ApiGenericException("Invalid garage.");
            }
            $this->setCustomTimezone($request->facility_id);
            $partner_id = $facility->owner_id;
            $facilityName =  ucwords($facility->full_name);
            $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first(); 
            if(!$gate){
            throw new ApiGenericException("The system is not currently available. Please try again later.");
            }
    
            if(isset($gate) && $gate->active == '0'){
            throw new ApiGenericException('The system is not currently available. Please try again later.');    
            }
            
            if($request->license_plate == ''){
            throw new ApiGenericException('Invalid license plate. Please try again later.');    
            }
            if(isset($gate) && $gate->gate_type == "entry"){
            //check gate api
              if($facility->check_vehicle_enabled == '1'){   
                $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
                //dd($gateStatus);
                if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
                }
              }
			

            $permitVehicles = PermitVehicle::where("license_plate_number", $request->license_plate)->where("partner_id", $partner_id)->get();
            if(count($permitVehicles) > 0){
                foreach($permitVehicles as $key=>$permitVehicle){
                    $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $request->facility_id)->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
                    if(count($permitRequests) > 0){
                        foreach($permitRequests as $key=>$permitRequest){

                            $permitTicket = PermitTicket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
                            if($permitTicket){
                                throw new ApiGenericException('You have already checked-in.');    
                            }

                            $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                            if(!$mapping){
                                continue;
                            }
                            
                            $facilityName = ucwords($facility->full_name);

                            $data['user_id'] = $permitRequest->user_id;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkPermitTicketNumber();
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['check_in_datetime'] = date('Y-m-d H:i:s');
                            $data['checkout_datetime'] = $permitRequest->desired_end_date;
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['partner_id'] = $facility->owner_id;
                            $data['total'] = $permitRequest->permit_rate;
                            $data['grand_total'] = $permitRequest->permit_rate;
                            $data['permit_request_id'] = $permitRequest->id;
                            $data['license_plate'] = $request->license_plate;
                            $result = PermitTicket::create($data);
                            Artisan::queue('email:touchless-parking-permit-prepaid-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
                            $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                            $this->customeReplySms($msg, $permitRequest->user->phone);
                            //$msg =  "Welcome to $facilityName. #$result->ticket_number.";
                            $msg =  "Welcome";
                            //check gate api
                            if($facility->open_gate_enabled == '1'){  
                              $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                              if($gateStatus == "true"){}else{
                                throw new ApiGenericException($gateStatus);
                              }
                            }
                            $data = [
                              'msg' => $msg,
                              'permit_number' => $permitRequest->tracking_code,
                              'checkin_time' => date("g:i A", strtotime($result->checkin_time)),
                              'booking_type' => "permit",
                            ];
                            return $data;
                        }
                    }
                }
            }
            
            $ticket = Ticket::where("license_plate", $request->license_plate)->where("facility_id", $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
            if($ticket){
                throw new ApiGenericException('You have already checked-in.');    
            }

            
            $reservation = Reservation::with('user')->where("license_plate", $request->license_plate)->where("facility_id", $request->facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
            $userPass = [];
            if(!$reservation){
      
                if($facility->facility_booking_type == '0'){
                  throw new ApiGenericException('Drive-Up booking is not allowed.');
                }
                
                $userPass = UserPass::with('user')->where("license_plate", $request->license_plate)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days','>',0)->orderBy('id', 'DESC')->first();
                if(!$userPass){
                    throw new ApiGenericException('No prepaid booking found against this license plate.');
      
                }
                
              }

              $config = Configuration::where('field_name','prepaid-checkin-time')->where("facility_id", $facility->id)->first();
              if(count($config) > 0){
                  $prepaidCheckinTime = $config->field_value;
              }else{
                  $prepaidCheckinTime = 15;
              }
              $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
              $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
              $reservationEndDate = $reservationstartDate->addHours($reservation->length);
              if(strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)){
                  $data['is_check_in_ontime'] = '0';
                  $data['booking_number'] = $reservation->ticketech_code;
                  $data['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
                  $data['booking_type'] = 'reservation';
                  $data['msg'] = 'EARLY FOR RESERVATION';
                  return $data;
                  
              }
      
              if(strtotime($today) < strtotime($reservation->start_timestamp)){
                $data['is_check_in_ontime'] = '0';
                $data['booking_number'] = $reservation->ticketech_code;
                $data['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
                $data['booking_type'] = 'reservation';
                $data['msg'] = 'EARLY FOR RESERVATION';
                return $data;
              }
      
              
                $user = $reservation->user;
                if($facility->facility_booking_type == '1'){
                    throw new ApiGenericException('Prepaid booking is not allowed.');    
                    }
            
                    $data['license_plate'] = $request->license_plate;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['vp_device_checkin'] = '1';
                    $data['partner_id'] = $facility->owner_id;
                    if($reservation){         
                        $data['user_id'] = $reservation->user_id;         
                        $data['reservation_id'] = $reservation->id;
                        $data['check_in_datetime'] = $reservation->start_timestamp;
                        $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                        $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
                        $data['total'] = $reservation->total;
                        $data['grand_total'] = $reservation->total;
                        $data['length'] = $reservation->length;
                        $data['user_pass_id'] = $reservation->user_pass_id;
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['vp_device_checkin'] = '0';
                        $reservation->is_ticket = '1';
                        $reservation->save();            
                    }
                    
                    $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                    $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                    if(isset($request->CardType)){
                        $card_type = '';
                        if(strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa" ){
                        $card_type = 'VISA';
                        }else if(strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard" ){
                        $card_type = 'MASTERCARD';
                        }else if(strtolower($request->CardType) == "jcb"){
                        $card_type = 'JCB';
                        }else if(strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express" ){
                        $card_type = 'AMEX';
                        }else if(strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover" ){
                        $card_type = 'DISCOVER';
                        }else{
                        $card_type = $request->CardType;
                        }
                        $data['card_type'] = $card_type;
                    }
                    
                    if(count($userPass) > 0){         
                        $data['user_id'] = $userPass->user_id;                  
                        $data['check_in_datetime'] = date('Y-m-d H:i:s');
                        $data['checkout_datetime'] = date('Y-m-d H:i:s');
                        $data['user_pass_id'] = $userPass->id;
                        $data['vp_device_checkin'] = '0';
                        
                        $userPass->consume_days = $userPass->consume_days + 1;
                        $userPass->remaining_days = $userPass->remaining_days - 1;
                        $userPass->save();                              
                    }

                    $data['device_type'] = 'IM30';
                    $result = Ticket::create($data);
            
                    $this->log->info("Townsend checkin done --".json_encode($result));
                    if($result){
                        $facilityName = ucwords($facility->full_name);
                        if($reservation){
                        Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
                        }else{
                        try{
                        Artisan::queue('email:classic-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
                        $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                        }catch(\Exception $e){
                            $this->log->error("System exception -". $e->getMessage());
                        }
                        }
                        $this->log->info("checkin SMS send to user  with ticket number {$result->ticket_number}");
                        //check gate api
                        if($facility->open_gate_enabled == '1'){  
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if($gateStatus == "true"){}else{
                            throw new ApiGenericException($gateStatus);
                        }
                        }
                        $msg =  "WELCOME. #$result->ticket_number.";
                        $data = ['msg' => $msg];

                        $data['booking_number'] = $reservation->ticketech_code;
                        $data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
                        $data['booking_exit_time'] = date("g:i A", strtotime($result->checkout_datetime));
                        $data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));        
                        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        $data['booking_type'] = 'reservation';
                        $data['is_check_in_ontime'] = '1';
                        

                        if($user->phone != ''){
                          //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                          $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                          $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                          $url = env('RECEIPT_URL');
                          $sms_msg = "Welcome to ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$result->ticket_number;
                          $this->customeReplySms($sms_msg, $user->phone);
                        }
                        return $data;
                    }else{
                        throw new ApiGenericException('Something wrong.');    
                    }
                
    
            }
            if(isset($gate) && $gate->gate_type == "exit"){
              //check gate api
              if($facility->check_vehicle_enabled == '1'){   
                $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
                //dd($gateStatus);
                if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
                }
              }
                
                $permitVehicles = PermitVehicle::where("license_plate_number", $request->license_plate)->where("partner_id", $partner_id)->get();
                if(count($permitVehicles) > 0){
                    foreach($permitVehicles as $key=>$permitVehicle){
                        $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $request->facility_id)->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
                        if(count($permitRequests) > 0){
                            foreach($permitRequests as $key=>$permitRequest){
                                $permitTicket = PermitTicket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
                                if(!$permitTicket){
                                    //dd($permitRequest->id);
                                    //throw new ApiGenericException('You have already checked-Out.');    
                                    continue;
                                }

                                $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                                if(!$mapping){
                                    continue;
                                }
                                
                                $facilityName = ucwords($facility->full_name);
                                $permitTicket->is_checkout = '1';
                                $permitTicket->checkout_time = date('Y-m-d H:i:s');
                                $permitTicket->checkout_datetime = date('Y-m-d H:i:s');
                                $permitTicket->checkout_gate = $request->gate_id;
                                $permitTicket->save();
                                
                                //Artisan::queue('customer:checkout-pushnotification',array('user_id'=>$ticket->user_id,'ticket_number'=>$ticket->ticket_number));

                                //$msg = "Thank you for visiting ".$facilityName.". #".$permitTicket->ticket_number;
								$msg = "Thank you for visiting ";
                                //$this->customeReplySms($msg, $user->phone);
                                //check gate api
                                if($facility->open_gate_enabled == '1'){
                                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                                if($gateStatus == "true"){}else{
                                    throw new ApiGenericException($gateStatus);
                                }
                                }
                                $data = ['msg' => $msg,
                                'booking_type' => "permit"
                                ];
                                
                                return $data;
                            }
                        }
                    }
                }
                
                $ticket = Ticket::with('user')->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
                $user = $ticket->user;
                if($ticket){
                 
                    if($ticket->reservation_id == '' && $ticket->user_pass_id == ''){   
                        if($ticket->anet_transaction_id == '' && $ticket->paid_by == ''){
                            throw new ApiGenericException('Sorry, No prepaid booking found against this card.');   
                        }   
                    }
               
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                    $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                }
                $ticket->checkout_gate = $request->gate_id;
                $ticket->is_checkout = 1;
                $ticket->length = $diff_in_hours;
                $ticket->checkout_time = date('Y-m-d H:i:s');
                if(isset($request->payment_details)){
                $this->log->info("payment details request --".json_encode($request->payment_details));
                $authorized_anet_transaction=new AuthorizeNetTransaction();           
                $authorized_anet_transaction->sent='1';
                $authorized_anet_transaction->user_id=$ticket->user_id;
                $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
                $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
                $authorized_anet_transaction->description="Drive-Up Payment Done Ticket ID : ".$ticket->id;
                $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
                $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
                $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
                $authorized_anet_transaction->ref_id=$request->payment_details['processorReference'];
                $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
                $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
                $authorized_anet_transaction->method="card";
                $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
                $authorized_anet_transaction->save();
                $charge=AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first()->toArray();              
                $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                $ticket->total = $request->payment_details['TransactionAmount'];
                $ticket->grand_total = $request->payment_details['TransactionAmount'];
                $ticket->terminal_id = $request->payment_details['TerminalID'];
                }
                
                $result = $ticket->save();
                $this->log->info("checkout VP6800 done --".json_encode($ticket));
                if($result){
                //check gate api
                if($facility->open_gate_enabled == '1'){  
                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                if($gateStatus == "true"){}else{
                    throw new ApiGenericException($gateStatus);
                }  
                }
                //return "Thank you for visiting ".$facilityName.".";
                $msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                $data = ['msg' => $msg];
                $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                $data['booking_type'] = 'reservation';
                $data['is_check_in_ontime'] = '1';

                if($user->phone != ''){
                  $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                  $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                  $url = env('RECEIPT_URL');
                  $sms_msg = "Welcome to ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
                  $this->customeReplySms($sms_msg, $user->phone);
                return $data;
            }else{
                throw new ApiGenericException('Something wrong.');    
            }
            }else{
                throw new ApiGenericException('No checkin found.');    
            }
        }
      }
    
        /*}catch(\Exception $e){
            $this->log->error("error received --".$e->getMessage());  
            throw new ApiGenericException("The system is not currently available. Please try again later.");
        }*/
            
        }
    
	protected function checkPermitTicketNumber(){
		$ticket = 'SC'.rand(100,999).rand(100,999);
		$isExist = PermitTicket::where('ticket_number', $ticket)->first();
		if($isExist){
		  $this->checkPermitTicketNumber();
		}
		return $ticket;
	}


  public function lprCheckinCheckout(Request $request){


    if($request->header('X-ClientSecret') !='')
    { 
      $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
      if(!$secret){
         throw new NotFoundException('Invalid partner.');
      }
    }
    //$this->validate($request, ['image' => 'mimes:jpeg,png,jpg,svg'], ['image.mimes' => 'vehicle image must be a file of type : .jpeg, .png, .jpg' ]);    
      
      $image = $request->file('image');
      if($image != ''){
          $file_extension = $image->getClientOriginalExtension();
          $file_name =  $request->license_plate. '_'.rand(1001, 9999).'.' . $file_extension;
          $destination_path = storage_path("app/license-plate");
          $data['image'] = $file_name;
          if(!$image->move($destination_path, $file_name)) {
              throw new ApiGenericException("Something went wrong while upload image.");
          }
      }
      $data['license_plate'] = $request->license_plate;
      $data['partner_id'] = $secret->partner_id;
      $data['make'] = $request->make;
      $data['model'] = $request->model;
      $data['year'] = $request->year;
      $data['color'] = $request->color;
      $data['facility_id'] = $request->facility_id;
      $data['gate'] = $request->gate_id;
      
      $licensePlate = LicensePlate::create($data);
      
      if($licensePlate){
        $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
            $checkinData = [];
            if ($gate && isset($gate->gate_type)) {
                if ($gate->gate_type == 'entry') {
                    return "License plate successfully updated.";
                }else if ($gate->gate_type == 'exit') {
                    $this->checkoutByLicensePlate($request, $licensePlate, $gate);
                }else{
                    throw new ApiGenericException("Invalid gate.");
                }
            }
        }else{
            throw new ApiGenericException("Something went wrong while upload image.");
        }
  }


  public function checkoutByLicensePlate($request, $licensePlate, $gate){
    //check gate api
    $facility = Facility::find($request->facility_id);
    $partner_id = $request->partner_id;
    if($facility->check_vehicle_enabled == '1'){   
      $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
      if($gateStatus == "true"){}else{
      throw new ApiGenericException($gateStatus);
      }
    }
      
      $permitVehicles = PermitVehicle::where("license_plate_number", $request->license_plate)->where("partner_id", $partner_id)->get();
      if(count($permitVehicles) > 0){
          foreach($permitVehicles as $key=>$permitVehicle){
              $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $request->facility_id)->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
              if(count($permitRequests) > 0){
                  foreach($permitRequests as $key=>$permitRequest){
                      $permitTicket = PermitTicket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
                      if(!$permitTicket){
                          continue;
                      }

                      $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                      if(!$mapping){
                          continue;
                      }
                      
                      $facilityName = ucwords($facility->full_name);
                      $permitTicket->is_checkout = '1';
                      $permitTicket->checkout_time = date('Y-m-d H:i:s');
                      $permitTicket->checkout_datetime = date('Y-m-d H:i:s');
                      $permitTicket->checkout_gate = $request->gate_id;
                      $permitTicket->save();
                      
                      //Artisan::queue('customer:checkout-pushnotification',array('user_id'=>$ticket->user_id,'ticket_number'=>$ticket->ticket_number));

                      //$msg = "Thank you for visiting ".$facilityName.". #".$permitTicket->ticket_number;
      $msg = "Thank you for visiting ";
                      //$this->customeReplySms($msg, $user->phone);
                      //check gate api
                      if($facility->open_gate_enabled == '1'){
                      $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                      if($gateStatus == "true"){}else{
                          throw new ApiGenericException($gateStatus);
                      }
                      }
                      $data = ['msg' => $msg,
                      'booking_type' => "permit"
                      ];
                      
                      return $data;
                  }
              }
          }
      }
      
      $ticket = Ticket::with(['reservation','user'])->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
      
      if(!$ticket){
        throw new ApiGenericException('Sorry, No checkin found against this card.');   
      }else{
      $user = $ticket->user;
      $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
      $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
      $diff_in_hours = $arrival_time->diffInRealHours($from);
      $diff_in_mins = $arrival_time->diffInRealMinutes($from);
      $diff_in_secs = $arrival_time->diffInSeconds($from);

      if ($diff_in_mins < 60) {
          $diff_in_hours = $diff_in_mins / 100;
      }
      if ($diff_in_mins > 59) {
          $diff_in_hours = number_format($diff_in_mins / 60, 2);
      }
      if ($diff_in_secs < 60) {
          $diff_in_hours = .01;
      }

      $request->request->add(['diff_in_hours' => $diff_in_hours]);
      
      if($ticket->reservation_id == ''){   
          if($ticket->anet_transaction_id == '' && $ticket->paid_by == ''){
              throw new ApiGenericException('Sorry, No prepaid booking found against this card.');   
          }elseif($ticket->anet_transaction_id != ''){
            $this->checkoutWithSMS($request, $ticket, $facility, $user);
          }elseif($ticket->paid_by != ''){
            $isMember = 0;
            $rate = $facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            if($rate['price'] > 0 ){
              $rate['price'] = $rate['price'] + $facility->processing_fee + $facility->tax_rate;
            }else{
              $rate['price'] = "0.00";
            }
            
              if($ticket->paid_type == '1' || $ticket->paid_type == 1){
                if($ticket->paid_hour != ''){
                    if($diff_in_hours <= $ticket->paid_hour){
                      $rate['price'] = "0.00";
                    }else{
                      throw new ApiGenericException('Sorry, No prepaid booking found against this card.');   
                    }
                }
              }
              				
                if($ticket->paid_type == '0' || $ticket->paid_type == 0){
                  $updatedPaidAmount = $rate['price'];
                  $rate['price'] = "0";  
                }
            
                if($ticket->paid_type == '2' || $ticket->paid_type == 2){
                  if($rate['price'] <= 0 || $rate['price'] <= 0.00){
                      $rate['price'] = "0";
                  }else{
                    if($rate['price'] <= $ticket->paid_amount){
                      $rate['price'] = "0";
                    }else{
                      throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                    }
                    $updatedPaidAmount = 0;
                  }
                }
                if($ticket->paid_type == '3' || $ticket->paid_type == 3){
                    $percentageAmount = number_format((($rate['price'] * $ticket->paid_percentage)/100),2);
                    if(($ticket->max_validated_amount > 0) && $ticket->max_validated_amount != ''){
                      if($percentageAmount <= $ticket->max_validated_amount){
                        $rate['price'] = number_format($rate['price'] - $percentageAmount,2);
                        $updatedPaidAmount = $percentageAmount;
                      }else{
                          $rate['price'] = number_format($rate['price'] - $ticket->max_validated_amount,2);
                          $updatedPaidAmount = $ticket->max_validated_amount;
                      }
                    }else{
                      $rate['price'] = number_format($rate['price'] - $percentageAmount,2);
                      $updatedPaidAmount = $percentageAmount;
                    }

                    if($rate['price'] > 0 ){
                      throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
                    }
                    
                }
      
              if($rate['price'] == '0' || $rate['price'] == 0){ 
                $ticket->is_checkout= '1';
                $ticket->checkout_gate = $request->gate_id;
                $ticket->checkout_time = date("Y-m-d H:i:s");
                $ticket->checkout_datetime = date("Y-m-d H:i:s");
                $ticket->total= 0.00;
                $ticket->grand_total= 0.00;
                $ticket->length= $diff_in_hours;
                if($updatedPaidAmount > 0){
                  $ticket->paid_amount= $updatedPaidAmount;
                }
                $ticket->save();
                      
                //check gate api
                if($facility->open_gate_enabled == '1'){
                  $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                  if($gateStatus == "true"){}else{
                    throw new ApiGenericException($gateStatus);
                  }
                }
    
                $facilityName = isset($facility->full_name)?ucwords($facility->full_name):'';
                $msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                $url = env('RECEIPT_URL');
                $sms_msg = "Thank you for visiting ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
                              
                $this->customeReplySms($sms_msg, $user->phone);
                $this->log->info("SMS sent, response sent");
                $data = ['msg' => $msg];
                $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                return $data;
              }
      }
      $this->checkoutWithSMS($request, $ticket, $facility, $user);
  }else{
      throw new ApiGenericException('Something wrong.');    
  }
  
}
}
  


  public function checkoutWithSMS($request, $ticket, $facility, $user){
    
    $ticket->checkout_gate = $request->gate_id;
    $ticket->is_checkout = 1;
    $ticket->length = $request->diff_in_hours;
    $ticket->checkout_time = date('Y-m-d H:i:s');      
    
    $result = $ticket->save();

    if(isset($ticket->reservation->is_ticket)){
      $ticket->reservation->is_ticket = '2';
      $ticket->reservation->save();
    }
    
    $facilityName =  ucwords($facility->full_name);

    $this->log->info("checkout LPR done --".json_encode($ticket));
    if($result){
    //check gate api
      if($facility->open_gate_enabled == '1'){
        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
        }  
      }
      //return "Thank you for visiting ".$facilityName.".";
      $msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
      $data = ['msg' => $msg];
      $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
      $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
      $data['booking_type'] = 'reservation';
      $data['is_check_in_ontime'] = '1';

      if($user->phone != ''){
        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
        $url = env('RECEIPT_URL');
        $sms_msg = "Welcome to ".$facilityName. ". Please use the following link to download E-Receipt $url/$name/ticket/".$ticket->ticket_number;
        $this->customeReplySms($sms_msg, $user->phone);
      
      }
      return $data;
    }
  }

  //Save Transaction Data for Report
  protected function saveTransactionData($rate, $mainTicket)
  {
      $rate_id = isset($rate['id']) ? $rate['id']:'';
      $rate_description = isset($rate['description']) ? $rate['description'] :'';
      $rate_amount = $rate['price'];
      $tax_rate = $mainTicket->tax_rate == '' ? 0.00 : $mainTicket->tax_rate;
      
      $ticket = new TransactionData();

      $ticket->user_id = $mainTicket->user_id;
      $ticket->facility_id = $mainTicket->facility_id;
      $ticket->partner_id = $mainTicket->partner_id;
      $ticket->ticket_id = $mainTicket->id;
      $ticket->rate_id = $rate_id;
      $ticket->rate_description = $rate_description;
      $ticket->rate_amount = $rate['price'];
      $ticket->total = $rate['price'];
      $ticket->tax_fee = $tax_rate;
      $ticket->processing_fee = $mainTicket->processing_fee;
      $ticket->discount_amount = $mainTicket->paid_amount;
      $ticket->grand_total = $mainTicket->grand_total;
      $ticket->save();
      return $ticket;
  }

  public function saveAnetTransaction($request)
    {
           
            if(isset($request->payment_details)){
              $facility = Facility::find($request->facility_id);
              $user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
              $this->log->info("Save payment details before checkin  --".json_encode($request->payment_details));
              $authorized_anet_transaction=new AuthorizeNetTransaction();           
              $authorized_anet_transaction->sent='1';
              $authorized_anet_transaction->user_id=$user->id;
              $authorized_anet_transaction->total=$request->payment_details['TransactionAmount'];
              $authorized_anet_transaction->name=$request->payment_details['MerchantName'];              
              $authorized_anet_transaction->description="Payment details  save before checkin";
              $authorized_anet_transaction->response_message=$request->payment_details['ProcessorMessage'];
              $authorized_anet_transaction->expiration=$request->payment_details['expiry'];
              $authorized_anet_transaction->card_type=$request->payment_details['CardType'];
              $authorized_anet_transaction->anet_trans_hash=isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
              $authorized_anet_transaction->ref_id=$request->payment_details['RequesterTransRefNum'];
              $authorized_anet_transaction->anet_trans_id=$request->payment_details['TransactionID'];
              $authorized_anet_transaction->payment_last_four=substr($request->payment_details['MaskedPAN'], -4);
              $authorized_anet_transaction->method="card";
              $authorized_anet_transaction->reader_used=$request->payment_details['ReaderUsed'];
              $authorized_anet_transaction->status_code=  isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
              $authorized_anet_transaction->status_type=isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
              $authorized_anet_transaction->status_message=isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
              $authorized_anet_transaction->name=isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
              $authorized_anet_transaction->save();

             
            }
         $this->log->info("Save payment details before checkin  done  --".json_encode($request->payment_details));
         return $authorized_anet_transaction;
    }

}
	

