<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Crypt;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use App\Models\ParkEngage\Gate;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitTicket;
use App\Models\PermitRequest;
use App\Classes\AbacusWebService;
use Twilio\Exceptions\RestException;
use App\Models\UserPass;
use App\Models\AuthorizeNetTransaction;
use App\Models\UserEventsLog;
use App\Models\OauthClient;
use App\Models\ParkEngage\TicketCitation;
use App\Classes\GetCountryCode;
use App\Exceptions\ApiGenericException;
use App\Http\Helpers\QueryBuilder;

class SmsController extends Controller
{

    protected $log;
    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;
        $this->log = $logFactory->setPath('logs/parkengage/sms')->createLogger('sms');
    }

    public function setCustomTimezone($facility_id){
      $facility = Facility::find($facility_id);
      $secret = OauthClient::where('partner_id', $facility->owner_id)->first();        
      $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
      if($partnerTimezone){
        if($partnerTimezone->timezone != ''){
            config(['app.timezone' => $partnerTimezone->timezone]);
            date_default_timezone_set($partnerTimezone->timezone);
        }
      }
    }
     
    public function commonSendSms(Request $request)
    {

        $this->log->info("Request get:" . json_encode($request->all()));
        try {

            $msg = '';
            $ticket = Ticket::with('user', 'facility','userPass')->where('ticket_number', $request->ticket_number)->first();
            if (!$ticket) {
                throw new ApiGenericException("Ticket number not found.");
            }
            if ($request->phone == '') {
                throw new ApiGenericException("Phone number is required.");
            }

            $url =  env('TOUCHLESS_WEB_URL');
            $pay = '';
            $facility = $ticket->facility;
            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                $join->on('user_facilities.user_id', '=', 'users.id');
                $join->where('user_facilities.facility_id', "=", $facility->id);
            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
            if(isset($getRM->slug)){
                $pay = $getRM->slug;
            }else{
                $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                $pay = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
            }
            

            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
            $facilityName = str_replace('@', "-", $facilityName);
            if($ticket->facility->is_gated_facility == '0'){
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->partner_id)->first();
                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                $url = env('RECEIPT_URL');
                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $ticket->partner_id);
                if($dynamicReceiptUrl){
                    $url = $dynamicReceiptUrl->value;
                }
                $msg = "Thank you for visiting " . $facilityName .  ". Please use the following link to download E-Ticket/extend your stay $url/$name/ticket/" . $ticket->ticket_number;
                if($ticket->user_pass_id > 0){
                    $passRemainingUsage = $ticket->userPass->remaining_days;
                    $msg = $msg. "\nPass Remaining Usage : ".$passRemainingUsage;
                }
            }else{
                if ($ticket->is_checkin == '1' && $ticket->is_checkout == '0' && $ticket->anet_transaction_id == NULL) {
                    $url = env('TOUCHLESS_APP_URL');
                    $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $ticket->partner_id);
                    if($dynamicReceiptUrl){
                        $url = $dynamicReceiptUrl->value;
                    }
                    $grace_period = $ticket->facility->grace_period_minute;
                    $ticket_number = base64_encode($ticket->ticket_number);
                    $msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$pay/$ticket_number";
                } elseif ($ticket->is_checkin == '1' && $ticket->is_checkout == '1') {
    
                    $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                    $url = env('RECEIPT_URL');
                    $name = $pay;
                    $msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                } elseif ($ticket->is_checkin == '1' && $ticket->anet_transaction_id != '') {
                    $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                    $url = env('RECEIPT_URL');
                    $name = $pay;
                    $msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                }
                //  Vijay : 22-02-2024 Zeag ThirdParty Ticket checkout only from Our system : 
                elseif ($ticket->is_checkin == '0' && $ticket->is_checkout == '1') {
    
                    $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                    $url = env('RECEIPT_URL');
                    $name = $pay;
                    $msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                }
            }
            

            if (!empty($msg)) {
                $phone = $request->phone;
                $this->log->info("SMS Phone {$phone}");

                $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
                if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                    $countryCode = "+91";
                } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                    $countryCode = "+1";
                } else {
                    $countryCode = "+1";
                }
                $phone = $countryCode . $phone;

                $status = $this->customeReplySms($msg, $phone);
                if ($status == true) {
                    $this->log->info("SMS Phone {$phone} Success");
                    return "SMS sent successfully";
                } else {
                    $this->log->info("SMS Phone {$phone} Fail");
                    return "SMS not send";
                }
            } else {
                $this->log->info("SMS Send Condition not match");
                /* $errorMessage = "User Phone : {$request->phone} : Error is : {$msg}";
                $data['partner_key'] = "SMS Exception";
                $data['exception'] = "Exception in SMS while sending from Manage checkin checkout screen " . $errorMessage;
                Mail::send('email-exception', $data, function ($message) {
                    $message->to(config('email_exceptions'));
                    $message->subject("Email Exception :- Manage checkin chekout screen");
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                }); */
                return "SMS Send Fail Due to some issue, Please contact admin. ";
            }
        } catch (\Throwable $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");
            $errorMessage = "User Email {$request->phone} : Error is : {$msg}";

            $data['partner_key'] = "SMS Exception";
            $data['exception'] = "Exception in SMS while sending from Manage checkin checkout screen " . $errorMessage;
            Mail::send('email-exception', $data, function ($message) {
                $message->to(config('email_exceptions'));
                $message->subject("Email Exception :- Manage checkin chekout screen");
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            });
        }
    }
 

    public function customeReplySms($msg, $phone)
    {
            
        $accountSid = env('TWILIO_ACCOUNT_SID');
        $authToken  = env('TWILIO_AUTH_TOKEN');
        $client = new Client($accountSid, $authToken);
        try {
            
            $client->messages->create(
                $phone,
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => env('TWILIO_PHONE'),
                    // the body of the text message you'd like to send
                    //'body' => "Fine"
                    'body' => "$msg"
                )
            );
            $this->log->info("Error message : {$msg} sent to $phone");
            return "true";
        } catch (RestException $e) {
            
            //echo "Error: " . $e->getMessage();
            $this->log->error($e->getMessage());
            return false;
        }
    }
  


}
