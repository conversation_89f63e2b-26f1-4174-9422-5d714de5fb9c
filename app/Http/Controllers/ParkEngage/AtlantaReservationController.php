<?php

namespace App\Http\Controllers\ParkEngage;

use App\Http\Controllers\Controller;
use App\Exceptions\NotFoundException;
use App\Models\AuthorizeNetTransaction;
use Auth;
use Exception;
use Response;
use Hash;
use Illuminate\Http\Request;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\TransactionsApplePay as AuthorizeNetApplePay;
use App\Classes\AuthorizeNet\Cim;
use App\Models\Rate;
use App\Models\User;
use App\Models\Reservation;
use App\Models\Facility;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotFound;
use App\Exceptions\UserNotAuthorized;
use App\Exceptions\UserWithEmailExistsException;
use Carbon\Carbon;
use Log;
use Artisan;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\LicensePlateNumber;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\MemberUser;
use App\Models\UserPass;
use App\Models\Ticket;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Storage;
use App\Models\ParkEngage\BrandSetting;
use URL;
use App\Services\Pdf;
use App\Services\Image;
//use App\Models\ParkEngage\UserSession;


class AtlantaReservationController extends Controller
{

    protected $log;

    protected $user;

    protected $partnerPaymentDetails;

    protected $countryCode;

    protected $facility;

    protected $request;

    protected $authNet;
    protected $authNetApple;

    protected $cim;

    protected $paymentProfileId;

    protected $validationRules;

    protected $sendAnet = false;
    protected $anonymousAnet = false;

    const DEFAULT_CONFIRM_VAL = 1;
    const APPLE_PAY_FLAG = 1;
    const ADD_EXTRA_DAY_COUNT = 1;

    const DEFAULT_CANCEL_VAL = 2;

    const TWENTY_FOUR_HOURS = 23;
    const DEFAULT_HOURS = 0;

    const REALTIME_WINDOW = 2;
    const END_TIME_EXTRA_MINUTES = 30;
    const DEFAULT_VALUE = 0;
    const RESERVATION_THRESHOLD_TYPE = 2;
    const QUEUE_NAME = 'iqp-inventory';
    const UPDATE_INVENTORY_TRUE = 1;
    const UPDATE_INVENTORY_FALSE = 0;
    const RESERVATION_TYPE = 'PARKENGAGE';
    const AUTH_LOGIN_ID = '92YXdf5TFA';
    const AUTH_TRANSACTION_ID = '22z48X6XHDr8Znjg';

    public function __construct(Request $request)
    {
        $this->request = $request;

        $this->log = (new LoggerFactory)->setPath('logs/parkengage')->createLogger('atlanta-booking');
    }


    public function checkBookingBeforePayment(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
            if (!$checkFacility) {
                throw new NotFoundException('No garage found with this partner.');
            }
            if ($checkFacility->active != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->is_available != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->facility_booking_type == '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if (!$checkFacility->accept_cc) {
                throw new ApiGenericException("Garage does not accept credit cards");
            }
            /*
            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
            if($geoLocation['geoplugin_countryCode'] == 'IN'){
                  $this->countryCode = "+91";
            }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                  $this->countryCode = "+1";
            }else{
                  $this->countryCode = "+1";
            }
			*/
            $this->countryCode = "+1";
            $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
            $this->user = $existPhone;
            if ($existPhone) {

                if ($this->request->is_pass_purchase == '1' && $this->request->is_book_parking == '0' && $this->request->is_booking_also == '0') {
                } else {
                    Reservation::where("user_id", $this->user->id)->whereNull("anet_transaction_id")->delete();
                    UserPass::where("user_id", $this->user->id)->whereNull("anet_transaction_id")->delete();
                    $isResExist = Reservation::where("user_id", $this->user->id)->whereDate("start_timestamp", '=', date("Y-m-d", strtotime($this->request->arrival)))->whereNull("cancelled_at")->orderBy('id', 'DESC')->first();
                    //return $this->user->id;
                    if ($isResExist) {

                        $tickets = Ticket::where('reservation_id', $isResExist->id)->first();
                        if (!$tickets) {

                            $day = date('d', strtotime($this->request->arrival));
                            $monthYear = date('F, Y', strtotime($this->request->arrival));
                            $number = (string) $day;
                            $last_digit = substr($number, -1);
                            $second_last_digit = substr($number, -2, 1);
                            $suffix = 'th';
                            if ($second_last_digit != '1') {
                                switch ($last_digit) {
                                    case '1':
                                        $suffix = 'st';
                                        break;
                                    case '2':
                                        $suffix = 'nd';
                                        break;
                                    case '3':
                                        $suffix = 'rd';
                                        break;
                                    default:
                                        break;
                                }
                            }
                            if ((string) $number === '1') $suffix = 'st';
                            throw new ApiGenericException('You already have a booking for ' . $number . $suffix . ' ' . $monthYear . '.');
                        } else {
                            if ($tickets->is_checkout == '0') {
                                throw new ApiGenericException('You already have check-in against today booking.');
                            }
                        }
                    }
                }


                return "success";
            } else {
                return "success";
            }
        } else {
            throw new NotFoundException('No partner found.');
        }
    }

    /*
    public function planetPaymentSession(Request $request){
    
        $facility = Facility::find($request->facility_id);
		
        if(!$facility){
            throw new ApiGenericException('Invalid garage.');
        }
		$brand_setting = BrandSetting::where('user_id', $facility->owner_id)->first();
        
		$merchant_script_data_1 = '';
        $price = 0;
        if($request->rate_id != ''){
            $rate = Rate::find($request->rate_id);
            if(!$rate){
                throw new ApiGenericException('Rate does not match in our database.');
            }
            $tax_rate = (float) $facility->processing_fee;
            //$price = $rate->price;
			if($request->is_booking_also=='1'){
				$merchant_script_data_1 = "Reserve Parking for ".date("F d, Y", strtotime($request->arrival));
			}else if($request->is_booking_also=='2'){
				$merchant_script_data_1 = $rate->description." Booking";
			}else if($request->is_booking_also=='3'){
				$merchant_script_data_1 = $rate->description." Pass & Reserve Parking for ".date("F d, Y", strtotime($request->arrival));
			}
        }else{
            $tax_rate = (float) $facility->processing_fee;
            //$price = $request->amount;
        }
		//$merchant_script_data_2 = URL::asset('assets/media/images/logo.png');		
		$merchant_script_data_2 = URL::asset('assets/media/images/logo.8ed76367.svg');		
		$merchant_script_data_3 = env('APP_URL').'/brand-settings-logo/'.$brand_setting->id;
        $amount = $request->amount + $tax_rate;
         
        $security_emerchant_id = "ParkEngageTest";
        $security_validation_code = "ParkEngageTest1!";
       // $template_id = "ParkEngage_002.xml";
		$template_id = "ParkEngage_004.xml";
        //$userDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        //$paymentUrl = $userDetails->payment_url;
        $paymentUrl = "https://staging-zooatlanta.parkengage.com/paymentResponse";
        $cent = $amount * 100;
        $reference = rand(1000, 9999).rand(1000, 9999).rand(1000, 9999);
        $params = ['security_emerchant_id'=>"$security_emerchant_id",
        'security_validation_code'=>"$security_validation_code",
        'template_id'=>"$template_id",
        'trx_amount_currency_code'=>"USD",
        'trx_amount_value'=>"$cent",
        'trx_options'=>"G",
        'posturl_success'=> "$paymentUrl",
        'posturl_failure'=>"$paymentUrl",
        'redirect_approved'=>"$paymentUrl",
        'redirect_declined'=>"$paymentUrl",
        'trx_merchant_reference'=>"$reference",
        'service_action'=>"pay",
		'merchant_script_data_1'=> "$merchant_script_data_1",
		'merchant_script_data_2'=> "$merchant_script_data_2",
		'merchant_script_data_3'=> "$merchant_script_data_3",
    ];
	
    $url = "https://web2payuat.3cint.com/ipage/Service/_2019_09_v1_3_0/IPGService/Initialise";
    $ch = curl_init($url);
    
    $headers = array(
        "Content-Type: application/json"
     );
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
        curl_setopt($ch, CURLOPT_POST, 1);
        if ($params !== null) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }
    $response = curl_exec($ch);
    $result['success'] = true;
    $explode = explode(",",json_decode($response,TRUE));    
    $ipgSession = explode(":",$explode[0]);    
    $new = ltrim($ipgSession[1], '"');
    $new = rtrim($new, '"');
    
    if($new == 'null'){
        throw new ApiGenericException('There is some issue with payment gateway.');
    }
    
    $charid = strtoupper(md5(uniqid(rand(), true)));
    $hyphen = chr(45);
    $uuid = chr(123)
      .substr($charid, 0, 8).$hyphen
      .substr($charid, 8, 4).$hyphen
      .substr($charid,12, 4).$hyphen
      .substr($charid,16, 4).$hyphen
      .substr($charid,20,12)
      .chr(125);
     $data['ipgSession'] =  $new;
     $data['guid'] =  $uuid;
     $data['api_url'] =  "https://web2payuat.3cint.com/iPage/Service/_2006_05_v1_0_1/service.aspx";
     $data['tax_rate'] =  $tax_rate;
     $data['amount'] =  $amount;
     $data['merchant_script_data_1'] =  $merchant_script_data_1;
     $data['merchant_script_data_2'] =  $merchant_script_data_2;
     $data['merchant_script_data_3'] =  $merchant_script_data_3;
    
    return $data;
    
    }
	*/

    public function postSuccess(Request $request)
    {
        $this->log->info("AAA Payment Success call Back :" . json_encode($request->all()));
        $this->request = $request;
        if ($this->request->TokenNo == '' || $this->request->AuthorisationCode == '') {
            throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
        }

        if ($this->request->arrival != '') {
            if (strtotime(date("Y-m-d", strtotime($this->request->arrival))) < strtotime(date("Y-m-d"))) {
                //	throw new ApiGenericException('Parking Date can not be a past date');       
            }
        }

        $reservation = Reservation::with('transaction')->where("mer_reference", $this->request->ref)->first();
        $pass = UserPass::where("mer_reference", $this->request->ref)->first();

        if (isset($pass) && !empty($pass)) {
            $this->log->info("AAA Pass Entry:" . json_encode($request->all()));
            $user = User::where('id', $pass['user_id'])->first();
            $amount = $this->request->Amount;
            $amount = (float) $amount;

            if ($amount > 0) {
                // Charge successful, save transaction relationship to it						
                if ($this->request->TokenNo != '') {
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $user->id;
                    $authorized_anet_transaction->total = $pass->total;
                    $authorized_anet_transaction->description = "AAA Booking Payment Done User : " . $user->id;
                    $authorized_anet_transaction->card_type = $this->request->CardType;
                    $authorized_anet_transaction->ref_id = $this->request->ref;
                    $authorized_anet_transaction->anet_trans_id = $this->request->TxID;
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->payment_last_four = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
                    $authorized_anet_transaction->auth_code = isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
                    $authorized_anet_transaction->ip_address = \Request::ip();
                    $authorized_anet_transaction->save();
                    $pass->anet_transaction_id = $authorized_anet_transaction->id;
                    $pass->partner_id = $user->user_type == '3' ? $user->id : $user->created_by;
                    $pass->save();
                    $user->session_id = $this->request->TokenNo;
                    $user->save();
                    /*
                    $userSession = UserSession::where("session_id", $this->request->TokenNo)->where('partner_id', $user->created_by)->first();
                    if(!$userSession){
                        $userSession = new UserSession();           
                        $userSession->user_id = $user->id;
                        $userSession->partner_id = $user->created_by;
                        $userSession->session_id = $this->request->TokenNo;
                        $userSession->save();
						$this->log->info("AAA Payment POST Success call Back :".json_encode($request->all()));
		            }else{
						$this->log->error("AAA Payment POST Fail call Back :".json_encode($request->all()));
		            }
					*/

                    $this->log->info("Pass Log by ujjwal");
                    // Email Send Code Start
                    $pass->emailPassToUser();
                    $this->log->info("AAA Pass email Sent");
                }

                if (isset($reservation) && !empty($reservation)) {
                    $clientData = OauthClient::where('partner_id', $reservation->partner_id)->first();

                    $this->log->info("Email Log by ujjwal inside pass");
                    // Email Send Code Start

                    if ($user->phone != '') {
                        $reservation->emailReservationToPartnerUser($clientData->secret);
                        $this->log->info("AAA Reservation email Sent with SMS inside pass");
                        if ($reservation->is_daily == '0') {
                            $startLabel = "Enter After";
                            $endLabel = "Exit Before";
                        } else {
                            $startLabel = "Start Date";
                            $endLabel = "End Date";
                        }
                        //send sms to user
                        try {
                            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
                            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
                            $client = new Client($accountSid, $authToken);
                            try {
                                // Use the client to do fun stuff like send text messages!
                                $client->messages->create(
                                    // the number you'd like to send the message to
                                    $user->phone,
                                    array(
                                        // A Twilio phone number you purchased at twilio.com/console
                                        'from' => env('TWILIO_PHONE'),
                                        // the body of the text message you'd like to send
                                        //'body' => "Fine"
                                        'body' =>
                                        "Thank you for booking your parking with Zoo Atlanta.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date \n$endLabel : $reservation->formatted_start_date \nAmount Charged : $$reservation->total"
                                    )
                                );
                            } catch (RestException $e) {
                            }
                        } catch (RestException $e) {
                        }
                    } else {
                        $reservation->emailReservationToPartnerUser($clientData->secret);
                        $this->log->info("AAA Reservation email Sent inside pass");
                    }
                    // Email Send Code End				

                }
            }
        } else if (isset($reservation) && !empty($reservation)) {
            $this->log->info("AAA Reservation Entry:" . json_encode($request->all()));
            $user = User::where('id', $reservation['user_id'])->first();
            $clientData = OauthClient::where('partner_id', $reservation->partner_id)->first();
            $amount = $this->request->Amount;
            $amount = (float) $amount;

            if ($amount > 0) {
                if ($this->request->TokenNo != '') {
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $user->id;
                    $authorized_anet_transaction->total = $reservation->total;
                    $authorized_anet_transaction->description = "AAA Booking Payment Done User : " . $user->id;
                    $authorized_anet_transaction->card_type = $this->request->CardType;
                    $authorized_anet_transaction->ref_id = $this->request->ref;
                    $authorized_anet_transaction->anet_trans_id = $this->request->TxID;
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->payment_last_four = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
                    $authorized_anet_transaction->auth_code = isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
                    $authorized_anet_transaction->ip_address = \Request::ip();
                    $authorized_anet_transaction->save();
                    $reservation->anet_transaction_id = $authorized_anet_transaction->id;
                    $reservation->save();
                    $user->session_id = $this->request->TokenNo;
                    $user->save();
                    /*
                    $userSession = UserSession::where("session_id", $this->request->TokenNo)->where('partner_id', $user->created_by)->first();
                    if(!$userSession){
                        $userSession = new UserSession();           
                        $userSession->user_id = $user->id;
                        $userSession->partner_id = $user->created_by;
                        $userSession->session_id = $this->request->TokenNo;
                        $userSession->save();
						$this->log->info("AAA Payment POST Success call Back :".json_encode($request->all()));
		            }else{
						$this->log->error("AAA Payment POST Fail call Back :".json_encode($request->all()));
		            }
					*/

                    $this->log->info("Email Log by ujjwal");
                    // Email Send Code Start

                    if ($user->phone != '') {
                        $reservation->emailReservationToPartnerUser($clientData->secret);
                        $this->log->info("AAA Reservation email Sent with SMS");
                        if ($reservation->is_daily == '0') {
                            $startLabel = "Enter After";
                            $endLabel = "Exit Before";
                        } else {
                            $startLabel = "Start Date";
                            $endLabel = "End Date";
                        }
                        //send sms to user
                        try {
                            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
                            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
                            $client = new Client($accountSid, $authToken);
                            try {
                                // Use the client to do fun stuff like send text messages!
                                $client->messages->create(
                                    // the number you'd like to send the message to
                                    $user->phone,
                                    array(
                                        // A Twilio phone number you purchased at twilio.com/console
                                        'from' => env('TWILIO_PHONE'),
                                        // the body of the text message you'd like to send
                                        //'body' => "Fine"
                                        'body' =>
                                        "Thank you for booking your parking with Zoo Atlanta.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date \n$endLabel : $reservation->formatted_start_date \nAmount Charged : $$reservation->total"
                                    )
                                );
                            } catch (RestException $e) {
                            }
                        } catch (RestException $e) {
                        }
                    } else {
                        $reservation->emailReservationToPartnerUser($clientData->secret);
                        $this->log->info("AAA Reservation email Sent");
                    }
                    // Email Send Code End
                }
            }
        }
        return 1;
    }

    public function postFail(Request $request)
    {
        $this->log->info("AAA Payment Fail call Back :" . json_encode($request->all()));
        $details = Reservation::where('mer_reference', $this->request->ref)->delete();
        $userPass = UserPass::where('mer_reference', $this->request->ref)->delete();
        return 1;
    }

    protected function checkReferenceNumber()
    {
        $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);;
        $resRefrenceExist = Reservation::where("mer_reference", $reference)->first();
        if ($resRefrenceExist) {
            $this->checkReferenceNumber();
        } else {
            $passRefrenceExist = UserPass::where("mer_reference", $reference)->first();
            if ($passRefrenceExist) {
                $this->checkReferenceNumber();
            }
        }
        return $reference;
    }


    public function planetPaymentSession(Request $request)
    {

        $facility = Facility::find($request->facility_id);
        if (!$facility) {
            throw new ApiGenericException('Invalid garage.');
        }
        $brand_setting = BrandSetting::where('user_id', $facility->owner_id)->first();

        $merchant_script_data_1 = '';
        $price = 0;
        if ($request->rate_id != '') {
            $rate = Rate::find($request->rate_id);

            if (!$rate) {
                throw new ApiGenericException('Rate does not match in our database.');
            }

            //$tax_rate = (float) $facility->processing_fee;
            $price = $rate->price;
        } else {
            //$tax_rate = (float) $facility->processing_fee;
            $price = $request->amount;
        }
        $tax_rate = (float) $facility->processing_fee;
        $request->request->add(['amount' => $price]);
        $request->request->add(['tax_rate' => $tax_rate]);
        if ($request->is_booking_also == '1') {
            $merchant_script_data_1 = "Reserve Parking for " . date("F d, Y", strtotime($request->arrival));
        } else if ($request->is_booking_also == '2') {
            $merchant_script_data_1 = $rate->description . " Booking";
        } else if ($request->is_booking_also == '3') {
            $merchant_script_data_1 = $rate->description . " Pass & Reserve Parking for " . date("F d, Y", strtotime($request->arrival));
        }

        $merchant_script_data_2 = URL::asset('assets/media/images/logo.8ed76367.svg');
        //	$merchant_script_data_3 = config('parkengage.APP_URL').'/brand-settings-logo/'.$brand_setting->id;
        $merchant_script_data_3 = 'https://api.parkengage.com/brand-settings-logo/5';

        $amount = $request->amount + $tax_rate;

        $amount = number_format($amount, 2);

        $posturl_success = config('parkengage.AAA_PLANET_POST_SUCCESS_URL');
        $posturl_fail = config('parkengage.AAA_PLANET_POST_FAIL_URL');
        $payment_success = config('parkengage.AAA_PLANET_SUCCESS_URL');
        $payment_fail = config('parkengage.AAA_PLANET_FAIL_URL');

        /*$security_emerchant_id = "ParkEngageTest";
        $security_validation_code = "ParkEngageTest1!";
        $template_id = "ParkEngage_004.xml";*/

        // $security_emerchant_id = "AtlantaFultonCountyRe";
        // $security_validation_code = "4tl4nt4fult.C0untyR3!";
        // // $template_id = "ParkEngage_002.xml";
        // $template_id = "ParkEngage_004.xml";

        $security_emerchant_id = config('parkengage.AAA_PLANET_MERCHANT_ID');
        $security_validation_code = config('parkengage.AAA_PLANET_VALIDATION_CODE');
        $template_id = config('parkengage.AAA_PLANET_TEMPLATE_ID');
        

        $cent = $amount * 100;
        $reference = $this->checkReferenceNumber();
        $params = [
            'security_emerchant_id' => "$security_emerchant_id",
            'security_validation_code' => "$security_validation_code",
            'template_id' => "$template_id",
            'trx_amount_currency_code' => "USD",
            'trx_amount_value' => "$cent",
            'trx_options' => "G",
            'posturl_success' => "$posturl_success",
            'posturl_failure' => "$posturl_fail",
            'redirect_approved' => "$payment_success",
            'redirect_declined' => "$payment_fail",
            'trx_merchant_reference' => "$reference",
            'service_action' => "pay",
            'merchant_script_data_1' => "$merchant_script_data_1",
            'merchant_script_data_2' => "$merchant_script_data_2",
            'merchant_script_data_3' => "$merchant_script_data_3",
        ];

        $url = config('parkengage.AAA_PLANET_SESSION_URL');
        $ch = curl_init($url);

        $headers = array(
            "Content-Type: application/json"
        );
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        curl_setopt($ch, CURLOPT_POST, 1);
        if ($params !== null) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }
        $response = curl_exec($ch);
        //dd($response);
        $result['success'] = true;
        $explode = explode(",", json_decode($response, TRUE));
        $ipgSession = explode(":", $explode[0]);
        if (!isset($ipgSession[1])) {
            //		throw new ApiGenericException('There is some issue with payment gateway.');
        }
        $new = ltrim($ipgSession[1], '"');
        $new = rtrim($new, '"');

        if ($new == 'null') {
            throw new ApiGenericException('There is some issue with payment gateway.');
        }

        $charid = strtoupper(md5(uniqid(rand(), true)));
        $hyphen = chr(45);
        $uuid = chr(123)
            . substr($charid, 0, 8) . $hyphen
            . substr($charid, 8, 4) . $hyphen
            . substr($charid, 12, 4) . $hyphen
            . substr($charid, 16, 4) . $hyphen
            . substr($charid, 20, 12)
            . chr(125);
        $data['ipgSession'] =  $new;
        $data['guid'] =  $uuid;
        $data['api_url'] =  config('parkengage.AAA_PLANET_PAYMENT_URL');
        $data['tax_rate'] =  $tax_rate;
        $data['amount'] =  (float) $amount;
        $data['reference'] =  $reference;
        $data['merchant_script_data_1'] =  $merchant_script_data_1;
        $data['merchant_script_data_2'] =  $merchant_script_data_2;
        $data['merchant_script_data_3'] =  $merchant_script_data_3;

        return $data;
    }

    public function makeAnonReservation(Request $request)
    {
        $this->log->info("Booking about to start1" . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
            if (!$checkFacility) {
                throw new NotFoundException('No garage found with this partner.');
            }
            if ($checkFacility->active != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->is_available != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->facility_booking_type == '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if (!$checkFacility->accept_cc) {
                throw new ApiGenericException("Garage does not accept credit cards");
            }
        }

        if ($this->request->is_pass_purchase == '1' &&  $this->request->is_book_parking == '1') {
        } else {
            /*
            if($this->request->TokenNo == '' || $this->request->AuthorisationCode == ''){
                throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
            }
			*/
        }
        /*if($this->request->arrival == ''){
            throw new ApiGenericException("Event start date can not empty.");
        }*/

        if ($this->request->phone != '') {
            /*
            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
            if($geoLocation['geoplugin_countryCode'] == 'IN'){
                  $this->countryCode = "+91";
            }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                  $this->countryCode = "+1";
            }else{
                  $this->countryCode = "+1";
            }
			*/
            $this->countryCode = "+1";
            //$existPhone = User::where('session_id', $this->request->TokenNo)->where('created_by', $secret->partner_id)->first();
            // return $existPhone;

            if (isset($this->request->TokenNo) && !empty($this->request->TokenNo)) {
                /*
				$userSession = UserSession::where("session_id", $this->request->TokenNo)->where('partner_id', $secret->partner_id)->first();
                if($userSession){
                    $existPhone = User::where('id', $userSession->user_id)->first();
                }else{
                    $existPhone = User::where('phone', $this->countryCode.$this->request->phone)->where('created_by', $secret->partner_id)->first();
                }
				*/
                $existPhone = User::where('session_id', $this->request->TokenNo)->where('created_by', $secret->partner_id)->first();
            } else {
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
            }

            if ($existPhone) {
                if ($this->request->email != '') {
                    $existPhone->email = $this->request->email;
                }
                $existPhone->name = $this->request->name;
                $existPhone->phone = $this->countryCode . $this->request->phone;
                //$existPhone->type_id = $this->request->type_id;
                $existPhone->save();
                $this->user = $existPhone;
            } else {

                $this->user = User::create(
                    [
                        'name' => $this->request->name,
                        'email' => $this->request->email,
                        'phone' => $this->countryCode . $this->request->phone,
                        'password' => Hash::make(str_random(60)),
                        'anon' => false,
                        'user_type' => '5',
                        'created_by' => $secret->partner_id,
                        //'type_id' => $this->request->type_id
                    ]
                );
            }
        } else {
            throw new ApiGenericException("Please enter valid phone number.");
        }

        $is_member = '0';
        if ($request->header('X-ClientSecret') != '') {

            if ($this->request->member_id != '') {
                $is_member = '1';

                // Test if string contains the word 
                $search = '511';
                $mystring = $this->request->member_id;
                if (preg_match("/{$search}/i", $mystring)) {
                } else {
                    throw new ApiGenericException('Invalid member user.');
                }
                $member = MemberUser::where('member_id', $this->request->member_id)->first();

                if ($member) {
                    $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                    // return $memberUser;
                    if ($memberUser) {
                        throw new ApiGenericException('Member Id already associate with other user.');
                    }
                }
                //$this->user->member_user_id = $memberUser->id;
                //$this->user->save();
            }

            if ($this->request->is_book_parking == '0' && $this->request->is_booking_also == '0' && $this->request->pass_id == '' && $this->request->is_pass_purchase == '1') {
            } else {
                $isResExist = Reservation::where("user_id", $this->user->id)->whereDate("start_timestamp", '=', date("Y-m-d", strtotime($this->request->arrival)))->whereNull("cancelled_at")->orderBy('id', 'DESC')->first();
                //$isResExist = Reservation::where("mer_reference", $this->request->MerchantRef)->whereDate("start_timestamp", '=', date("Y-m-d", strtotime($this->request->arrival)))->whereNull("cancelled_at")->orderBy('id', 'DESC')->first();
                if ($isResExist) {
                    if ($this->request->is_pass_purchase == '1' && $this->request->is_book_parking == '0' && $this->request->is_booking_also == '0') {
                        $tickets = Ticket::where('reservation_id', $isResExist->id)->first();
                        if (!$tickets) {

                            $day = date('d', strtotime($this->request->arrival));
                            $monthYear = date('F, Y', strtotime($this->request->arrival));
                            $number = (string) $day;
                            $last_digit = substr($number, -1);
                            $second_last_digit = substr($number, -2, 1);
                            $suffix = 'th';
                            if ($second_last_digit != '1') {
                                switch ($last_digit) {
                                    case '1':
                                        $suffix = 'st';
                                        break;
                                    case '2':
                                        $suffix = 'nd';
                                        break;
                                    case '3':
                                        $suffix = 'rd';
                                        break;
                                    default:
                                        break;
                                }
                            }
                            if ((string) $number === '1') $suffix = 'st';
                            throw new ApiGenericException('You already have a booking for ' . $number . $suffix . ' ' . $monthYear . '.');
                        } else {
                            if ($tickets->is_checkout == '0') {
                                throw new ApiGenericException('You already have check-in against today booking.');
                            }
                        }
                    }
                }
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }

        if ($this->request->arrival != '') {
            if (strtotime(date("Y-m-d", strtotime($this->request->arrival))) < strtotime(date("Y-m-d"))) {
                throw new ApiGenericException('Parking Date can not be a past date');
            }
        }

        if ($this->request->is_pass_purchase == '1') {
            if ($this->request->pass_id != '') {
                $details = $this->getExistingPassDetails($this->request->pass_id);
            } else {
                $details = $this->makePassPayment();
            }

            $reservation = [];
            if ($this->request->is_book_parking == '1' || $this->request->is_booking_also == '1') {

                $reservation = $this->makePassReservation();

                //save zoo atlanta member user id 
                //$this->user->member_user_id = $member_user_id;
                $this->user->is_member = $is_member;
                $this->user->save();

                if ($this->request->member_id != '') {
                    $is_member = '1';

                    $search = '511';
                    $mystring = $this->request->member_id;
                    if (preg_match("/{$search}/i", $mystring)) {
                    } else {
                        throw new ApiGenericException('Invalid member user.');
                    }
                    $member = MemberUser::where('member_id', $this->request->member_id)->first();
                    if ($member) {
                        $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                        if ($memberUser) {
                            throw new ApiGenericException('Member Id already associate with other user.');
                        }
                    } else {
                        $mUser['member_id'] = $this->request->member_id;
                        $member = MemberUser::create($mUser);
                    }
                    $this->user->member_user_id = $member->id;
                    $this->user->save();
                }
                if ($reservation['reservation']) {
                    $res = Reservation::find($reservation['reservation']->id);
                    $res->user_pass_id = $details->id;
                    $res->save();

                    $details->consume_days = $details->consume_days + 1;
                    $details->remaining_days = $details->remaining_days - 1;
                    $details->save();

                    if ($this->request->pass_id != '') {
                        $res->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));
                    }
                    $reservation = $reservation['reservation']->toArray();
                }
                if ($this->request->TxID != '') {
                    //$details->emailPassToUser();
                }

                /*
                $details->consume_days = $details->consume_days + 1;
                $details->remaining_days = $details->remaining_days - 1;
                $details->save();
				*/
                /*
				if($reservation['reservation']){
                    $res = Reservation::find($reservation['reservation']->id);
                    $res->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    
				}
				*/
                return [
                    'pass' => $details->withRelations(),
                    'reservation' => $reservation
                ];
            } else {
                $this->user->is_member = $is_member;
                $this->user->save();

                if ($this->request->member_id != '') {
                    $is_member = '1';

                    $search = '511';
                    $mystring = $this->request->member_id;
                    if (preg_match("/{$search}/i", $mystring)) {
                    } else {
                        throw new ApiGenericException('Invalid member user.');
                    }
                    $member = MemberUser::where('member_id', $this->request->member_id)->first();
                    if ($member) {
                        $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                        if ($memberUser) {
                            throw new ApiGenericException('Member Id already associate with other user.');
                        }
                    } else {
                        $mUser['member_id'] = $this->request->member_id;
                        $member = MemberUser::create($mUser);
                    }
                    $this->user->member_user_id = $member->id;
                    $this->user->save();
                }
            }

            return [
                'pass' => $details->withRelations(),
                'reservation' => $reservation
            ];
        } else {
            $this->log->info("Booking about to start");
            $details = $this->makeReservation();
            //return $details;
            //save zoo atlanta member user id 
            $this->user->is_member = $is_member;
            $this->user->save();

            if ($this->request->member_id != '') {
                $is_member = '1';

                $search = '511';
                $mystring = $this->request->member_id;
                if (preg_match("/{$search}/i", $mystring)) {
                } else {
                    throw new ApiGenericException('Invalid member user.');
                }
                $member = MemberUser::where('member_id', $this->request->member_id)->first();
                if ($member) {
                    $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                    if ($memberUser) {
                        throw new ApiGenericException('Member Id already associate with other user.');
                    }
                } else {
                    $mUser['member_id'] = $this->request->member_id;
                    $member = MemberUser::create($mUser);
                }
                $this->user->member_user_id = $member->id;
                $this->user->save();
            }
            $reservation_id = $details['reservation']->ticketech_code;

            if (isset($promocode) && $promocode) {
                $this->updatePromocodeChanges($request, $promocode, $reservation_id, $this->user); // Update If Promocode Used
            }

            // Return reservation and charge details to caller
            return [
                'reservation' => $details['reservation']->toArray(),
                //'ref_id' => $details['charge']['ref_id']
            ];
        }
    }

    public function getExistingPassDetails($pass_id)
    {

        $pass = UserPass::find($pass_id);
        if (!$pass) {
            throw new ApiGenericException('Invalid Pass Id.');
        }

        if (strtotime($pass->start_date) > strtotime($this->request->arrival)) {
            throw new ApiGenericException('Selected pass will be valid from ' . date('d\t\h F, Y', strtotime($pass->start_date)));
        } else if (strtotime($pass->end_date) < strtotime($this->request->arrival)) {

            throw new ApiGenericException('Selected pass already expired on ' . date('d\t\h F, Y', strtotime($pass->end_date)));
        } else if ($pass->remaining_days <= 0) {

            throw new ApiGenericException('You have already consumed you pass.');
        } else {

            return $pass;
        }
    }


    protected function makePassPayment()
    {

        // Validate the reservation details here
        $this->log->info("Pass about to purchase.");

        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        //$sentRate = $this->request->pass_total;

        $passRate = Rate::with(['rateType', 'category'])->where('id', $this->request->pass_rate_id)->where('rate_type_id', '7')->where('active', '1')->first();
        if (!$passRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset pass rate options and try again.',
                422,
                ['database_rate' => $passRate->price]
            );
        }
        $pass_rate_id = $passRate->id;
        $pass_rate = $passRate->price;


        if (isset($this->request->facility_id)) {
            $this->facility = Facility::find($this->request->facility_id);

            //for processing fee
            if ($this->facility->processing_fee > 0) {

                $pass_rate = $pass_rate + (float)$this->facility->processing_fee;
            }
        }        //check if the total amount is 0




        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $pass = $this->savePass($passRate);

        // Mobile reservation overstay text comes from mobile end.
        $passMode = "Zoo Atlanta Pass Purchase";

        //check if the total amount is 0
        if ($pass_rate > 0) {
            // Charge successful, save transaction relationship to it

            if ($this->request->AuthorisationCode != '') {
                /* 
                $authorized_anet_transaction=new AuthorizeNetTransaction();           
                $authorized_anet_transaction->sent='1';
                $authorized_anet_transaction->user_id=$this->user->id;
                $authorized_anet_transaction->total=$this->request->total;
                $authorized_anet_transaction->description="Mapco Booking Payment Done User : ".$this->user->id;
                $authorized_anet_transaction->card_type=$this->request->CardType;
                $authorized_anet_transaction->ref_id=$this->request->MerchantRef;
                $authorized_anet_transaction->anet_trans_id=$this->request->TxID;
                $authorized_anet_transaction->method="card";
                $authorized_anet_transaction->payment_last_four=isset($this->request->CardNumberLast4) ? $this->request->CardNumberLast4 : '0';
                $authorized_anet_transaction->auth_code=isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
                $authorized_anet_transaction->ip_address=\Request::ip();
                $authorized_anet_transaction->save();
                $pass->anet_transaction_id = $authorized_anet_transaction->id;
                if($this->request->header('X-ClientSecret') !=''){
                    $pass->partner_id = $this->user->user_type == '3'? $this->user->id : $this->user->created_by;
                }
                $pass->save();
                $this->user->session_id = $this->request->TokenNo;
                $this->user->save();
                */
                /*
                $userSession = UserSession::where("session_id", $this->request->TokenNo)->where('partner_id', $this->user->created_by)->first();
                if(!$userSession){
                    $userSession = new UserSession();           
                    $userSession->user_id = $this->user->id;
                    $userSession->partner_id = $this->user->created_by;
                    $userSession->session_id = $this->request->TokenNo;
                    $userSession->save();
                }
				*/
            }
        }


        if ($this->request->is_book_parking == '0' && $this->request->is_booking_also == '0' && $this->request->is_pass_purchase == '1') {
            //$pass->emailPassToUser();
        }
        $this->log->info("Pass created : " . $pass->id);
        return $pass;
        /*return [
            'charge' => $charge,
            'pass' => $pass->withRelations()
        ];*/
    }

    protected function checkPassCode()
    {
        $ticket = 'PA' . rand(100, 999) . rand(100, 999);
        $isExist = UserPass::where('pass_code', $ticket)->first();
        if ($isExist) {
            $this->checkPassCode();
        }
        return $ticket;
    }

    protected function savePass($rate)
    {

        $pass = new UserPass(
            [
                'user_id' => $this->user->id,
                'email' => $this->request->email,
                'phone' => $this->request->phone,
                'purchased_on' => date("Y-m-d H:i:s"),
                'start_date' => date("Y-m-d"),
                'pass_code' => $this->checkPassCode(),
                'partner_id' => $this->user->created_by,
                'facility_id' => $this->request->facility_id,
            ]
        );
        if ($rate->category->no_of_days > 0) {
            $endDate = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $rate->category->no_of_days . ' days'));
            $pass->end_date = $endDate;
        }
        $pass->total_days = $rate->total_usage;
        $pass->remaining_days = $rate->total_usage;
        $pass->consume_days = 0;
        $pass->rate_id = $rate->id;


        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {

            $pass->processing_fee = $this->facility->processing_fee;
            $pass->total = $rate->price + (float)$this->facility->processing_fee;
        } else {
            $pass->total = $rate->price;
        }
        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {

            $pass->processing_fee = $this->facility->processing_fee;
            $pass->total = $rate->price + (float)$this->facility->processing_fee;
        } else {
            $pass->total = $rate->price;
        }

        // If this is higher the apply bonus will get double applied

        $pass->mer_reference = isset($this->request->MerchantRef) ? $this->request->MerchantRef : '';
        $pass->save();

        $userDetails  = User::where('id', $this->user->id)->first();
        $userDetails->is_member = '1';
        $userDetails->save();


        return $pass;
    }



    /**
     * Given a user, make a reservation for that user.
     * TODO: This functionality should probably be drawn out into a ReservationService
     *
     * @param  User $user [description]
     * @return [type]       [description]
     */
    protected function makeReservation($car_req = '')
    {
        // Validate the reservation details here
        //return 'ujjwal';
        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);
        // return 'test';
        $this->facility = Facility::find($this->request->facility_id);

        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;


        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        //if($this->request->is_rate_dynamic == '1'){
        $isMember = $this->request->member_id != '' ? 1 : 0;
        $rate = $this->facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, $useBonus, false, false, true, $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE, $isMember);

        $length = $this->request->length;
        if ($length == 24) {
            $length = 23.59;
        }

        if (!$rate) {
            $this->log->info("No rate found in database for this reservation.");
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }
        /*}else{
            $rate = [];
        }*/

        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        $sentRate = $this->request->total;



        //for Processing Fee
        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {
            $sentRate = ($sentRate - $this->facility->processing_fee);
        }


        //add use bonus if used

        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }

        if (filter_var($rate['price'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $sentRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset reservation options and try again.',
                422,
                ['sent_rate' => $this->request->total, 'database_rate' => $rate['price']]
            );
        }



        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->saveReservation($rate, $useBonus);
        //return $reservation;

        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Zoo Atlanta Reservation";

        // loyalty programs check for reservations
        //        removed second condtion discuced with nirbhay- if ($this->user->is_loyalty && $this->user->is_loyalty_active == LoyaltyProgram::CONST_ONE) {

        // if ($this->user->is_loyalty) {
        //     $reservation->loyalty_program = LoyaltyProgram::LP_ALLOW;
        // }

        //save warning message flag and values 

        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        //check if the total amount is 0
        if ($this->request->total > 0) {

            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                try {
                    // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                    $ticketech_guid = rand(100000, 999999);
                } catch (TicketechException $e) {
                    // If ticketech returns an invalid response, void our transaction and pass the error on to the user
                    $reservation->delete();
                    throw $e;
                }
            }
            // Send reservation to parkonect
            else if ($this->facility->parkonect_id) {
                try {
                    // $ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card); 
                    $ticketech_guid = rand(100000, 999999);
                } catch (ParkonectException $e) {
                    // If parkonect returns an invalid response, void our transaction and pass the error on to the user
                    $reservation->delete();
                    throw $e;
                }
            }


            if ($this->request->AuthorisationCode != '') {
                /*
                $authorized_anet_transaction=new AuthorizeNetTransaction();           
                $authorized_anet_transaction->sent='1';
                $authorized_anet_transaction->user_id=$this->user->id;
                $authorized_anet_transaction->total=$this->request->total;
                $authorized_anet_transaction->description="Mapco Booking Payment Done User : ".$this->user->id;
                $authorized_anet_transaction->card_type=$this->request->CardType;
                $authorized_anet_transaction->ref_id=$this->request->MerchantRef;
                $authorized_anet_transaction->anet_trans_id=$this->request->TxID;
                $authorized_anet_transaction->method="card";
                $authorized_anet_transaction->payment_last_four=isset($this->request->CardNumberLast4) ? $this->request->CardNumberLast4 : '0';
                $authorized_anet_transaction->auth_code=isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
                $authorized_anet_transaction->ip_address=\Request::ip();
                $authorized_anet_transaction->save();
                $reservation->anet_transaction_id = $authorized_anet_transaction->id;  
                $reservation->save();    
        
                $this->user->session_id = $this->request->TokenNo;
                $this->user->save();
                */
                /*
                $userSession = UserSession::where("session_id", $this->request->TokenNo)->where('partner_id', $this->user->created_by)->first();
                if(!$userSession){
                    $userSession = new UserSession();           
                    $userSession->user_id = $this->user->id;
                    $userSession->partner_id = $this->user->created_by;
                    $userSession->session_id = $this->request->TokenNo;
                    $userSession->save();
                }
				*/
            }

            // Charge successful, save transaction relationship to it
            $reservation->ticketech_guid = rand(100000, 999999);
            $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;

            if ($this->request->week != '') {
                $weekDays = 7 * $this->request->week;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }
            if ($this->request->month != '') {
                $weekDays = date('t') * $this->request->month;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }

            if ($this->request->member_id != '') {
                $reservation->is_daily = '1';
            }

            if ($this->request->no_of_visitor != '') {
                $reservation->no_of_visitor = $this->request->no_of_visitor;
            }

            /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/

            $reservation->save();
        } else {

            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                try {
                    // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                    $ticketech_guid = rand(100000, 999999);
                } catch (TicketechException $e) {
                    $reservation->delete();
                    throw $e;
                }
            }
            // Send reservation to parkonect
            else if ($this->facility->parkonect_id) {
                try {
                    //$ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card);
                    $ticketech_guid = rand(100000, 999999);
                } catch (ParkonectException $e) {
                    $reservation->delete();
                    throw $e;
                }
            }

            // Charge successful, save transaction relationship to it
            $authorized_anet_transaction = new AuthorizeNetTransaction();

            $authorized_anet_transaction->sent = $this->sendAnet;
            $authorized_anet_transaction->anonymous = $this->anonymousAnet;
            $authorized_anet_transaction->user_id = $this->user->id;
            $authorized_anet_transaction->ip_address = \Request::ip();
            $authorized_anet_transaction->total = 0;
            $authorized_anet_transaction->name = $this->getBillingName();

            if ($this->facility->parkonect_id) {
                $authorized_anet_transaction->description = "Reservation {$reservation->id}, Parkonect Code {$reservation->ticketech_code}";
            } else {
                $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";
            }

            if (
                isset($this->request->is_loyalty_redeemed)
                && $this->request->is_loyalty_redeemed
            ) {
                $authorized_anet_transaction->response_message = "Zero amount loyalty transaction";
            } else {
                $authorized_anet_transaction->response_message = "Zero amount transaction";
            }
            $authorized_anet_transaction->save();

            $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

            $reservation->anet_transaction_id = $authorized_anet_transaction->id;
            $reservation->ticketech_guid = $ticketech_guid;

            if ($this->request->week != '') {
                $weekDays = 7 * $this->request->week;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }
            if ($this->request->month != '') {
                $weekDays = date('t') * $this->request->month;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }

            if ($this->request->member_id != '') {
                $reservation->is_daily = '1';
            }

            /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/

            if ($this->request->header('X-ClientSecret') != '') {
                $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            if ($this->request->no_of_visitor != '') {
                $reservation->no_of_visitor = $this->request->no_of_visitor;
            }


            /*if($this->request->license_plate_number != ''){
                $reservation->license_plate_number = $this->request->license_plate_number;
                $reservation->make_model = $this->request->make_model;
            }*/

            $reservation->save();
        }


        // Send email to user
        if ($this->request->header('X-ClientSecret') != '') {
            /*
			if($this->request->phone != ''){
            //    $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    

                if($reservation->is_daily == '0'){
                    $startLabel = "Enter After";
                    $endLabel = "Exit Before";
                }else{
                    $startLabel = "Start Date";
                    $endLabel = "End Date";    
                }
                //send sms to user
                    $accountSid = env('TWILIO_ACCOUNT_SID');
                    $authToken  = env('TWILIO_AUTH_TOKEN');
                    $client = new Client($accountSid, $authToken);
                    try
                    {
                        // Use the client to do fun stuff like send text messages!
                        $client->messages->create(
                        // the number you'd like to send the message to
                            $this->countryCode.$this->request->phone,
                       array(
                             // A Twilio phone number you purchased at twilio.com/console
                             'from' => env('TWILIO_PHONE'),
                             // the body of the text message you'd like to send
                             //'body' => "Fine"
                             'body' => 
                "Thank you for booking your parking with Zoo Atlanta.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date \n$endLabel : $reservation->formatted_start_date \nAmount Charged : $$reservation->total"
                         )
                     );
                    }catch (RestException $e)
                    {
                        //throw new ApiGenericException($e->getMessage());
                        //return "success";
                    }
            }else{
              //  $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    
            }
            */
        } else {
            //  $reservation->emailReservationToUser();
        }
        $this->log->info("Booking successfull : " . $reservation->id);
        return [
            //'charge' => $charge,
            'reservation' => $reservation->withRelations(),
            'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : ''
        ];
    }

    protected function makePassReservation()
    {
        $this->log->info("Pass reservation about to start.");
        // Validate the reservation details here
        $useBonus = (bool) $this->request->use_bonus;
        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);

        $this->facility = Facility::find($this->request->facility_id);

        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        //if($this->request->is_rate_dynamic == '1'){
        $isMember = $this->request->member_id != '' ? 1 : 0;
        $rate = $this->facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, $useBonus, false, false, true, $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        if (!$rate) {
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }
        /*}else{
            $rate = [];
        }*/

        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        $sentRate = $this->request->total;

        $useBonus = 0;
        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->savePassReservation($rate, $useBonus);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Zoo Atlanta Reservation";

        //save warning message flag and values 

        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        // Send reservation to ticketech
        if ($this->facility->ticketech_id) {
            try {
                // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                $ticketech_guid = rand(100000, 999999);
            } catch (TicketechException $e) {
                $reservation->delete();
                throw $e;
            }
        }
        // Send reservation to parkonect
        else if ($this->facility->parkonect_id) {
            try {
                //$ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card);
                $ticketech_guid = rand(100000, 999999);
            } catch (ParkonectException $e) {
                $reservation->delete();
                throw $e;
            }
        }

        // Charge successful, save transaction relationship to it
        $authorized_anet_transaction = new AuthorizeNetTransaction();

        $authorized_anet_transaction->sent = $this->sendAnet;
        $authorized_anet_transaction->anonymous = $this->anonymousAnet;
        $authorized_anet_transaction->user_id = $this->user->id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = 0;
        $authorized_anet_transaction->name = $this->getBillingName();

        if ($this->facility->parkonect_id) {
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Parkonect Code {$reservation->ticketech_code}";
        } else {
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";
        }

        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
        ) {
            $authorized_anet_transaction->response_message = "Zero amount loyalty transaction";
        } else {
            $authorized_anet_transaction->response_message = "Zero amount transaction";
        }
        $authorized_anet_transaction->save();

        $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

        $reservation->anet_transaction_id = $authorized_anet_transaction->id;
        $reservation->ticketech_guid = $ticketech_guid;

        if ($this->request->week != '') {
            $weekDays = 7 * $this->request->week;
            $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
            $reservation->end_timestamp = $endTimestamp;
            $reservation->length = $weekDays * 24;
            $reservation->days = $weekDays;
        }
        if ($this->request->month != '') {
            $weekDays = date('t') * $this->request->month;
            $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
            $reservation->end_timestamp = $endTimestamp;
            $reservation->length = $weekDays * 24;
            $reservation->days = $weekDays;
        }

        if ($this->request->member_id != '') {
            $reservation->is_daily = '1';
        }

        /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/

        if ($this->request->header('X-ClientSecret') != '') {
            $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
        }

        if ($this->request->no_of_visitor != '') {
            $reservation->no_of_visitor = $this->request->no_of_visitor;
        }

        $reservation->mer_reference = isset($this->request->MerchantRef) ? $this->request->MerchantRef : '';
        $reservation->save();


        //    $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    

        if ($reservation->is_daily == '0') {
            $startLabel = "Enter After";
            $endLabel = "Exit Before";
        } else {
            $startLabel = "Start Date";
            $endLabel = "End Date";
        }
        /*
                //send sms to user
                    $accountSid = env('TWILIO_ACCOUNT_SID');
                    $authToken  = env('TWILIO_AUTH_TOKEN');
                    $client = new Client($accountSid, $authToken);
                    try
                    {
                        // Use the client to do fun stuff like send text messages!
                        $client->messages->create(
                        // the number you'd like to send the message to
                            $this->countryCode.$this->request->phone,
                       array(
                             // A Twilio phone number you purchased at twilio.com/console
                             'from' => env('TWILIO_PHONE'),
                             // the body of the text message you'd like to send
                             //'body' => "Fine"
                             'body' => 
                "Thank you for booking your parking with Zoo Atlanta.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date \n$endLabel : $reservation->formatted_start_date \nAmount Charged : $$reservation->total"
                         )
                     );
                    }catch (RestException $e)
                    {
                        //throw new ApiGenericException($e->getMessage());
                        //return "success";
                    }
            
                 */

        $this->log->info("Pass reservation done ." . $reservation->id);
        return [
            'charge' => $charge,
            'reservation' => $reservation->withRelations(),
            'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : ''
        ];
    }



    /**
     * Save the processed reservation
     *
     * @return [type] [description]
     */
    protected function saveReservation($rate, bool $useBonus): Reservation
    {
        $discount = 0.00;
        $discount_credit_used = 0.00;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $discount = $this->request->redeemed_amount;
        }

        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $discount_credit_used = $this->request->redeemed_amount_credit;
        }

        //verifying the company tracking id
        $companyAffilateId = 0;
        if (isset($this->request->companyName) && $this->request->companyName != '') {
            $companyAffilateDetails = CompanyAffilate::where('slug', $this->request->companyName)->first();
            if ($companyAffilateDetails) {
                $companyAffilateId = $companyAffilateDetails->id;
            }
        }

        $reservation = new Reservation(
            [
                'user_id' => $this->user->id,
                'facility_id' => $this->facility->id,
                'start_timestamp' => $this->request->arrival,
                'length' => $this->request->length,
                'discount' => $discount,
                'credit_used' => $discount_credit_used,
                'company_affilate_id' => $companyAffilateId
            ]
        );


        //for processing fee
        if ($this->facility->processing_fee > 0) {

            $reservation->processing_fee = $this->facility->processing_fee;
        }

        if (is_a($rate, Rate::class)) {
            $reservation->rate_id = $rate->id;
        }

        if (is_array($rate) && isset($rate['id'])) {
            $reservation->rate_id = $rate['id'];
        }

        if ($useBonus) {
            $reservation->applyBonus();
        }

        // If this is higher the apply bonus will get double applied
        $reservation->total = $this->request->total;
        $reservation->mer_reference = isset($this->request->MerchantRef) ? $this->request->MerchantRef : '';
        $reservation->payment_gateway = 'planet';

        $reservation->save();



        return $reservation;
    }


    /**
    /**
     * Save the processed reservation
     *
     * @return [type] [description]
     */
    protected function savePassReservation($rate, bool $useBonus): Reservation
    {
        $discount = 0.00;
        $discount_credit_used = 0.00;

        //verifying the company tracking id
        $companyAffilateId = 0;

        $reservation = new Reservation(
            [
                'user_id' => $this->user->id,
                'facility_id' => $this->facility->id,
                'start_timestamp' => $this->request->arrival,
                'length' => $this->request->length,
                'discount' => $discount,
                'credit_used' => $discount_credit_used,
                'company_affilate_id' => $companyAffilateId
            ]
        );



        if (is_a($rate, Rate::class)) {
            $reservation->rate_id = $rate->id;
        }

        if (is_array($rate) && isset($rate['id'])) {
            $reservation->rate_id = $rate['id'];
        }


        // If this is higher the apply bonus will get double applied
        $reservation->total = $this->request->total;
        $reservation->mer_reference = isset($this->request->MerchantRef) ? $this->request->MerchantRef : '';
        $reservation->payment_gateway = 'planet';
        $reservation->save();

        return $reservation;
    }

    /**
     * Update the processed reservation
     *
     * @return [type] [description]
     */
    protected function updateReservation(Reservation $reservation, bool $useOverstay): Reservation
    {
        if ($useOverstay) {
            $reservation->applyOverstay();
        }

        $reservation->save();

        return $reservation;
    }

    /**
     * Get billing address information in auth net form from the current request
     * Note we are currently only sending the user name, not the complete billing address
     *
     * @return [type] [description]
     */
    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }
    protected function getBillingName()
    {
        $name = $this->request->name_on_card ?: $this->user->name;

        $nameArray = explode(' ', trim($name));

        return (reset($nameArray) . " " . end($nameArray));
    }

    private function getDatesFromRange($start_date, $end_date, $date_format = 'Y-m-d')
    {
        $dates_array = array();
        for ($x = strtotime($start_date); $x <= strtotime($end_date); $x += 86400) {
            array_push($dates_array, date($date_format, $x));
        }

        return $dates_array;
    }

    //update Reservation Availibility 
    public function updateReservationAvailability(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $reservation = Reservation::where('id', $request->reservation_id)->first();
        $type = $request->type;

        if ($type == self::DEFAULT_CONFIRM_VAL) {

            //check if it is not getting updating twice.
            if ($reservation->is_avaiability_updated != '0') {

                throw new ApiGenericException('Sorry, Availability can not be updated now', 422);
            }
        }
        if ($type == self::DEFAULT_CANCEL_VAL) {
            //check if it is not getting updating twice.
            if ($reservation->is_avaiability_updated != '1') {
                throw new ApiGenericException('Sorry, Availability can not be updated now', 422);
            }
        }

        if ($type == self::DEFAULT_CONFIRM_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_CONFIRM_VAL;
        } else  if ($type == self::DEFAULT_CANCEL_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_CANCEL_VAL;
        }
        $reservation->save();


        $facility_id = $reservation->facility_id;
        $date_time_in = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
        $length = round($reservation->length, 0);

        $date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($length)));

        $reservation_length = $reservation->length;
        $reservation_minutes = 0;
        $reservation_hours = explode(".", $reservation->length);
        if (isset($reservation_hours[1]) && ($reservation_hours[1]) > 0) {
            $reservation_minutes = 30;
        }
        $reservation_date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));


        $inventoryRepository = new Inventory();

        //check how many slots does entry and exit time occupies
        $difference = date_diff(date_create(date('Y-m-d', strtotime($date_time_in))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

        //check if someone is parking for more than a day

        if ($difference->d > 0) {

            $dates   = $inventoryRepository->generateArrayOfDates(
                ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                date('Y-m-d H:i:s', strtotime($date_time_in))
            );

            $dayDifference = $difference->d;

            foreach ($dates as $key => $date) {

                $dayIn = date('w', strtotime($date->format('Y-m-d')));

                $hours = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->first();

                $startingHour = self::DEFAULT_HOURS;
                $endingHour   = self::TWENTY_FOUR_HOURS;

                if ($hours) {
                    $startingHour = date('G', strtotime($hours->open_time));
                    $endingHour   = date('G', strtotime($hours->close_time));
                }

                $facilityAvailability = FacilityAvailability::where(
                    ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                )->first();
                if ($facilityAvailability) {
                    $inventory = json_decode($facilityAvailability->availability);
                    if ($key == 0) {
                        /**
                         * because this is the first day in the dates provided
                         * we should remove 1 from each time_slot starting
                         * from the hour provided in the api call
                         */
                        $i = date('G', strtotime($date_time_in));
                        if ($startingHour > $i) {
                            $i = $startingHour;
                        }

                        //$loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
                        $loopEnd  = self::TWENTY_FOUR_HOURS;
                        while ($i <= $loopEnd) {
                            if (isset($inventory->{$i})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$i} = $inventory->{$i} + 1;
                                } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$i} = $inventory->{$i} - 1;
                                }
                            }
                            $i++;
                        }
                    } elseif ($key == $dayDifference) {
                        $i = date('G', strtotime($date_time_out));
                        $minutes = date('i', strtotime($reservation_date_time_out));
                        $starting_minutes = date('i', strtotime($date_time_in));
                        if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                            $i++;
                        }
                        /**
                         * because this is the last day in the dates provided
                         * we should remove 1 from each time_slot starting
                         * till the hour provided in the api call
                         */

                        $j  = 0;
                        while ($j < $i) {
                            if (isset($inventory->{$j})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$j} = $inventory->{$j} + 1;
                                } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$j} = $inventory->{$j} - 1;
                                }
                            }
                            $j++;
                        }
                    } else {
                        /**
                         * because this could be any day except first and last in
                         * the dates provided we should remove 1 from whole day
                         */

                        $k = 0;
                        while ($k <= self::TWENTY_FOUR_HOURS) {
                            if (isset($inventory->{$k})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$k} = $inventory->{$k} + 1;
                                } else   if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$k} = $inventory->{$k} - 1;
                                }
                            }
                            $k++;
                        }
                    }

                    $facilityAvailability->availability = json_encode($inventory, JSON_FORCE_OBJECT);
                    $facilityAvailability->save();
                }
            }
        } else {

            $startingHour = date('G', strtotime($date_time_in));
            $endingHour   = date('G', strtotime($date_time_out));
            $facilityAvailability     = FacilityAvailability::where(
                ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))]
            )->first();

            if ($facilityAvailability) {
                $availability = json_decode($facilityAvailability->availability);

                $minutes = date('i', strtotime($reservation_date_time_out));
                $starting_minutes = date('i', strtotime($date_time_in));
                if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                    $endingHour++;
                }
                while ($startingHour < $endingHour) {

                    if ($type == self::DEFAULT_CANCEL_VAL) {
                        $availability->{$startingHour} = $availability->{$startingHour} + 1;
                    } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                        $availability->{$startingHour} = $availability->{$startingHour} - 1;
                    }
                    $startingHour++;
                }

                $facilityAvailability->availability = json_encode($availability, JSON_FORCE_OBJECT);

                $facilityAvailability->save();
            }
        }

        //initialise the queue to update partner ineventory db
        $updateJobParams = ['reservationId' => $request->reservation_id, 'type' => self::RESERVATION_TYPE];
        Artisan::queue('cron:update-inventory', $updateJobParams);
        //       $this->updateReservationAvailabilityPartner($request->reservation_id);

        // Return reservation and charge details to caller
        return [
            'is_avalibility_update' => true,
        ];
    }

    public function sendSmsAfterReservation(Request $request)
    {
        $reservation = Reservation::with('user')->where('ticketech_code', $request->ticketech_code)->first();

        if (!$reservation) {
            throw new ApiGenericException('No reservation found.');
        }
        if ($reservation->is_daily == '0') {
            $startLabel = "Enter After";
            $endLabel = "Exit Before";
        } else {
            $startLabel = "Start Date";
            $endLabel = "End Date";
        }


        //send sms to user
        try {
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);

            try {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                    // the number you'd like to send the message to
                    $reservation->user->phone,
                    array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        'body' =>
                        "Thank you for booking with Spelman College. Your parking has been confirmed.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date, $reservation->formatted_start_time \n$endLabel : $reservation->formatted_end_date, $reservation->formatted_end_time \nAmount Charged : $$reservation->total"
                    )
                );
            } catch (RestException $e) {
                // throw new ApiGenericException($e->getMessage());
            }
        } catch (RestException $e) {
            // throw new ApiGenericException($e->getMessage());
        }

        return "true";
    }


    public function checkUserPermit(Request $request)
    {
        $permitVehicle = PermitVehicle::with([
            'vehicles' => function ($query) {
                $query->where('desired_end_date', '>=', date("Y-m-d"));
            }
        ])->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();

        if (!$permitVehicle) {
            throw new ApiGenericException('No permit found.');
        }
        if (isset($permitVehicle->vehicles->desired_end_date)) {

            $today = strtotime(date("Y-m-d H:i:s"));
            $endDate = strtotime($permitVehicle->vehicles->desired_end_date);
            $permitVehicle->vehicles['is_expired'] = 0;
            if ($today > $endDate) {
                $permitVehicle->vehicles['is_expired'] = 1;
            }
            $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
            $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
            return $permitVehicle;
        } else {

            $permitVehicle = PermitVehicle::with('vehicles')->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();
            if (isset($permitVehicle->vehicles->desired_end_date)) {

                $today = strtotime(date("Y-m-d H:i:s"));
                $endDate = strtotime($permitVehicle->vehicles->desired_end_date);
                $permitVehicle->vehicles['is_expired'] = 0;
                if ($today > $endDate) {
                    $permitVehicle->vehicles['is_expired'] = 1;
                }
                $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
                $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
                return $permitVehicle;
            } else {
                throw new ApiGenericException('This permit has been expired or not valid.');
            }
        }
    }
    public function allPermitData(Request $request)
    {
        $query = PermitVehicle::query();

        //for date
        if (isset($request->license_plate_number) && ($request->license_plate_number != '')) {
            $query->where('license_plate_number', 'like', "{$request->license_plate_number}%");
        }
        //expired
        $today = date('Y-m-d');
        if (isset($request->expired_flag)) {
            if ($request->expired_flag == '1') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('desired_end_date', '>', $today);
                });
            }
            if ($request->expired_flag == '2') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('desired_end_date', '<=', $today);
                });
            }
        }

        $query->whereHas('vehicles', function ($q) {
            $q->whereNull('deleted_at');
        });

        $query->with('vehicles');
        return $query->paginate(20);


        //           $result->vehicles['desired_start_date'] = $this->getDaySufixFormat($result->vehicles->desired_start_date);
        //         $result->vehicles['desired_end_date'] = $this->getDaySufixFormat($result->vehicles->desired_end_date);





    }

    public function getDaySufixFormat($date)
    {
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        return $number . $suffix . ' ' . $monthYear;
    }


    public function getBookingList(Request $request)
    {

        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
                $result  = [];
                $result["total"] = 0;
                $result["per_page"] = 20;
                $result["current_page"] = 1;
                $result["last_page"] = 1;
                $result["next_page_url"] = Null;
                $result["prev_page_url"] = Null;
                $result["from"] = Null;
                $result["to"] = Null;
                $result["data"] = [];
                return $result;
            } else {
                $partner_id = Auth::user()->id;
            }
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }
        $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction']);

        if (isset($request->search)) {
            $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%");
            $reservation = $reservation->orWhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%");
                }
            );
            $reservation = $reservation->orWhereHas(
                'transaction',
                function ($query) use ($request) {
                    $query
                        ->where('ref_id', 'like', "%{$request->search}%");
                }
            );
        }
        if ($request->booking_type == '0') {
            $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
        } elseif ($request->booking_type == '1') {
            $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
        } else {
            $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
        }

        $reservation = $reservation->where(function ($query) use ($partner_id) {
            $query->where('partner_id', $partner_id);
        });
        if ($request->sort != '') {
            $reservation = $reservation->orderBy($request->sort, $request->sortBy);
        } else {
            $reservation = $reservation->orderBy('id', 'DESC');
        }

        /*$reservation = QueryBuilder::buildSearchQueryForPartner($reservation, $request->search, Reservation::$searchFields)
        ->orWhereHas(
                'user', function ($query) use ($request) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%");
                }
        );*/


        $reservation = $reservation->paginate(20);
        if ($request->sort == 'phone') {
            if (count($reservation) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->phone > $reservation[$j]['user']->phone) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->phone < $reservation[$j]['user']->phone) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }
        if ($request->sort == 'email') {
            if (count($reservation) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->email > $reservation[$j]['user']->email) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->email < $reservation[$j]['user']->email) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }


        return $reservation;
    }


    public function showQrCodeImage($photo)
    {
        if (!$photo) {
            throw new NotFoundException('No image with that name found.');
        }

        $file = Storage::disk('local')->get($photo) ?: null;
        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/' . $photo));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function cancelTicket($reservation_id)
    {
        $reservation = Reservation::with(['ticket'])->where("id", $reservation_id)->first();
        // return $reservation;
        if ($reservation) {
            if ($reservation->total == 0.00 || $reservation->total == 0 || $reservation->total == NULL) {
                //return $reservation->total;
                $reservation->cancelled_at = date("Y-m-d H:i:s");
                $reservation->save();
                Artisan::queue('reservation:email-cancellation', array('reservationId' => $reservation_id));
                $this->log->info("Booking Cancel against total Amount is 0 to  Atlanta reservation id:" . $reservation_id . "--" . json_encode($reservation));
                return 'User booking successfully canceled.';
            } else if ($reservation->user_pass_id != NULL) {
                $reservation->cancelled_at = date("Y-m-d H:i:s");
                $reservation->save();
                $pass = UserPass::where("id", $reservation->user_pass_id)->first();

                $pass->consume_days = $pass->consume_days - 1;
                $pass->remaining_days = $pass->remaining_days + 1;
                //return $pass;
                $pass->save();
                Artisan::queue('reservation:email-cancellation', array('reservationId' => $reservation_id));
                $this->log->info("Booking Cancel against Pass to  Atlanta reservation id:" . $reservation_id . "--" . json_encode($pass));
                return 'User booking successfully canceled.';
            } else {
                // return $reservation->total;
                if ($reservation->payment_gateway == 'authnet') {
                    return $reservation->cancel();
                } else {
                    $refundstatus = $this->atlantaRefundAmount($reservation_id);
                    // return $refundstatus;
                    foreach ($refundstatus as $val) {
                        //print_r($val['Type']);
                        //if( $val['Params']['ResultReason'] == "APPROVED" || $val['Params']['ResultReason'] == "APPROVAL        " || $val['Params']['ResultReason'] == "APPROVAL"){
                        if ($val['Params']['TxState'] == 'AR' || $val['Params']['TxState'] == 'CQ') {
                            $reservation->cancelled_at = date("Y-m-d H:i:s");
                            $reservation->save();
                            Artisan::queue('reservation:email-cancellation', array('reservationId' => $reservation_id));
                            $this->log->info("Payment refund  to  Atlanta reservation id:" . $reservation_id . "--" . json_encode($val['Params']));
                            return 'User booking successfully canceled.';
                        } else {
                            $this->log->info("payment refund fail to Atlanta reservation id:" . $reservation_id . "--" . json_encode($val['Params']));
                            throw new ApiGenericException('Refund Fail due to' . " " . $val['Params']['ResultReason']);
                        }
                    }
                }
            }
        } else {
            throw new ApiGenericException('Reservation id not found');
        }
    }



    // refund Amount
    public function atlantaRefundAmount($reservation_id)
    {
        //$reservation_id = "3145";
        $reservation = Reservation::with('transaction')->where("id", $reservation_id)->first();
        $tran_id = $reservation->transaction->anet_trans_id;
        $amount = - ($reservation->total * 100);
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => config('parkengage.AAA_PLANET_REFUND_URL') . $tran_id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
             "Request": {
                 "Type": "payrequestnocardreadbytxid",
                 "Version": "W2MXG520",
                 "Credentials": {
                     "ValidationID": "AtlantaFultonCountyRe",
                     "ValidationCode": "4tl4nt4fult.C0untyR3!",
                     "ValidationCodeHash": null
                 },
                 "Params": {
                     "RequesterTransRefNum": "NAU TEST PAYMENT 001",
                      "Amount": "' . $amount . '",
                     "Currency": "USD"
                     }
             }
         }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //return gettype($response);
        return $refundstatus = json_decode($response, TRUE);
    }

    public function downloadQrCode($reservation_id)
    {

        $reservation = Reservation::with(['transaction', 'facility', 'user', 'rate'])->where('id', $reservation_id)->first();
        if (!$reservation) {
            throw new ApiGenericException("Invalid booking details.");
        }
        $pdf = (new MapcoQrcode())->generatePdf($reservation, Pdf::class);
        return $pdf;
    }

    public function sendUserDetailsEmail($reservation_id)
    {

        $reservation = Reservation::with(['user', 'userPass', 'userPass.transaction', 'rate', 'facility', 'transaction'])->where('id', $reservation_id)->first();
        if (!$reservation) {
            throw new ApiGenericException("Invalid booking details.");
        }
        //return $resultData;
        //Artisan::queue('send:userpass-details-email',array('id' => $reservation_id));

        $clientData = OauthClient::where('partner_id', Auth::user()->id)->first();

        $this->id = $reservation_id;

        //$email = $reservation->user->email;
        $reservation->emailReservationToPartnerUser($clientData->secret);
        return 'Email successfully sent to the user.';
    }

    public function downloadBookingPdf($id)
    {
        /*
		//$data = Reservation::with(['userPass','transaction','facility','user'])->where('id',$id)->first(); 
        $data = Reservation::with(['userPass','userPass.transaction','transaction','facility','user'])->where('id',$id)->first();
        //return $data;		
		
		if(!$data){
			throw new ApiGenericException('Reservation Data Not Found');    
		}
		
		//return $data;
		$html = view("download.aaa_booking_pdf", ["data" =>$data])->render();
		$image = app()->make(Pdf::class);
		$pdf = $image->getOutputFromHtmlString($html);
		return $pdf;
		*/
        //$data = Reservation::with(['userPass','transaction','facility','user'])->where('id',$id)->first(); 
        $reservation = Reservation::with(['userPass', 'userPass.transaction', 'transaction', 'facility', 'user'])->where('id', $id)->first();
        //  return $reservation->partner_id;		

        if (!$reservation) {
            throw new ApiGenericException('Reservation Data Not Found');
        }

        $clientData = OauthClient::where('partner_id', $reservation->partner_id)->first();
        //return $clientData;	
        $client_secret = $clientData->secret != '' ? $clientData->secret : '';

        $image = $reservation->generateStubJpg($client_secret);

        $imageBarcode = $reservation->generateBarcodeJpgNew($client_secret);

        $reservationVal = $reservation->getReservationVal();


        $reservationFileName = str_random(10) . '_reservation.jpg';
        Storage::put($reservationFileName, $image);

        $imageBarcodeFileName = str_random(10) . '_barcode.png';
        Storage::put($imageBarcodeFileName, $imageBarcode);

        $facilityimage = '';
        $warning_on_reservation = $reservation->warning_on_reservation;
        $warning_on_reservation_msg = '';

        if ($warning_on_reservation) {
            $warning_on_reservation_msg = $reservation->warning_on_reservation_msg;
        }
        $is_end_time_updated = isset($reservation->is_end_time_updated) ? $reservation->is_end_time_updated : 0;

        $userpass = [];
        if ($reservation->user_pass_id != '') {
            $userpass = UserPass::find($reservation->user_pass_id);
        }



        $brand_setting = BrandSetting::where('user_id', $reservation->user->created_by)->first();
        //return $reservationVal;    
        $html = view("download.email-atlanta", [
            'image_path' => $reservationFileName,
            'warning_on_reservation_msg' => $warning_on_reservation_msg,
            'bar_image_path' => $imageBarcodeFileName,
            'facility_image_path' => $facilityimage,
            'photo' => $reservationVal['photo'],
            'reservation' => $reservation,
            'addressLink' => $reservation->facility->generateAddressLink(),
            'reservationVal' => $reservationVal,
            'is_end_time_updated' => $is_end_time_updated,
            'client_secret' => $client_secret,
            'brand_setting' => $brand_setting,
            'user_pass' => $userpass
        ])->render();
        $image = app()->make(Pdf::class);
        $pdf = $image->getOutputFromHtmlString($html);
        return $pdf;
    }

    public function paymentSuccess(Request $request)
    {

        $this->log->info("AAA After Payment Success request received : " . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {
            $this->log->info("AAA After Payment Success X-ClientSecret Found : " . $request->header('X-ClientSecret'));
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                //throw new NotFoundException('No partner found.');
                $this->log->info("No partner found");
            }
            //$checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();

            /*if(!$checkFacility){
             //   throw new NotFoundException('No garage found with this partner.');  
				$this->log->info("No garage found with this partner.");		
            }
            if($checkFacility->active != '1'){
              //  throw new ApiGenericException('Garage is not available for any booking');
				$this->log->info("Garage is not available for any booking");	
            }
            if($checkFacility->is_available != '1'){
               // throw new ApiGenericException('Garage is not available for any booking');
				$this->log->info("Garage is not available for any booking");	
            }
            if($checkFacility->facility_booking_type == '1'){
               // throw new ApiGenericException('Garage is not available for any booking');
				$this->log->info("Garage is not available for any booking");	
            }
            if (!$checkFacility->accept_cc) {
               // throw new ApiGenericException("Garage does not accept credit cards");
				$this->log->info("Garage does not accept credit cards");	
            }*/

            if ($this->request->TokenNo == '' || $this->request->AuthorisationCode == '') {
                //throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
                $this->log->info("Sorry! Payment not successfully done, There is some issue in payment response.");
                throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
            }

            if ($this->request->arrival != '') {
                if (strtotime(date("Y-m-d", strtotime($this->request->arrival))) < strtotime(date("Y-m-d"))) {
                    //throw new ApiGenericException('Parking Date can not be a past date');       
                    $this->log->info("Parking Date can not be a past date");
                }
            }

            if (!$this->request->MerchantRef) {
                $this->log->info("MerchantRef Not Found");
            }

            //$member_user_id = '';
            $is_member = '0';
            if ($this->request->is_pass_purchase == '1') {
                $this->log->info("MerchantRef Found: Pass Case");
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->where('partner_id', $secret->partner_id)->where('mer_reference', $this->request->MerchantRef)->first();
                //return [$secret->partner_id,$this->request->MerchantRef];
                $pass = UserPass::where('partner_id', $secret->partner_id)->where('mer_reference', $this->request->MerchantRef)->first();
                //return $pass; 
                $details = $this->getExistingPassDetails($pass->id);

                $user = User::where('id', $pass['user_id'])->first();
            } else {
                $this->log->info("MerchantRef Found: Reservation Case");
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->where('partner_id', $secret->partner_id)->where('mer_reference', $this->request->MerchantRef)->first();
                $user = User::where('id', $reservation['user_id'])->first();
            }
            $this->user = $user;
            //$pass = UserPass::find($this->request->pass_id);

            if ($this->request->is_pass_purchase == '1') {

                //	if($this->request->pass_id != ''){
                //$details = $this->getExistingPassDetails($this->request->pass_id);
                $this->log->info("AAA Pass email about to Send");
                if ($this->request->TxID != '') {
                    //$pass->emailPassToUser();
                    //$this->log->info("AAA Pass email Sent");
                }
                //	}

                //$reservation = [];
                if ($this->request->is_book_parking == '1') {
                    $this->user->is_member = $is_member;

                    if ($reservation) {
                        //  Code Commented for Call Back Request
                        /*if($this->user->phone != ''){
                            $this->log->info("AAA Reservation email Sent with pass with SMS"); 
                            $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    
                            $this->log->info("AAA Reservation email Sent with SMS"); 
                            if($reservation->is_daily == '0'){
                                $startLabel = "Enter After";
                                $endLabel = "Exit Before";
                            }else{
                                $startLabel = "Start Date";
                                $endLabel = "End Date";    
                            }
                            //send sms to user
                            try{
                            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
                            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
                            $client = new Client($accountSid, $authToken);
                            try
                            {
                                // Use the client to do fun stuff like send text messages!
                                $client->messages->create(
                                // the number you'd like to send the message to
                                $this->user->phone,
                                array(
                                        // A Twilio phone number you purchased at twilio.com/console
                                        'from' => env('TWILIO_PHONE'),
                                        // the body of the text message you'd like to send
                                        //'body' => "Fine"
                                        'body' => 
                            "Thank you for booking your parking with Zoo Atlanta.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date \n$endLabel : $reservation->formatted_start_date \nAmount Charged : $$reservation->total"
                                     )
                                 );
                            }catch (RestException $e)
                            {
                                    //throw new ApiGenericException($e->getMessage());
                                    //return "success";
                                    //echo "Error: " . $e->getMessage();
                                    //$this->log->error($e->getMessage());
                                   //return "success";
                                   //throw new ApiGenericException($e->getMessage());
                                    //return "success";
                            }
                            }catch (RestException $e)
                            {
                                    //throw new ApiGenericException($e->getMessage());
                                    //return "success";
                                    //echo "Error: " . $e->getMessage();
                                    //$this->log->error($e->getMessage());
                                   //return "success";
                                   //throw new ApiGenericException($e->getMessage());
                                    //return "success";
                            }
                        }*/
                        //$res->user_pass_id = $details->id;
                        $reservation = $reservation->toArray();
                    }
                }
                return [
                    'pass' => $details->withRelations(),
                    'reservation' => $reservation
                ];
            } else {
                $this->log->info("MerchantRef Found1: Reservation Case");
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->where('partner_id', $secret->partner_id)->where('mer_reference', $this->request->MerchantRef)->first();
                $user = User::where('id', $reservation['user_id'])->first();

                /*$geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
                if($geoLocation['geoplugin_countryCode'] == 'IN'){
                    $this->countryCode = "+91";
                }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                    $this->countryCode = "+1";
                }else{
                    $this->countryCode = "+1";
                }*/
                $this->countryCode = "+1";
                $this->log->info("AAA Email about to Send");
                if ($this->request->header('X-ClientSecret') != '') {
                    //  Code Commented for Call Back Request	    
                    /*if($this->user->phone != ''){
                    $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    
                    $this->log->info("AAA Reservation email Sent with SMS"); 
                    if($reservation->is_daily == '0'){
                        $startLabel = "Enter After";
                        $endLabel = "Exit Before";
                    }else{
                        $startLabel = "Start Date";
                        $endLabel = "End Date";    
                    }
                    //send sms to user
					try{
                    $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
					$authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
                    $client = new Client($accountSid, $authToken);
                    try
                    {
                        // Use the client to do fun stuff like send text messages!
                        $client->messages->create(
                        // the number you'd like to send the message to
                        $this->user->phone,
                        array(
                                // A Twilio phone number you purchased at twilio.com/console
                                'from' => env('TWILIO_PHONE'),
                                // the body of the text message you'd like to send
                                //'body' => "Fine"
                                'body' => 
                    "Thank you for booking your parking with Zoo Atlanta.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date \n$endLabel : $reservation->formatted_start_date \nAmount Charged : $$reservation->total"
                             )
                         );
                    }catch (RestException $e)
                    {
                            //throw new ApiGenericException($e->getMessage());
                            //return "success";
							//echo "Error: " . $e->getMessage();
							//$this->log->error($e->getMessage());
						   //return "success";
						   //throw new ApiGenericException($e->getMessage());
							//return "success";
                    }
					}catch (RestException $e)
                    {
                            //throw new ApiGenericException($e->getMessage());
                            //return "success";
							//echo "Error: " . $e->getMessage();
							//$this->log->error($e->getMessage());
						   //return "success";
						   //throw new ApiGenericException($e->getMessage());
							//return "success";
                    }
                }else{
                    //$reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));    
                    //$this->log->info("AAA Reservation email Sent");		
                } */
                } else {
                    //$reservation->emailReservationToUser();
                }

                $this->log->info("booking completed with email.");
                // Return reservation and charge details to caller
                return [
                    'reservation' => $reservation
                ];
            }
        }
    }
}
