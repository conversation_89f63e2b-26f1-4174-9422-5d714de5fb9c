<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Auth;
use Response;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use Artisan;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\OauthClient;
use App\Classes\DatacapPaymentGateway;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\WorldportLicensePlate;
use App\Models\ParkEngage\KstreetLicensePlate;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
use App\Models\ParkEngage\LicensePlate;



class TattileCheckinCheckoutController extends Controller
{

    protected $log;
    protected $user;
    protected $facility;

    const  PARTNER_ID = 2980;

    use DispatchesJobs;
    const QUEUE_NAME = 'sms-send';
    const QUEUE_ENTRY = 'read-license-plate';

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;

        $this->log = $logFactory->setPath('logs/tattile')->createLogger('tattile');
    }


    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                config(['app.timezone' => $facility->timezone]);
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                config(['app.timezone' => $partnerTimezone->timezone]);
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }

    public function parkengageTattileFeed(Request $request)
    {
        try {
            $this->log->info("parkengageTattileFeed Request received --" . json_encode($request->all()));
            //dd($request->transit['plate']['text']);
            //start time $request->transit['timestamps']['start']

            $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $request->transit['lane'])->first();
            if ($gate) {
                $this->setCustomTimezone($gate->facility_id);
                $this->log->info("Request : " . date("Y-m-d H:i:s"));

                $facility = Facility::find($gate->facility_id);
                if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
                    $this->log->info("lpr is disabled");
                    return true;
                }

                $this->saveLicensePlate($request, $gate);

                if ($gate->gate_type == 'entry') {
                    $lastTicket = Ticket::where("facility_id", $gate->facility_id)->whereNull("checkout_time")->orderBy("id", "DESC")->first();

                    if (isset($lastTicket) && $lastTicket->license_plate == $request->transit['plate']['text'] && $lastTicket->is_checkout == '0') {
                        return "Duplicate Feed";
                    }
                    $this->saveCheckin($request, $gate);
                    $this->log->info("Checkin Done");
                } elseif ($gate->gate_type == 'exit') {
                    $this->saveCheckout($request, $gate);
                    $this->log->info("Checkout Done");
                } else {
                    $this->log->info("Invalid Gate");
                }
            } else {
                $this->log->info("Lane Id not matched");
            }
            return "Feed done";
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }


    public function saveCheckin($request, $gate)
    {
        $this->log->info("before checkin");
        try {
            $queue_name = '';
            $gate_type = '';
            if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                $gate_type = $gate->gate_type;
            }
            $alreadyCheckin = $this->alreadyCheckinStatus($request->transit['plate']['text'], $gate);
            if ($alreadyCheckin) {
                $this->log->info("You have already checked-in.");
                $msg = [];
                $msg['msg'] = 'You have already checked-in.';
                $this->sendCheckinCheckoutInQueue($alreadyCheckin, '-1', $msg, $queue_name, $gate_type);
                return true;
            }
            $ticket = new Ticket();
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            $response_type = '0';
            $msg = [];
            $isPermit = 0;
            $licensePlate = '';
            if ($request->transit['plate']['text'] == "8B03096" || $request->transit['plate']['text'] == "8BO3096" || $request->transit['plate']['text'] == "8BO3O96" || $request->transit['plate']['text'] == "8B03O96") {
                $licensePlate = "8B03096";
            }
            if ($facility->facilityConfiguration->is_check_cloud_permit == '1') {
                
                //check permit exist
                $permit = $this->checkExistingPermit($request->transit['plate']['text'], $gate->facility_id, $gate->partner_id);
                if (count($permit) > 0) {
                    $isPermit = 1;
                    $ticket->permit_request_id = $permit->id;
                    $ticket->user_id = $permit->user_id;
                }
            }

            $checkinTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
            $ticket->check_in_datetime = $checkinTime;
            $ticket->checkin_time = $checkinTime;
            $ticket->is_checkin = '0';

            $reservation = [];
            if ($isPermit != 1) {
                $reservation = $this->checkExistingReservation($request->transit['plate']['text'], $gate->facility_id);
                if (count($reservation) > 0) {
                    $response_type = '1';
                    $this->log->info("reservation checkin");
                    $resevationExitTime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                    $resevationExitTime = Carbon::parse($resevationExitTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
                    $ticket->reservation_id = $reservation->id;
                    $ticket->check_in_datetime = $reservation->start_timestamp;
                    $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->estimated_checkout = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->payment_date = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->length = $reservation->length;
                    $ticket->is_checkin = '1';
                    $ticket->user_id = $reservation->user_id;
                    $ticket->session_id = $reservation->session_id;
                    $msg['booking_number'] = $reservation->ticketech_code;
                    $msg['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
                    $msg['booking_exit_time'] = date("g:i A", strtotime($resevationExitTime));
                    $msg['booking_entry_time'] = date("g:i A", strtotime(date('Y-m-d H:i:s')));
                    $msg['is_phone_linked'] = '0';
                    $msg['phone_linked_msg'] = '';
                    $msg['booking_type'] = 'reservation';
                    $msg['is_check_in_ontime'] = '1';
                    $this->log->info("reservation check : " . json_encode($msg));
                }
            }

            if ($isPermit == 1) {
                $ticket->is_checkin = '1';
                $response_type = '1';
                $msg['permit_number'] = $permit->account_number;
                $msg['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
                $msg['is_phone_linked'] = '1';
                $msg['phone_linked_msg'] = '';
                $msg['booking_type'] = 'permit';
                $msg['is_check_in_ontime'] = '1';
            }

            $ticket->facility_id = $gate->facility_id;
            $ticket->partner_id = $facility->owner_id;
            $ticketNumber = $this->checkTicketNumber($gate->facility_id);
            $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
            if ($isExist) {
                $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
            } else {
                $ticket->ticket_number = $ticketNumber;
            }
            $ticket->license_plate = $licensePlate != '' ? $licensePlate : $request->transit['plate']['text'];
            $ticket->checkin_gate = $gate->gate;
            $ticket->device_type = "LPR";
            $ticket->save();

            if (count($reservation) > 0) {
                $reservation->is_ticket = '1';
                $reservation->save();
            }
            $this->log->info("checkin done {$ticket->ticket_number}");

            $msg['msg'] = "WELCOME #" . $ticket->ticket_number;

            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

            if ($ticket->user_id != '') {

                $user = User::find($ticket->user_id);
                if ($user->email != '') {
                    Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                }

                $facilityName = ucwords($facility->full_name);
                //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                if ($partnerDetails->user_id == self::PARTNER_ID) {
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }
                $url = env('TOUCHLESS_WEB_URL');
                $grace_period = $facility->grace_period_minute;
                $ticket_number = base64_encode($ticket->ticket_number);
                $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                if ($isPermit == 1) {
                    $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number.";
                }
                dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Tattile Info : Tattile Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            return true;
        }
    }

    public function checkExistingReservation($plate, $facility_id)
    {
        $reservation = Reservation::with('user')->where("license_plate", $plate)->where("facility_id", $facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
        if (!$reservation) {
            return $reservation;
        }
        return $reservation;
    }

    public function alreadyCheckinStatus($plate, $gate)
    {
        $ticket = Ticket::where("license_plate", $plate)->where("facility_id", $gate->facility_id)->where("checkin_gate", $gate->gate)->where("is_checkin", '1')->where("is_checkout", '0')->first();
        return $ticket;
    }


    public function sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type)
    {
        $this->log->info("before queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send " . $queue_name);
        //Artisan::queue('read-license-plate', array('license_plate' => $ticket->license_plate, 'is_checkin_or_checkout' => '1', 'ticket' => $ticket, 'response_type' => $response_type, "msg" => $msg, 'queue_name' => $queue_name, 'gate_type' => $gate_type));
        return $ticket;
    }


    //tickets number is total 7 digit number for kstreet
    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "KT";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }


    public function saveCheckout($request, $gate)
    {

        $this->log->info("checkout start");
        $queue_name = '';
        $gate_type = '';
        if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
            $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
            $gate_type = $gate->gate_type;
        }
        $licensePlate = $request->transit['plate']['text'];
        if ($request->transit['plate']['text'] == "8B03096" || $request->transit['plate']['text'] == "8BO3096" || $request->transit['plate']['text'] == "8BO3O96" || $request->transit['plate']['text'] == "8B03O96") {
            $licensePlate = "8B03096";
        }
        $ticket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation', 'user'])->where('license_plate', $licensePlate)->where('facility_id', $gate->facility_id)->where('is_checkout', '0')->whereNull("checkout_time")->orderBy("id", "DESC")->first();
        if ($ticket) {

            if ($ticket->is_transaction_status == '1') {
                $this->log->info("Payment already in process " . $ticket->ticket_number);
                return true;
            }

            $is_our_ticket = '1';
            if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0') {
                $is_our_ticket = '0';
            }

            $checkinDate = date("Y-m-d", strtotime($ticket->checkin_time));
            $today = date("Y-m-d", strtotime($request->transit['timestamps']['start']));
            $checkoutTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));

            $updatedCheckinTime = '';
            if (strtotime($today) != strtotime($checkinDate)) {
                $updatedCheckinTime = date("d M", strtotime($checkinDate)) . ' ' . date("g:i A", strtotime($ticket->checkin_time));
            } else {
                $updatedCheckinTime = date("g:i A", strtotime($ticket->checkin_time));
            }


            //permit check before checkout
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            if ($facility->facilityConfiguration->is_check_cloud_permit == '1') {
                if ($ticket->permit_request_id != '') {
                    $ticket->is_checkout = '1';
                    $ticket->checkout_gate = $gate->gate;
                    $ticket->checkout_time = $checkoutTime;
                    $ticket->checkout_datetime = $checkoutTime;
                    $ticket->estimated_checkout = $checkoutTime;
                    $ticket->payment_date = $checkoutTime;
                    $ticket->checkout_license_plate = $request->transit['plate']['text'];
                    $ticket->checkout_session_id = $ticket->session_id;
                    $ticket->checkout_mode = '4';
                    $ticket->is_transaction_status = '0';
                    $this->log->info(json_encode($ticket));
                    $ticket->save();

                    $response_type = '1';
                    $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                    $msg['is_phone_linked'] = '0';
                    $msg['phone_linked_msg'] = '';
                    $msg['booking_type'] = 'reservation';
                    $msg['is_check_in_ontime'] = '1';
                    $msg['is_our_ticket'] = $is_our_ticket;
                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                    if (isset($ticket->user->phone)) {
                        $this->log->info("SMS condition entered");
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::PARTNER_ID) {
                            $facility = $ticket->facility;
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('RECEIPT_URL');
                        $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                        $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                        dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                    }
                    return true;
                }
            }

            //overstay section start
            if ($ticket->estimated_checkout != '') {
                $overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
                if ($overstayExist) {
                    if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                        $ticket->is_checkout = '1';
                        $ticket->checkout_gate = $gate->gate;
                        $ticket->checkout_time = $checkoutTime;
                        $ticket->checkout_datetime = $checkoutTime;
                        $ticket->checkout_license_plate = $licensePlate;
                        $ticket->checkout_session_id = $ticket->session_id;
                        $ticket->checkout_mode = '4';
                        $ticket->is_transaction_status = '0';
                        $this->log->info(json_encode($ticket));
                        $ticket->save();

                        $response_type = '1';
                        $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                        $msg['is_phone_linked'] = '0';
                        $msg['phone_linked_msg'] = '';
                        $msg['booking_type'] = 'reservation';
                        $msg['is_check_in_ontime'] = '1';
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        return true;
                    } else {
                        $this->log->info("eticket overstay created.");
                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
                        $isMember = 0;
                        if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
                            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                        } else {
                            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                        }

                        if ($rate['price'] <= 0) {
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                        $rate['price'] = $rate['price'] + $taxRate;
                        if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            $ticket->is_transaction_status = '1';
                            $ticket->save();
                            $this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
                            if ($ticket->session_id == '') {
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $rate['price'], $request);
                            $this->log->info("Overstay Payment Response :" . json_encode($refundstatus));
                            //if payment error start
                            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                $this->log->error(" Overstay Payment failed :");

                                $ticket->is_transaction_status = '0';
                                $ticket->save();

                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due, Please use different card.";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, '1');

                            $overstay = $this->saveOverstayDetails($rate, $ticket, $ticket->facility);
                            $overstay->total = $rate['price'];
                            $overstay->grand_total = $rate['price'];
                            $overstay->anet_transaction_id = $planetTransaction->id;
                            $overstay->length = $diff_in_hours;
                            $overstay->tax_fee = $taxRate;
                            $overstay->save();


                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();



                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $response_type = '3';
                        $msg = [];
                        $msg['msg'] = "Amount Due";
                        $msg['is_phone_linked'] = '0';
                        $msg['checkin_time'] = $updatedCheckinTime;
                        $msg['amount'] = $rate['price'];
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['is_overstay'] =  '1';
                        $msg['eticket_id'] =  $ticket->ticket_number;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        return true;
                    }
                } else {

                    if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                    } else {
                        $this->log->info("overstay created.");

                        $overstayCheckinTime = $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout;

                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayCheckinTime);
                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayCheckinTime);

                        $isMember = 0;
                        if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
                            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                        } else {
                            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                        }


                        if ($rate['price'] <= 0) {
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                        $rate['price'] = $rate['price'] + $taxRate;
                        if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            $ticket->is_transaction_status = '1';
                            $ticket->save();
                            $this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
                            if ($ticket->session_id == '') {
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $rate['price'];
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));
                            if ($paymentResponse["Status"] == "Error") {
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    $this->log->info("datacap Payment Error Data -- 1");
                                } else {
                                    $this->log->info("datacap Payment Error Data -- 2");
                                }
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            }

                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                //$request->request->add(['card_last_four' => $ticket->card_last_four]);
                                //$request->request->add(['expiration' => $ticket->expiry]);

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            } else {
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $overstay = $this->saveOverstayDetails($rate, $ticket, $ticket->facility);
                            $overstay->total = $rate['price'];
                            $overstay->grand_total = $rate['price'];
                            $overstay->anet_transaction_id = $planetTransaction->id;
                            $overstay->length = $diff_in_hours;
                            $overstay->tax_fee = $taxRate;
                            $overstay->save();

                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();



                            $response_type = '1';
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $msg['msg'] = "THANK YOU FOR VISITING " . $facilityName . ".  Extra $" . $rate['price'] . " is charged for overstay.";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $response_type = '3';
                        $msg = [];
                        $msg['msg'] = "Amount Due";
                        $msg['is_phone_linked'] = '0';
                        $msg['checkin_time'] = $updatedCheckinTime;
                        $msg['amount'] = $rate['price'];
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['is_overstay'] =  '1';
                        $msg['eticket_id'] =  $ticket->ticket_number;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        return true;
                    }
                }
            }

            //overstay section end


            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

            $diff_in_hours = $ticket->getCheckinCheckOutDifference($checkoutTime);
            //$this->log->error($arrival_time.'--'. $diff_in_hours.'--'. $ticket->checkin_time. '--'.$checkoutTime);
            $isMember = 0;
            if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
                $this->log->error("worldport case");
                $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->error("townsnend case");
                $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
            }
            if ($rate == false) {
                $this->log->error("rate not found");
            }

            $this->log->error($arrival_time . '--' . $diff_in_hours . '--' . $ticket->checkin_time . '--' . $checkoutTime . '--' . $rate['price']);

            $priceBreakUp = $ticket->priceBreakUp($rate);
            $this->log->info("priceBreakUp " . json_encode($priceBreakUp));

            if ($is_our_ticket == '1') {
                if ($priceBreakUp['payable_amount'] > 0 && $priceBreakUp['payable_amount'] <= 100) {
                    if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                        //save temorporary amount in ticket temp table to save in driverup
                        QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
                        $ticket->is_transaction_status = '1';

                        $this->log->info("is_lpr_cloud_payment_enabled entered");
                        if ($ticket->session_id == '') {
                            $ticket->is_transaction_status = '0';
                            $ticket->save();
                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $priceBreakUp['payable_amount'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }


                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $priceBreakUp['payable_amount'];
                        $datacap['Token'] = $ticket->payment_token;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);

                        $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));

                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                $this->log->info("datacap Payment Error Data -- 1");
                            } else {
                                $this->log->info("datacap Payment Error Data -- 2");
                            }

                            $ticket->is_transaction_status = '0';
                            $ticket->save();

                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due, Please use different card.";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $priceBreakUp['payable_amount'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        if ($paymentResponse['Status'] == 'Approved') {
                            $user_id = $ticket->user_id;

                            $request->request->add(['card_last_four' => $ticket->card_last_four]);
                            $request->request->add(['expiration' => $ticket->expiry]);

                            $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            $ticket->anet_transaction_id = $planetTransaction->id;
                        } else {

                            $ticket->is_transaction_status = '0';
                            $ticket->save();

                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due, Please use different card.";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $priceBreakUp['payable_amount'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }


                        $ticket->is_checkout = '1';
                        $ticket->checkout_gate = $gate->gate;
                        $ticket->checkout_time = $checkoutTime;
                        if ($ticket->reservation_id != '') {
                            $ticket->checkout_datetime = $checkoutTime;
                        }
                        $ticket->checkout_license_plate = $licensePlate;
                        //$request->transit['plate']['text']
                        $ticket->checkout_session_id = $ticket->session_id;
                        $ticket->checkout_mode = '4';
                        $ticket->is_cloud_payment = '1';
                        $ticket->is_transaction_status = '0';
                        $this->log->info(json_encode($ticket));
                        $ticket->save();

                        $response_type = '1';
                        $msg['msg'] = "THANK YOU FOR VISITING CHARGED: $" . ($priceBreakUp['payable_amount']) . " #" . $ticket->ticket_number;
                        $msg['is_phone_linked'] = '0';
                        $msg['phone_linked_msg'] = '';
                        $msg['is_check_in_ontime'] = '1';
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['ticket_id'] = $ticket->ticket_number;
                        $msg['booking_type'] = 'driveup';
                        if ($ticket->reservation_id != '') {
                            $msg['booking_number'] = $ticket->reservation_id != '' ? $ticket->reservation->ticketech_code : '';
                            $msg['booking_start_time'] = date("g:i A", strtotime($ticket->check_in_datetime));
                            $msg['booking_exit_time'] = date("g:i A", strtotime($ticket->checkout_datetime));
                            $msg['booking_entry_time'] = date("g:i A", strtotime($ticket->checkin_time));
                            $msg['booking_type'] = 'reservation';
                        }
                        $msg['amount'] = number_format($priceBreakUp['payable_amount'], 2);
                        $msg['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

                        $length_of_stay = '';
                        $diff_in_days = $startDate->diffInDays($endDate);
                        $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
                        $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);

                        if ($diff_in_days > 0) {
                            if ($diff_in_days == 1) {
                                $length_of_stay .= $diff_in_days . " day ";
                            } else {
                                $length_of_stay .= $diff_in_days . " days ";
                            }
                        }
                        if ($diff_in_hours > 0) {
                            $length_of_stay .= $diff_in_hours . " hr ";
                        }
                        if ($diff_in_minutes <= 0) {
                            $length_of_stay .= "0 min";
                        }
                        if ($diff_in_minutes > 0) {
                            $length_of_stay .= $diff_in_minutes . " min";
                        }
                        $msg['length_of_stay'] = $length_of_stay;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                        if (isset($ticket->user->phone)) {
                            $this->log->info("SMS condition entered");
                            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                            if ($partnerDetails->user_id == self::PARTNER_ID) {
                                $facility = $ticket->facility;
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                                $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                            } else {
                                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                            }
                            $url = env('RECEIPT_URL');
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                            dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                        }

                        return true;
                    }
                    //if payment error close

                    QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
                    $this->log->error("Without payment :");
                    $response_type = '3';
                    $msg = [];
                    $msg['msg'] = "Amount Due";
                    $msg['is_phone_linked'] = '0';
                    $msg['checkin_time'] = $updatedCheckinTime;
                    $msg['amount'] = $priceBreakUp['payable_amount'];
                    $msg['is_our_ticket'] = $is_our_ticket;
                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                    return true;
                }else{
                    $this->log->info("amount greater log ");
                    return true;
                }
            }

            $this->log->info("LPR before checkout");

            if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0') {
                $this->log->info("zeag checkout not done --" . $ticket->ticket_number);

                $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                $ticket->tax_fee        = $priceBreakUp['tax_rate'];
                $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                $ticket->discount_amount = $priceBreakUp['discount_amount'];
                $ticket->grand_total     = $priceBreakUp['payable_amount'];
                $ticket->total     = $priceBreakUp['total'];
                $ticket->length     = $diff_in_hours;

                $ticket->is_checkout = '0';
                $ticket->checkout_gate = $gate->gate;
                $ticket->checkout_time = $checkoutTime;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->checkout_license_plate = $licensePlate;
                $ticket->checkout_session_id = $ticket->session_id;
                $ticket->checkout_mode = '4';
                $ticket->is_transaction_status = '0';
                $ticket->save();

                if ($priceBreakUp['payable_amount'] > 0 && $priceBreakUp['payable_amount'] <= 100) {
                    $response_type = '3';
                    $msg = [];
                    $msg['msg'] = "Amount Due";
                    $msg['is_phone_linked'] = '0';
                    $msg['checkin_time'] = $updatedCheckinTime;
                    $msg['amount'] = $priceBreakUp['payable_amount'];
                    $msg['is_our_ticket'] = $is_our_ticket;
                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                }

               /* $msg = [];
                $msg['msg'] = 'Zeag ticket found.';
                $msg['license_plate'] = $request->transit['plate']['text'];
                $this->sendAnonymousCheckinCheckoutInQueue('-2', $msg, $queue_name, $gate_type, $request->transit['plate']['text']);
                */
                return true;
            }

            $ticket->processing_fee  = $priceBreakUp['processing_fee'];
            $ticket->tax_fee        = $priceBreakUp['tax_rate'];
            $ticket->parking_amount  = $priceBreakUp['parking_amount'];
            $ticket->paid_amount     = $priceBreakUp['paid_amount'];
            $ticket->discount_amount = $priceBreakUp['discount_amount'];
            $ticket->grand_total     = $priceBreakUp['grand_total'];
            $ticket->total     = $priceBreakUp['total'];
            $ticket->length     = $diff_in_hours;

            $ticket->is_checkout = '1';
            $ticket->checkout_gate = $gate->gate;
            $ticket->checkout_time = $checkoutTime;
            $ticket->checkout_datetime = $checkoutTime;
            $ticket->checkout_license_plate = $request->transit['plate']['text'];
            $ticket->checkout_mode = '4';
            $ticket->is_transaction_status = '0';
            $ticket->save();

            $msg = [];
            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";

            $msg['is_phone_linked'] = '0';
            $msg['phone_linked_msg'] = '';
            $msg['booking_type'] = 'reservation';
            $msg['is_check_in_ontime'] = '1';
            $response_type = '1';
            $msg['is_our_ticket'] = $is_our_ticket;
            $this->log->info("checkout done {$ticket->ticket_number}");
            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

            if (isset($ticket->user->phone)) {
                $this->log->info("SMS condition entered");
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                if ($partnerDetails->user_id == self::PARTNER_ID) {
                    $facility = $ticket->facility;
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }
                $url = env('RECEIPT_URL');
                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
            }

            return true;
        } else {
            $this->log->info("no checkin found in checkout function");
            $msg = [];
            $msg['msg'] = 'No checkin found against this license plate.';
            $msg['license_plate'] = $request->transit['plate']['text'];
            $this->sendAnonymousCheckinCheckoutInQueue('-2', $msg, $queue_name, $gate_type, $request->transit['plate']['text']);
            return true;
        }
    }


    public function saveOverstayDetails($rate, $ticket, $facility)
    {

        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';

        $currentTime = Carbon::parse('now');
        $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
        $overstay = new OverstayTicket();
        $overstay->user_id = $ticket->user_id;
        $overstay->facility_id = $ticket->facility_id;
        $overstay->ticket_number = $ticket->ticket_number;
        $overstay->is_checkin = '1';
        $overstay->is_checkout = '1';
        $overstay->check_in_datetime = $ticket->estimated_checkout;
        $overstay->checkout_datetime = $estimated_checkout;
        $overstay->estimated_checkout = $estimated_checkout;
        $overstay->partner_id = $ticket->partner_id;
        $overstay->ticket_id = $ticket->id;
        $overstay->payment_date = date("Y-m-d H:i:s");
        $overstay->rate_id = $rate_id;
        $overstay->rate_description = $rate_description;
        $overstay->reservation_id = $ticket->reservation_id;
        $overstay->save();

        return $overstay;
    }



    public function saveLicensePlate($request, $gate)
    {
        KstreetLicensePlate::where("facility_id", $gate->facility_id)->where("gate", $gate->gate)->delete();
        $license['license_plate'] = $request->transit['plate']['text'];
        $license['partner_id'] =  $gate->partner_id;
        //$license['make'] = $data->Make;
        //$license['model'] = $data->Model;
        $license['facility_id'] =  $gate->facility_id;
        $license['gate'] = $gate->gate;
        $license['gate_type'] = $gate->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
        $result = KstreetLicensePlate::create($license);
        return $result;
    }


    public function checkExistingPermit($plate, $facility_id, $partner_id)
    {
        if ($plate == "8B03096" || $plate == "8BO3O96" || $plate == "8BO3096" || $plate == "8B03O96") {
            $plate = "8B03096";
        }
        $permitVehicles = PermitVehicle::where("license_plate_number", $plate)->where("partner_id", $partner_id)->get();
        if (count($permitVehicles) > 0) {
            foreach ($permitVehicles as $key => $permitVehicle) {
                $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $facility_id)->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
                if (count($permitRequests) > 0) {
                    foreach ($permitRequests as $key => $permitRequest) {

                        $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                        if (!$mapping) {
                            continue;
                        }
                        return $permitRequest;
                    }
                } else {
                    return $permitRequests;
                }
            }
        }
        return $permitVehicles;
    }


    public function sendAnonymousCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type, $license_plate)
    {
        $this->log->info("sendAnonymousCheckinCheckoutInQueue queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['data' => $msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id' => 'KTERROR', 'license_plate' => $license_plate, 'data' => $msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send " . $queue_name);
        return true;
    }

    public function townsendTattileFeed(Request $request)
    {

        $this->log->info("townsendTattileFeed Request received --" . json_encode($request->all()));
        //dd($request->transit['plate']['text']);
        //start time $request->transit['timestamps']['start']
        $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $request->transit['lane'])->first();
        if ($gate) {
            $this->setCustomTimezone($gate->facility_id);
            $this->log->info("townsendTattileFeed Request : " . date("Y-m-d H:i:s"));

            $facility = Facility::find($gate->facility_id);

            $this->saveTownsendLicensePlate($request, $gate);

            if ($gate->gate_type == 'entry') {
                $lastTicket = Ticket::where("facility_id", $gate->facility_id)->whereNull("checkout_time")->orderBy("id", "DESC")->first();
                if (isset($lastTicket) && $lastTicket->license_plate == $request->transit['plate']['text'] && $lastTicket->is_checkout == '0') {
                    return "Duplicate Feed";
                }
                $this->saveTownsendCheckin($request, $gate);
                $this->log->info("Checkin Done");
            } elseif ($gate->gate_type == 'exit') {

                if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
                    $this->log->info("townsendTattileFeed lpr is disabled");
                    return true;
                }
                $this->saveTownsendCheckout($request, $gate);
                $this->log->info("Checkout Done");
            } else {
                $this->log->info("Invalid Gate");
            }
        } else {
            $this->log->info("Lane Id not matched");
        }
        return "Feed done";
    }

    public function saveTownsendLicensePlate($request, $gate)
    {
        LicensePlate::where("facility_id", $gate->facility_id)->where("gate", $gate->gate)->delete();
        $license['license_plate'] = $request->transit['plate']['text'];
        $license['partner_id'] =  $gate->partner_id;
        //$license['make'] = $data->Make;
        //$license['model'] = $data->Model;
        $license['facility_id'] =  $gate->facility_id;
        $license['gate'] = $gate->gate;
        $license['gate_type'] = $gate->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
        $result = LicensePlate::create($license);
        return $result;
    }

    public function saveTownsendCheckin($request, $gate)
    {
        $this->log->info("saveTownsendCheckin before checkin");
        try {
            $queue_name = '';
            $gate_type = '';
            if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                $gate_type = $gate->gate_type;
            }

            $this->log->info("gate found");

            $msg['license_plate'] = $request->transit['plate']['text'];
            $msg['is_checkin_or_checkout'] = '1';
            $response_type = 1;
            $this->sendTownsendCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type);
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Townsend Tattile Info : Tattile Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            return true;
        }
    }


    public function saveTownsendCheckout($request, $gate)
    {

        $this->log->info("saveTownsnendCheckout checkout start");
        $queue_name = '';
        $gate_type = '';
        if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
            $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
            $gate_type = $gate->gate_type;
        }
        $this->log->info("saveTownsnendCheckout LPR before checkout");
        $msg = [];
        $msg['license_plate'] = $request->transit['plate']['text'];
        $msg['is_checkin_or_checkout'] = '0';
        $response_type = '1';
        $this->sendTownsendCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type);
        return true;
    }

    public function sendTownsendCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type)
    {
        $this->log->info("townsnend before queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['license_plate' => $msg['license_plate'], 'is_checkin_or_checkout' => $msg['is_checkin_or_checkout'], 'response_type' => $response_type]));
        $myArray = json_encode(['license_plate' => $msg['license_plate'], 'is_checkin_or_checkout' => $msg['is_checkin_or_checkout'], 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("townsend data send " . $queue_name);
        return true;
    }
}
