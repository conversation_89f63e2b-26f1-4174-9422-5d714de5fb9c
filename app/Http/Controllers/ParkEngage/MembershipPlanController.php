<?php

namespace App\Http\Controllers\ParkEngage;

use App\Models\Permission;
use App\Models\ParkEngage\Service;
use App\Models\ParkEngage\MembershipPlan;
use App\Models\ParkEngage\UserMembership;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\ServicePermission;
use App\Models\RateType;
use App\Models\PartnerRateType;
use App\Models\Facility;
use Auth;
use App\Models\PermitRateDescription;
use App\Models\ParkEngage\CustomerPermissionMaster;
use App\Models\ParkEngage\CustomerPortalPermission;
use App\Models\ParkEngage\CustomerPermission;
use App\Models\User;
use App\Services\LoggerFactory;
use App\Services\Mailers\UserMailer;


class MembershipPlanController extends Controller
{

  protected $mail;
  protected  $log;
  protected  $request;

  // How to setup for sending mail
  public function __construct(UserMailer $mail, Request $request, LoggerFactory $logFactory)
  {
    $this->mail = $mail;
    $this->log = $logFactory->setPath('logs/membershipplan')->createLogger('membership-plan');
    $this->request = $request;
  }

  public function index(Request $request)
  {

    if (Auth::user()->user_type == '1') {
      $partner_id = isset($request->partner_id) ? $request->partner_id : '';
    } elseif ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
      $partner_id = Auth::user()->created_by;
    } elseif (Auth::user()->user_type == '3') {
      $partner_id = Auth::user()->id;
    } else {
      $partner_id = isset($request->partner_id) ? $request->partner_id : '';
    }



    if ($request->sort != '') {
      if ($request->sort == 'full_name') {
        $membership = MembershipPlan::with('service', 'permission');
      } else {
        $membership = MembershipPlan::with('service', 'permission')->orderBy($request->sort, $request->sortBy);
      }
    } else {
      $membership = MembershipPlan::with('service', 'permission')->orderBy('id', 'Desc');
    }

    if (isset($partner_id) &&  $partner_id != '') {
      $membership_id = UserMembership::where('user_id', $partner_id)->pluck('membership_plan_id');
      $membership = $membership->whereIn('id', $membership_id);
    }


    if ($request->search) {

      if ($request->search == 'trial') {
        $request->search = 1;
      }
      if ($request->search == 'paid') {
        $request->search = 0;
      }
      $membership = QueryBuilder::buildSearchQuery($membership, $request->search, MembershipPlan::$searchFields)
        ->orWhereHas(
          'service',
          function ($query) use ($request) {
            $query
              ->where('full_name', 'like', "%{$request->search}%")
              ->orWhere('short_name', 'like', "%{$request->search}%");
          }
        );
      $membership = $membership->paginate(20);
      if ($request->sort == 'full_name') {
        if (count($membership) > 0) {
          if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
            for ($i = 0; $i < count($membership); $i++) {
              for ($j = $i + 1; $j < count($membership); $j++) {
                if ($membership[$i]['service']->full_name > $membership[$j]['service']->full_name) {
                  $temp = $membership[$i];
                  $membership[$i] = $membership[$j];
                  $membership[$j] = $temp;
                }
              }
            }
          } else {
            for ($i = 0; $i < count($membership); $i++) {
              for ($j = $i + 1; $j < count($membership); $j++) {
                if ($membership[$i]['service']->full_name < $membership[$j]['service']->full_name) {
                  $temp = $membership[$i];
                  $membership[$i] = $membership[$j];
                  $membership[$j] = $temp;
                }
              }
            }
          }
        }
      }
      return $membership;
    }

    $membership = $membership->paginate(20);
    if ($request->sort == 'full_name') {
      if (count($membership) > 0) {
        if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
          for ($i = 0; $i < count($membership); $i++) {
            for ($j = $i + 1; $j < count($membership); $j++) {
              if ($membership[$i]['service']->full_name > $requestDemo[$j]['service']->full_name) {
                $temp = $membership[$i];
                $membership[$i] = $membership[$j];
                $membership[$j] = $temp;
              }
            }
          }
        } else {
          for ($i = 0; $i < count($membership); $i++) {
            for ($j = $i + 1; $j < count($membership); $j++) {
              if ($membership[$i]['service']->full_name < $membership[$j]['service']->full_name) {
                $temp = $membership[$i];
                $membership[$i] = $membership[$j];
                $membership[$j] = $temp;
              }
            }
          }
        }
      }
    }
    return $membership;
  }

  public function store(Request $request)
  {
    $this->log->info("Log Create Permission Request :  " . json_encode($request->all()));
    $this->validate($request, MembershipPlan::$validation);

    $data['name'] = $request->name;
    $data['service_id'] = $request->service_id;
    $data['no_of_days'] = 0;
    $data['no_of_users'] = $request->no_of_users;
    $data['no_of_facility'] = $request->no_of_facility;
    $data['no_of_transaction'] = $request->no_of_transaction;
    $data['amount'] = $request->amount;
    $data['annual_amount'] = $request->annual_amount;
    $data['description'] = $request->description;
    $data['is_recommended'] = $request->is_recommended == 1 ? '1' : '0';

    $data['is_trial'] = $request->is_trial == 0 ? 0 : 1;
    $data['is_display'] = $request->is_display == 0 ? '0' : '1';
    $data['service_ids'] = $request->service_ids;
    $result = MembershipPlan::create($data);

    if ($request->is_recommended == 1) {
      MembershipPlan::where('service_id', $request->service_id)->where('id', '!=', $result->id)->update(['is_recommended' => '0']);
    }

    $permission = $request->permission;
    foreach ($permission as $key => $value) {
      $membership['membership_plan_id'] = $result->id;
      $membership['web'] = $value['web'];
      $membership['display_name'] = $value['display_name'];
      $membership['parent_id'] = $value['parent_id'];
      $membership['type'] = $value['type'];
      $membership['list_order'] = isset($value['list_order']) ? $value['list_order'] : 0;
      Permission::create($membership);
    }

    $rate_type_id = $request->rates;
    if (isset($rate_type_id) && !empty($rate_type_id)) {
      foreach ($rate_type_id as $k => $v) {
        $partnerRate['rate_type_id'] = $v;
        $partnerRate['membership_plan_id'] = $result->id;
        PartnerRateType::create($partnerRate);
      }
    }

    return $result;
  }

  public function update(Request $request)
  {
    $this->log->info("Log Update Permission Request :  " . json_encode($request->all()));
    $this->validate($request, MembershipPlan::$validation);

    $membershipPlan = MembershipPlan::with('users')->where('id', $request->id)->first();
    if (!$membershipPlan) {
      throw new NotFoundException('No membership plan id found.');
    }
    $usersMembership = UserMembership::where('membership_plan_id', $membershipPlan->id)->where('is_active', '1')->first();
    //if($usersMembership){
    //throw new ApiGenericException("Membership plan already allocate to partner. You can not update the membership plan.");
    //}
    $membershipPlan->name = $request->name;
    $membershipPlan->service_id = $request->service_id;
    $membershipPlan->no_of_days = 0;
    $membershipPlan->no_of_users = $request->no_of_users;
    $membershipPlan->no_of_facility = $request->no_of_facility;
    $membershipPlan->no_of_transaction = $request->no_of_transaction;
    $membershipPlan->amount = $request->amount;
    $membershipPlan->annual_amount = $request->annual_amount;
    $membershipPlan->description = $request->description;
    $membershipPlan->is_trial = $request->is_trial == 0 ? 0 : 1;
    $membershipPlan->status = $request->status == 0 ? '0' : '1';
    $membershipPlan->is_display = $request->is_display == 0 ? '0' : '1';
    $membershipPlan->is_recommended = $request->is_recommended == 1 ? '1' : '0';
    $membershipPlan->service_ids = $request->service_ids;
    $membershipPlan->save();

    if ($request->is_recommended == 1) {
      MembershipPlan::where('service_id', $request->service_id)->where('id', '!=', $membershipPlan->id)->update(['is_recommended' => '0']);
    }

    $permission = $request->permission;
    Permission::where('membership_plan_id', $membershipPlan->id)->delete();
    foreach ($permission as $key => $value) {
      $membership['membership_plan_id'] = $membershipPlan->id;
      $membership['web'] = $value['web'];
      $membership['display_name'] = $value['display_name'];
      $membership['parent_id'] = $value['parent_id'];
      $membership['type'] = $value['type'];
      $membership['list_order'] = isset($value['list_order']) ? $value['list_order'] : 0;
      Permission::create($membership);
    }

    $rate_type_id = $request->rates;

    if (isset($rate_type_id) && !empty($rate_type_id)) {
      PartnerRateType::where('membership_plan_id', $membershipPlan->id)->delete();
      foreach ($rate_type_id as $k => $v) {
        $partnerRate['rate_type_id'] = $v;
        $partnerRate['membership_plan_id'] = $membershipPlan->id;
        PartnerRateType::create($partnerRate);
      }
    }

    return $membershipPlan;
  }

  public function membershipPlanDetails($id)
  {
    return MembershipPlan::with(['service', 'permission', 'permissionRates'])->where('id', $id)->first();
  }

  public function delete($id)
  {
    $membership = MembershipPlan::find($id);
    if ($membership) {
      $users_count = $membership->users->count();
      if ($users_count === 0) {
        if ($membership->delete()) {
          return "Data successfully deleted.";
        }
      }
    }
    throw new ApiGenericException("Membership plan already allocate to partner. You can not remove membership plan.");
  }

  public function getServicePermissions(Request $request)
  {

    $default = ServicePermission::where('is_default', '1')->orderBy('type', 'ASC')->orderBy('list_order', 'ASC')->get();
    $new = [];
    $permissions = [];
    foreach ($default as $key => $value) {
      if ($value->type == '0') {
        $permissions["Main Menu"][] = $value;
      }

      if ($value->type == '1') {
        $permissions["Permission"][] = $value;
      }
      if ($value->type == '2') {
        $permissions["Tabs"][] = $value;
      }
      if ($value->type == '3') {
        $permissions["Actions"][] = $value;
      }
    }

    $new['General'] = $permissions;
    $allPer = [];
    //dd($permissions);
    $permission = ServicePermission::whereIn('service_id', $request->ids)->orderBy('type', 'ASC')->orderBy('list_order', 'ASC')->get();

    foreach ($request->ids as $id) {
      $i = 1;
      foreach ($permission as $key => $value) {

        $service = Service::find($id);
        if ($value->service_id == $id) {
          if ($value->type == '0') {
            $new[$service->full_name]["Main Menu"][] = $value;
          }
          if ($value->type == '1') {
            $new[$service->full_name]["Permission"][] = $value;
          }
          if ($value->type == '2') {
            $new[$service->full_name]["Tabs"][] = $value;
          }
          if ($value->type == '3') {
            $new[$service->full_name]["Actions"][] = $value;
          }
        }
      }
      //dd($allPer);
      //$new[] = $allPer;  
    }
    $arr = [];
    $new['rateType'] = RateType::all();
    $arr[] = $new;
    return $arr;
  }

  public function getMemberPlanByPlanId($facilityId, $id)
  {
    $rateType = [];
    $facility = Facility::where('id', $facilityId)->first();
    $partner = PartnerRateType::where('membership_plan_id', $id)->get();

    if ($partner) {
      $rateTypeId = [];
      foreach ($partner as $key => $value) {
        $rateTypeId[] = $value->rate_type_id;
      }
      $rateType =  RateType::with(
        ['rates' => function ($query) use ($facilityId) {
          $query->where('facility_id', $facilityId)->orderby('description', 'ASC');
        }]
      )->whereIN('id', $rateTypeId)->get();
      //handle rate id date changes
      if (!empty($rateType)) {
        $this->getRateApplicableDate($rateType);
      }
      //handle rate id date changes
      return $rateType;
    } else {

      $membership = UserMembership::where("user_id", $facility->owner_id)->first();
      $rateTypeId = [];
      if ($membership) {
        $permission = Permission::where("membership_plan_id", $membership->membership_plan_id)->where("type", '4')->get();
        /*
				if(count($permission) > 0){
					foreach($permission as $key=>$value){
						$rateTypeId[] = $value->parent_id;
					}
				}
				*/
        if (count($permission) > 0) {
          foreach ($permission as $key => $value) {
            $rateTypeId[] = $value->rate_type_id;
          }
        }
      }
      $rateType =  RateType::with(
        ['rates' => function ($query) use ($facilityId) {
          $query->where('facility_id', $facilityId)->orderby('description', 'ASC');
        }]
      )->whereIn("id", $rateTypeId)->get();

      //handle rate id date changes
      if (!empty($rateType)) {
        $this->getRateApplicableDate($rateType);
      }
      //handle rate id date changes

      return $rateType;
    }
  }

  public function getTabByFacilityId($facilityId)
  {
    $facility = Facility::where('id', $facilityId)->first();
    if (!$facility) {
      throw new NotFoundException('Facility Not found.');
    }
    $membership = UserMembership::where("user_id", $facility->owner_id)->first();

    if (!$membership) {
      throw new NotFoundException('Membership Plan Not found.');
    }

    $permission = Permission::where("membership_plan_id", $membership->membership_plan_id)->where("type", '2')->where("parent_id", '27')->get();
    if (!$permission) {
      throw new NotFoundException('Tab Not found.');
    }
    return $permission;
  }

  public function getTabMenu($partner_id, $flag = 0)
  {

    $membership = UserMembership::where("user_id", $partner_id)->first();

    if (!$membership) {
      throw new NotFoundException('Membership Plan Not found.');
    }
    if ($flag == '1') {

      //$permission = Permission::where("membership_plan_id", '14')->where("type", 2)->where("parent_id", 31)->orderBy('id', 'asc')->get();
      $permission = Permission::where("membership_plan_id", $membership->membership_plan_id)->where("type", 2)->where("parent_id", config('parkengage.MENU_TAB_PARENT_ID'))->orderBy('id', 'asc')->get();
    } else {
      $permission = Permission::where("membership_plan_id", $membership->membership_plan_id)->where("type", 2)->where("parent_id", config('parkengage.MENU_TAB_PARENT_ID'))->orderBy('id', 'asc')->get();
    }
    if (!$permission) {
      throw new NotFoundException('Tab Not found.');
    }
    if ($flag == '1') {
      foreach ($permission as $key => $val) {
        if ($val['slug'] == "check-in-out") {
          $val['display_name'] = "Check-In Against Booking";
          $val['slug'] = "check-in-against-booking";
        }
        if ($val['slug'] == "offline-check-outs") {
          $val['display_name'] = "Checkout From Admin";
          $val['slug'] = "checkout-from-admin";
          // unset($permission[$key]); 
        }
        if ($val['slug'] == "drive-up") {
          $val['display_name'] = "Transient Checkins";
          $val['slug'] = "transient-checkins";
        }
      }
    }
    $tab = [$permission];
    return $tab;
  }

  public function getCustomerPermissions(Request $request)
  {
    //return Auth::user()->id;
    $default = CustomerPermissionMaster::orderBy('type', 'ASC')->orderBy('list_order', 'ASC')->get();
    $new = [];
    $permissions = [];
    foreach ($default as $key => $value) {
      if ($value->type == '0') {
        $permissions["Main Menu"][] = $value;
      }

      if ($value->type == '1') {
        $permissions["Account Menu"][] = $value;
      }
      if ($value->type == '2') {
        $permissions["Access Permissions"][] = $value;
      }
      if ($value->type == '3') {
        $permissions["Restricted Permissions"][] = $value;
      }
    }
    $arr[] = $permissions;
    return $arr;
  }

  public function saveCustomerPermission(Request $request)
  {
    if ($request->rm_id) {
      $userID = $request->rm_id;
      $permissionExist = CustomerPermission::where('partner_id', $request->partner_id)->where('rm_id', $request->rm_id)->first();
    } else {
      $userID = $request->partner_id;
      $permissionExist = CustomerPermission::where('partner_id', $request->partner_id)->whereNull('rm_id')->first();
    }

    if ($permissionExist) {
      throw new ApiGenericException('Customer Permission Already Exists.');
    }

    $data['title'] = $request->title;
    $data['partner_id'] = $request->partner_id;
    $data['rm_id'] =  (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
    $data['created_at'] = date('Y-m-d H:i:s');
    $data['updated_at'] = date('Y-m-d H:i:s');
    $result = CustomerPermission::create($data);

    $permission = $request->permission;
    foreach ($permission as $key => $value) {
      $membership['partner_id'] = $request->partner_id;
      $membership['title'] = $request->title;
      $membership['rm_id'] =  (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
      $membership['customer_permission_id'] =  $result->id;
      $membership['name'] = $value['name'];
      $membership['web'] = $value['web'];
      $membership['display_name'] = $value['display_name'];
      $membership['type'] = $value['type'];
      $membership['list_order'] = isset($value['list_order']) ? $value['list_order'] : 0;
      $membership['is_default'] = isset($value['is_default']) ? $value['is_default'] : 0;
      CustomerPortalPermission::create($membership);
    }
    $permission = CustomerPortalPermission::where('customer_permission_id', $result->id)->get();
    $result['permission'] = $permission;
    return $result;
  }

  public function customerPermissionDetails($id)
  {

    $permissionExist = CustomerPermission::where('id', $id)->first();
    if (!$permissionExist) {
      throw new ApiGenericException('No Record Found.');
    }
    $permission = CustomerPortalPermission::where('customer_permission_id', $id)->get();
    $permissionExist['partner_id'] = $permission[0]->partner_id;
    $permissionExist['rm_id'] = isset($permission[0]->rm_id) ? $permission[0]->rm_id : '';
    $permissionExist['permission'] = $permission;
    return $permissionExist;
  }

  public function updateCustomerPermission(Request $request)
  {

    $permissionExist = CustomerPermission::where('id', $request->id)->first();

    CustomerPortalPermission::where('customer_permission_id', $permissionExist->id)->delete();

    $permission = $request->permission;
    foreach ($permission as $key => $value) {
      $membership['partner_id'] = $request->partner_id;
      $membership['title'] = $request->title;
      $membership['rm_id'] = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
      $membership['customer_permission_id'] =  $permissionExist->id;
      $membership['name'] = $value['name'];
      $membership['web'] = $value['web'];
      $membership['display_name'] = $value['display_name'];
      $membership['type'] = $value['type'];
      $membership['list_order'] = isset($value['list_order']) ? $value['list_order'] : 0;
      $membership['is_default'] = isset($value['is_default']) ? $value['is_default'] : 0;
      CustomerPortalPermission::create($membership);
    }
    $permission = CustomerPortalPermission::where('customer_permission_id', $permissionExist->id)->get();
    $permissionExist['permission'] = $permission;
    return $permissionExist;
  }

  public function destroyCustomerPermissionDetails($id)
  {
    $result = CustomerPermission::find($id);

    if ($result) {
      $permission = CustomerPortalPermission::where('customer_permission_id', $result->id)->delete();
      $result->delete();
      //return "Card Successfully Deleted.";
      return response(
        [
          "message" => 'Customer Permission Successfully Deleted.'
        ]
      );
    }
    throw new ApiGenericException("No Record Found.");
  }

  public function getRateApplicableDate($rateType)
  {
    //handle rate id date changes

    $rateid = [];
    if (count($rateType) > 0) {
      foreach ($rateType[0]['rates'] as $key) {
        $rateid[] = $key['id'];
      }

      $resultArray = \DB::table('rate_definition_date')->whereIn('rate_id', $rateid)->get();
      // $data = json_decode($resultArray, true)['data'];
      $groupedData = [];
      foreach ($resultArray as $item) {
        $rateId = $item->rate_id;
        if (!isset($groupedData[$rateId])) {
          $groupedData[$rateId] = [
            'rate_id' => $rateId,
            'rate_start_date' => [],
            'rate_date_type' => $item->day_type
          ];
        }
        $groupedData[$rateId]['rate_start_date'][] = [
          'start_date' => $item->rate_start_date,
          'end_date' => $item->rate_end_date
        ];
      }
      $getRateDateArray =  array_values($groupedData);
      //add Key in $result Array if found in $getRateDateArray
      foreach ($getRateDateArray as $item2) {
        $rateId = $item2['rate_id'];
        foreach ($rateType[0]['rates'] as &$item1) {
          if ($item1['id'] == $rateId) {
            // Merge the rate_start_date information from $a2 to $a1
            $item1['custom_date'] = $item2['rate_start_date'];
            $item1['day_type'] = $item2['rate_date_type'];
            break;
          }
        }
      }
    }


    //end changes for rate band
  }
}
