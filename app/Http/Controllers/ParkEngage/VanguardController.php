<?php
    namespace App\Http\Controllers\ParkEngage;


/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/



use App\Classes\CommonFunctions;
use App\Exceptions\ApiGenericException;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Models\ParkEngage\RevpassLicensePlate;
use App\Models\Ticket;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use GuzzleHttp\Client;

class VanguardController extends Controller
{
    protected $vanGarurd;

    public function __construct(LoggerFactory $logFactory)
    {
        $this->vanGarurd = $logFactory->setPath('logs/vanGarurd')->createLogger('vanGarurd');
    }

    public function getRportUrl()
    {
        $user = Auth::user();
        $vanguard_username = $user->vanguard_username;
        $vanguard_password = $user->vanguard_password;

        if (!empty($vanguard_username) && !empty($vanguard_password)) {
            $client = new Client();

            try {
                $response = $client->post('https://api.vparking.co/auth/login', [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'email' => $vanguard_username,
                        'password' => $vanguard_password,
                    ],
                ]);

                $body = $response->getBody();
                $data = json_decode($body, true);

                // Check if access_token is present in response
                if (isset($data['access_token'])) {
                    $reportUrl = "https://app.payrevpass.com/dashboard?token=" . $data['access_token'];

                    return $reportUrl;
                }
                throw new ApiGenericException("No report found for this user, Please contact site administrator!");
            } catch (\Exception $e) {
                throw new ApiGenericException($e->getMessage());
            }
        }
        throw new ApiGenericException("No report found for this user, Please contact site administrator!");
    }

    public function handleVanGaurdianTicket($payload)
    {
        try {
            $this->vanGarurd->info("Handling VanGuardian ticket", ['payload' => $payload]);

            $licensePlate = $payload['vehicle']['plate']['lpn'] ?? null;
            if (!$licensePlate) {
                $this->vanGarurd->warning("License plate not found in payload", ['payload' => $payload]);
                return null;
            }

            $partnerId = config('parkengage.PARTNER_RevPass');
            $facilityId = config('parkengage.REVPASS_FACILITY');

            // Check for existing active ticket
            $existingTicket = Ticket::where([
                'license_plate' => $licensePlate,
                'facility_id'   => $facilityId,
                'partner_id'    => $partnerId,
                'is_checkout'   => 0
            ])->first();

            if ($existingTicket) {
                $this->vanGarurd->info("Duplicate ticket found", ['ticket_number' => $existingTicket->ticket_number]);
                return $existingTicket;
            }

            // Create new ticket
            $ticket = new Ticket();
            $ticket->license_plate = $licensePlate;
            $ticket->ticket_number = CommonFunctions::checkTicketNumber();
            $ticket->facility_id = $facilityId;
            $ticket->partner_id = $partnerId;
            $ticket->save();

            $this->vanGarurd->info("New ticket created", ['ticket_number' => $ticket->ticket_number]);

            return $ticket;
        } catch (\Exception $e) {
            $this->vanGarurd->error("Error handling VanGuardian ticket", ['error' => $e->getMessage()]);
            return null;
        }
    }




    public function handleVanGaurdianFeed(Request $request)
    {

        $this->vanGarurd->info("handleVanGaurdianFeed request: " . json_encode($request->all()));

        $event_type = $request->event_type;
        // $payload = $request->data;

        switch ($event_type) {
            case 'vehicle.entered':
                // $response = $this->handleVanGaurdianTicket($payload);
                // dd($request->all());
                $response = RevpassLicensePlate::fromApiPayload($request->all());
                return  response()->json(['status' => 200, 'data' => $response]);
                break;
            case 'vehicle.exited':
                // $this->handleVanGaurdianCheckout($payload);
                break;
            default:
                break;
        }
    }
}
