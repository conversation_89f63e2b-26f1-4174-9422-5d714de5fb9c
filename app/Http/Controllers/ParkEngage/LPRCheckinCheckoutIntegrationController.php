<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Auth;
use Response;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Models\UserPass;
use Artisan;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\OauthClient;
use App\Classes\DatacapPaymentGateway;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\WorldportLicensePlate;
use App\Models\ParkEngage\UsmLicensePlate;
use App\Models\ParkEngage\LprPayload;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
use App\Classes\HeartlandPaymentGateway;
use DateTime;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\TicketExtend;

class LPRCheckinCheckoutIntegrationController extends Controller
{

    protected $log;
    protected $user;
    protected $facility;
    protected $partner_id;

    const  PARTNER_ID = 363361;

    use DispatchesJobs;
    const QUEUE_NAME = 'sms-send';
    const QUEUE_ENTRY = 'read-license-plate';

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;

        $this->log = $logFactory->setPath('logs/lpr')->createLogger('autostart');
    }


    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                config(['app.timezone' => $facility->timezone]);
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                config(['app.timezone' => $partnerTimezone->timezone]);
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }

    public function thirdPartyCheckinCheckout(Request $request)
    {

        $this->log->info("thirdPartyCheckinCheckout Request received -- " . json_encode($request->all()));
        $this->log->info('Secret : ' . $request->secret);
        $this->log->info('remote IP : ' . $request->ip());

        $currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        $oauthClient = OauthClient::where('secret', $request->secret)->first();

        if (is_null($oauthClient)) {
            $this->log->info("TPCC: Invalid Secret");
            $this->throwException('Invalid Secret', 422);
        }
        $this->partner_id= $oauthClient->partner_id;

        if (isset($request->LocationGroupId)) {   //ENTRY REQUEST
            $lane_id = $request->LocationGroupId;
            $api_type = "0";
        } else if (isset($request->Location)) { 
            $lane_id = $request->Location;
            $api_type = "1";
        }else{
            $lane_id = '';
            $api_type = "0";
        }
        
        try{
            $lprPayload = new LprPayload;
            $lprPayload->feed_time      = isset($request->TimeStamp) ? date("Y-m-d H:i:s", strtotime($request->TimeStamp)) : ''; 
            $lprPayload->location       = $lane_id; 
            $lprPayload->license_plate  = isset($request->Data[0]['PlateNumber']) ? $request->Data[0]['PlateNumber'] : ''; 
            $lprPayload->state          = isset($request->Data[0]['PlateState']) ? $request->Data[0]['PlateState'] : ''; 
            $lprPayload->session_id     = isset($request->Data[0]['ParkingSessionId']) ? $request->Data[0]['ParkingSessionId'] : ''; 
            $lprPayload->api_type       = $api_type; 
            $lprPayload->save();
        }catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received for lpr_payload insert --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>','<EMAIL>'])->subject("LPR Info : LPR Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->throwException('Error received,Please try again', 422);
            return true;
        }

        if(isset($request->TimeStamp) && !empty($request->TimeStamp)){
            // Validate the timestamp
            if (!$this->isValidUTCTimestamp($request->TimeStamp)) {
                $this->log->info("TPCC: Please provide the correct Timestamp!");
                $this->throwException('Please provide the correct Timestamp!', 422);
            }
        }else{
            $this->log->info("TPCC: Please provide the Timestamp");
            $this->throwException('Please provide the Timestamp', 422);
        }

        if(isset( $request->Data[0]['ParkingSessionId']) && !empty( $request->Data[0]['ParkingSessionId'])){
        }else{
            $this->log->info("TPCC: Please provide the ParkingSessionId");
            $this->throwException('Please provide the ParkingSessionId', 422);
        }

        if (isset($request->LocationGroupId)) {   //ENTRY REQUEST
            $lane_id = $request->LocationGroupId;
            if(isset($request->Data[0]['PlateNumber']) && !empty($request->Data[0]['PlateNumber'])){
                $plate_number = $request->Data[0]['PlateNumber'];
            }else{
                $this->log->info("TPCC: Please provide the license plate");
                $this->throwException('Please provide the license plate', 422);
            }
        } else if (isset($request->Location)) {    //EXIT REQUEST
            $lane_id = $request->Location;
            /* if (isset($request->Data[0]['Associations'][0]['PlateNumber']) && !empty($request->Data[0]['Associations'][0]['PlateNumber'])) {
                $plate_number = $request->Data[0]['Associations'][0]['PlateNumber'];
                if (isset($request->Data[0]['Associations'][0]['PurchaseDate']) && !empty($request->Data[0]['Associations'][0]['PurchaseDate']) && !$this->isValidUTCTimestamp($request->Data[0]['Associations'][0]['PurchaseDate'])) {
                    $this->log->info("TPCC: Please provide the correct PurchaseDate !");
                    $this->throwException('Please provide the correct PurchaseDate!', 422);
                }
                if (isset($request->Data[0]['Associations'][0]['ExpirationDate']) && !empty($request->Data[0]['Associations'][0]['ExpirationDate']) && !$this->isValidUTCTimestamp($request->Data[0]['Associations'][0]['ExpirationDate'])) {
                    $this->log->info("TPCC: Please provide the correct ExpirationDate !");
                    $this->throwException('Please provide the correct ExpirationDate!', 422);
                }

            } */
            if (isset($request->Data[0]['PlateNumber']) && !empty($request->Data[0]['PlateNumber'])) {
                $plate_number = $request->Data[0]['PlateNumber'];
            } else {
                $this->log->info("TPCC: Please provide the license plate");
                $this->throwException('Please provide the license plate', 422);
            }
        } else {
            $this->log->info("TPCC: Lane Id not matched");
            $this->throwException('Lane Id not matched', 422);
        }

        $lane_id = explode(',', $lane_id);
        if (isset($request->LocationGroupId)) {
            $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->whereIn("lane_id", $lane_id)->where("gate_type", 'entry')->first();
        } else if (isset($request->Location)) {
            $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->whereIn("lane_id", $lane_id)->where("gate_type", 'exit')->first();
            $lastTicket = Ticket::where('license_plate', $plate_number)->orderBy("id", "DESC")->first();
            if ($lastTicket) {
                $checkin_facility = $lastTicket->facility_id;
                $gateCheckin = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("facility_id", $checkin_facility)->where("gate_type", 'exit')->first();        
                if($gateCheckin){
                    if (in_array($gateCheckin->lane_id, $lane_id)){
                        $gate = $gateCheckin;
                    }
                }    
            }
        }else {
            $this->log->info("TPCC: Invalid Gate");
            $this->throwException('Invalid Gate', 422);            
        }   
        if ($gate) {
            $this->setCustomTimezone($gate->facility_id);
            $this->log->info("Request : " . date("Y-m-d H:i:s"));

            $facility = Facility::find($gate->facility_id);
            if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
               // $this->log->info("TPCC: lpr is disabled");
                //$this->throwException('LPR is disabled', 422);            
                //return true;
            }
            $todaysDate = Carbon::today()->toDateString();

            if (isset($request->LocationGroupId)) { // checkin section 
                //$lastTicket = Ticket::where("facility_id", $gate->facility_id)->where("is_checkin", '1')->where("is_checkout", '0')->where("license_plate", $plate_number)->whereNull('stop_parking_time')->orderBy("id", "DESC")->first();
                 #comment above for multi laneid
                
                #pims-12870 pave integration filter multi feed for same lp and also for campus grouping
                if(!is_null($facility->neighborhood_id) && $facility->neighborhood_id > 0){
                    $neighborhoodIds   = Facility::where('neighborhood_id',$facility->neighborhood_id)->pluck('id');
                }else{
                    $neighborhoodIds   = [];
                }  

                if(isset($facility)){
                    $alreadyCheckin = $this->alreadyCheckinStatus($plate_number, $gate, $neighborhoodIds);
                    if ($alreadyCheckin) {
                        $this->log->info("TPCC: You have already checked-in: ".$plate_number);
                        //send text if received lpr checkin after user session started but after 15minute of lpr feed timestamp
                        //point 1 mail
                        $checkinTime = date("Y-m-d H:i:s", strtotime($request->TimeStamp));
                        $timestampPlus15 = Carbon::parse($checkinTime)->addMinutes(15);
                        $this->log->info("TPCC: You have already checked-in  BEFORE SENDSMS: ".$timestampPlus15);
                        if ((is_null($alreadyCheckin->lpr_session_id) || empty($alreadyCheckin->lpr_session_id)) && Carbon::parse($alreadyCheckin->scan_date)->greaterThan($timestampPlus15)) { 
                            $this->log->info("TPCC: You have already checked-in SENDSMS: ".$plate_number);
                            if($lprPayload){
                                $lprPayload->ticket_number = $alreadyCheckin->ticket_number;
                                $lprPayload->save();
                            }
                            $link="https://preprod-transient.parkengage.com/usm/uc/".$alreadyCheckin->ticket_number;
                            $sms_msg = "we think your ticket ".$alreadyCheckin->ticket_number." session starts from  ".$checkinTime." instead ".$alreadyCheckin->scan_date." Please click on link for your consent ".$link;
                            dispatch((new SendSms($sms_msg, $alreadyCheckin->user->phone))->onQueue(self::QUEUE_NAME));
                        }
                        $this->throwException('You have already checked-in', 422); 
                    }
                    $alreadyOpenTicket = $this->alreadyOpenTicketStatus($plate_number, $gate, $request->TimeStamp, $neighborhoodIds);
                    if ($alreadyOpenTicket) {
                        $this->log->info("You have already open ticket.");
                        $this->throwException('You have already open ticket', 422);
                    }
                    //If we receive autostart after the user session has ended we can ignore the autostart altogether. For point 2 
                    $checkUserActiveSession = $this->checkUserCheckoutSession($plate_number, $gate, $request->TimeStamp, $neighborhoodIds);
                    if ($checkUserActiveSession) {
                        $this->log->info("Ticket was already created for this checkin.");
                        $this->throwException('Ticket was already created for this checkin', 422);
                    }
                }

                /* $lastTicket = Ticket::where("is_checkin", '1')->where("is_checkout", '0')->where("license_plate", $plate_number)->whereNull('stop_parking_time')->orderBy("id", "DESC")->first();
                if ($lastTicket) {
                    if($lastTicket->facility->neighborhood_id)
                    $this->log->info("TPCC: Duplicate Feed");
                    $this->throwException('Duplicate Feed', 422);            
                } */
                $this->saveLicensePlate($request, $gate);
                $this->saveCheckin($request, $gate, $neighborhoodIds);
                $this->log->info("Checkin Done");
            } else if (isset($request->Location)) {
                // check-out 
                /* $lastTicket = Ticket::where("facility_id", $gate->facility_id)->where("license_plate", $plate_number)->whereDate("created_at", '=', $todaysDate)->whereNull('stop_parking_time')->orderBy("id", "DESC")->first();
                if (!$lastTicket) {
                    $this->log->info("TPCC: No checkin found against this license plate");
                    $this->throwException('No checkin found against this license plate', 422);            
                } */

                $this->saveCheckout($request, $gate);
                $this->log->info("Checkout Done");
            } else {
                $this->log->info("TPCC: Invalid Gate");
                $this->throwException('Invalid Gate', 422);            
            }
        } else {
            $this->log->info("TPCC: Lane Id not matched");
            $this->throwException('Lane Id not matched!', 422);            
        }
        return "Success";
    }

   /*  public function tplLprCheckout(Request $request)
    {

        $this->log->info("tplLprCheckout Request received --" . json_encode($request->all()));
        $currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        $oauthClient = OauthClient::where('secret', $request->secret)->first();

        if (is_null($oauthClient)) {
            $this->throwException('Invalid Secret', 422);
        }

        $lane_id = $request->Location;
        $associations = isset($request->Data[0]['Associations']) ? $request->Data[0]['Associations'] : '';

        if ($associations && isset($request->Data[0]['Associations'][0]['PlateNumber'])) {
        } else {
            $this->log->info("Please provide the license plate!");
            $this->throwException('Please provide the license plate!', 422);
        }

        $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $lane_id)->first();
        if ($gate) {
            $this->setCustomTimezone($gate->facility_id);
            $this->log->info("Request : " . date("Y-m-d H:i:s"));

            $facility = Facility::find($gate->facility_id);
            if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
                $this->log->info("lpr is disabled");
                // return true;
            }

            if ($gate->gate_type == 'exit') {
                $this->saveCheckout($request, $gate);
                //return "Checkout Done";
                $this->log->info("Checkout Done");
            } else {
                $this->log->info("Invalid Gate");
                return "Invalid Gate";
            }
        } else {
            $this->log->info("Lane Id not matched");
            return "Lane Id not matched";
        }
        return "Success";
    } */


    public function saveCheckin($request, $gate, $neighborhoodIds)
    {
        $this->log->info("before checkin");
        try {
            $queue_name = '';
            $gate_type = '';
            $gate_type = $gate->gate_type;
            $plate_number   = $request->Data[0]['PlateNumber'];
            $timestamp      = $request->TimeStamp;
            $lpr_session_id = $request->Data[0]['ParkingSessionId'];
            $checkinTime = date("Y-m-d H:i:s", strtotime($timestamp));
            $checkinDate = date("Y-m-d", strtotime($timestamp));
            $ticket = new Ticket();
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            $response_type = '0';
            $msg = [];
            $isPermit = 0;
            //$ticket->facility_id = NULL;
            $ticket->facility_id = $facility->id;
            if($facility->facilityConfiguration->is_check_cloud_permit == '1'){
                //check permit exist
                $permit = $this->checkExistingPermit($plate_number, $gate->facility_id,$gate->partner_id,$checkinDate, $neighborhoodIds);
                if(count($permit) > 0){
                    $isPermit = 1;
                    $ticket->facility_id = $permit->facility_id;
                    $ticket->permit_request_id = $permit->id;
                    $ticket->user_id = $permit->user_id;
                }
            }

            if($isPermit!= 1){
               $pass = $this->checkExistingPass($plate_number, $gate->facility_id,$gate->partner_id,$checkinDate, $neighborhoodIds);
                if(count($pass) > 0){
                    $isPermit = 1;
                    $ticket->facility_id = $pass->facility_id;
                    $ticket->user_pass_id = $pass->id;
                    $ticket->user_id = $pass->user_id;
                }
            }

            
            $ticket->check_in_datetime = $checkinTime;
            $ticket->scan_date = Carbon::parse('now');
            $ticket->checkin_time = $checkinTime;
            $ticket->is_checkin = '0';

            $reservation = [];
            if($isPermit != 1){
                $reservation = $this->checkExistingReservation($plate_number, $gate->facility_id,$gate->partner_id, $checkinTime, $neighborhoodIds);
                if (count($reservation) > 0) {
                    $response_type = '1';
                    $this->log->info("reservation checkin");
                    $resevationExitTime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                    $resevationExitTime = Carbon::parse($resevationExitTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
                    $ticket->reservation_id = $reservation->id;
                    $ticket->check_in_datetime = $reservation->start_timestamp;
                    $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->estimated_checkout = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->payment_date = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->length = $reservation->length;
                    $ticket->is_checkin = '1';
                    $ticket->facility_id = $reservation->facility_id;
                    $ticket->user_id = $reservation->user_id;
                    $ticket->session_id = $reservation->session_id;
                    
                    $this->log->info("reservation check : " . json_encode($msg));
                }
            }
            
            if($isPermit == 1){
                $ticket->is_checkin = '1';
                $response_type = '1';
            }

            //$ticket->facility_id = $gate->facility_id;
            $ticket->partner_id = $gate->partner_id;
            $ticketNumber = $this->checkTicketNumber($gate->facility_id);
            $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
            if ($isExist) {
                $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
            } else {
                $ticket->ticket_number = $ticketNumber;
            }
            $ticket->license_plate = $plate_number;
            $ticket->checkin_gate = $gate->gate;
            $ticket->device_type = "LPR";
            $ticket->lpr_session_id = $lpr_session_id;
            $ticket->save();

            if (count($reservation) > 0) {
                $reservation->is_ticket = '1';
                $reservation->save();
            }
            $this->log->info("checkin done {$ticket->ticket_number}");

            if ($ticket->user_id != '') {
                $user = User::find($ticket->user_id);
                if ($user->email != '') {
                    //Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                }

                /* $facilityName = ucwords($ticket->facility->full_name);
                //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                if ($partnerDetails->user_id == self::PARTNER_ID) {
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }
                $url = env('TOUCHLESS_WEB_URL');
                $grace_period = $facility->grace_period_minute;
                $ticket_number = base64_encode($ticket->ticket_number);
                $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                if ($isPermit == 1) {
                    $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number.";
                } */
                //dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
            }
            return "Success";
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>'])->subject("LPR Info : LPR Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->throwException('Error received,Please try again', 422);
            return true;
        }
    }

    public function saveCheckout($request, $gate)
    {
        $this->log->info("checkout start");
        $queue_name = '';
        $gate_type = '';
        $lpr_session_id     = $request->Data[0]['ParkingSessionId'];
        $timestamp          = $request->TimeStamp;
        $facility   = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
        /*  $lprLicensePlate = UsmLicensePlate::where('lpr_session_id', $lpr_session_id)->first();
        if ($lprLicensePlate) {
           $plate_number = $lprLicensePlate->license_plate;
        }else{
            return "No License Plate Found";
        } */

        /* 
        $associations = isset($request->Data[0]['Associations']) ? $request->Data[0]['Associations'] : '';
        if ($associations && isset($request->Data[0]['Associations'][0]['PlateNumber'])) {
            $plate_number = $request->Data[0]['Associations'][0]['PlateNumber'];
        } else {
            $this->log->info("Save checkout: Please provide the license plate!");
            $this->throwException('Please provide the license plate!', 422);
        } */
        if (isset($request->Data[0]['PlateNumber']) && !empty($request->Data[0]['PlateNumber'])) {
            $plate_number = $request->Data[0]['PlateNumber'];
        } else {
            $this->log->info("Save checkout: Please provide the license plate!");
            $this->throwException('Please provide the license plate!', 422);
        }
        // dd($gate);
        /* if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
            $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
            $gate_type = $gate->gate_type;
        } */

        $current_time = date('Y-m-d H:i:s');
        
        //$ticket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation', 'user'])->where('license_plate', $plate_number)->where('facility_id', $gate->facility_id)->orderBy("id", "DESC")->first();
        #comment above for multi laneid
        if(!is_null($facility->neighborhood_id) && $facility->neighborhood_id > 0){
            $neighborhoodIds   = Facility::where('neighborhood_id',$facility->neighborhood_id)->pluck('id');
            $ticket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation', 'user'])->whereIn('facility_id',$neighborhoodIds)->where('license_plate', $plate_number)->whereNotNull("lpr_session_id")->orderBy("id", "DESC")->first();
        }else{
            $neighborhoodIds   = [];
            $ticket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation', 'user'])->where('license_plate', $plate_number)->whereNotNull("lpr_session_id")->orderBy("id", "DESC")->first();
        }        

        
        if ($ticket) {
            /*  if ($ticket->is_transaction_status == '1') {
                $this->log->info("Payment already in process " . $ticket->ticket_number);
                return true;
            } */
            $is_our_ticket = '1';
            if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0') {
                $is_our_ticket = '0';
            }
            $this->log->info("checkout time ticket -- " . $ticket->id);
            $checkinDate = date("Y-m-d", strtotime($ticket->checkin_time));

            $today = date("Y-m-d", strtotime($timestamp));
            $checkoutTime = date("Y-m-d H:i:s", strtotime($timestamp));

            if(strtotime($ticket->checkin_time) >= strtotime($checkoutTime)){
                $this->log->info("checkin_time is greater or equal to checkouttime -- " . $checkoutTime);
                $this->throwException('Checkout time is lesser than checkin time', 422);
            }

            /* if(isset($request->Data[0]['Associations'][0]['PurchaseDate']) && !empty($request->Data[0]['Associations'][0]['PurchaseDate'])){
                $PurchaseDate = date("Y-m-d H:i:s", strtotime($request->Data[0]['Associations'][0]['PurchaseDate']));
                if(strtotime($ticket->checkin_time) > strtotime($PurchaseDate)){
                    $this->log->info("checkin_time is greater or equal to PurchaseDate -- " . $PurchaseDate);
                    $this->throwException('PurchaseDate is lesser than checkin time', 422);
                }
            } */

            /* if(isset($request->Data[0]['Associations'][0]['ExpirationDate']) && !empty($request->Data[0]['Associations'][0]['ExpirationDate'])){
                $ExpirationDate = date("Y-m-d H:i:s", strtotime($request->Data[0]['Associations'][0]['ExpirationDate']));
                if(strtotime($ticket->checkin_time) >= strtotime($ExpirationDate)){
                    $this->log->info("checkin_time is greater or equal to ExpirationDate -- " . $ExpirationDate);
                    $this->throwException('ExpirationDate is lesser than checkin time', 422);
                }
            } */
            
            //permit check before checkout

            if($ticket->permit_request_id != ''){
                $ticket->is_checkout = '1';
                $ticket->is_closed = '1';
                $ticket->checkout_gate = $gate->gate;
                $ticket->checkout_facility_id = $gate->facility_id;
                $ticket->stop_parking_time      = $checkoutTime;
                $ticket->checkout_time = $checkoutTime;
                $details->closed_date = $current_time;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->estimated_checkout = $checkoutTime;
                $ticket->payment_date = $checkoutTime;
                $ticket->checkout_license_plate = $plate_number;
                $ticket->checkout_session_id = $ticket->session_id;
                $ticket->checkout_mode = '4';
                $ticket->is_transaction_status = '0';
                $this->log->info(json_encode($ticket));
                $ticket->save();

                $response_type = '1';
               /*  $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                $msg['is_phone_linked'] = '0';
                $msg['phone_linked_msg'] = '';
                $msg['booking_type'] = 'reservation';
                $msg['is_check_in_ontime'] = '1';
                $msg['is_our_ticket'] = $is_our_ticket;
                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type); */

                if(isset($ticket->user->phone)){
                    $this->log->info("SMS condition entered");
                    /* $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                    if ($partnerDetails->user_id == self::PARTNER_ID) {
                        $facility = $ticket->facility;
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                        })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                        $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                    } else {
                        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                    }
                    $url = env('RECEIPT_URL');
                    $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                    $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number; */
        
                    //dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                }
                return true;
            }

            if($ticket->reservation_id != ''){
                $ticket->is_checkout = '1';
                $ticket->is_closed = '1';
                $ticket->checkout_gate = $gate->gate;
                $ticket->checkout_facility_id = $gate->facility_id;
                $ticket->stop_parking_time      = $checkoutTime;
                $ticket->checkout_time = $checkoutTime;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->estimated_checkout = $checkoutTime;
                $ticket->payment_date = $checkoutTime;
                $ticket->checkout_license_plate = $plate_number;
                $ticket->checkout_session_id = $ticket->session_id;
                $ticket->checkout_mode = '4';
                $ticket->is_transaction_status = '0';
                $this->log->info(json_encode($ticket));
                $ticket->save();
                return true;
            }

            if($ticket->is_checkout == 0){   //user checkout within estimated checkout time
              /*   $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                $msg['is_phone_linked'] = '0';
                $msg['phone_linked_msg'] = '';
                $msg['is_check_in_ontime'] = '1';
                $msg['is_our_ticket'] = $is_our_ticket; */
    
                //$ticket->is_checkout            = '1';
                $ticket->checkout_gate          = $gate->gate;
                $ticket->checkout_facility_id   = $gate->facility_id;
                //$ticket->stop_parking_time      = $checkoutTime;
                $ticket->checkout_datetime      = $checkoutTime;
                $ticket->checkout_license_plate = $plate_number;
                $ticket->checkout_session_id    = $ticket->session_id;
                $ticket->checkout_mode = '4';
                /* if ($ticket->reservation_id != '') {
                    $msg['booking_number'] = $ticket->reservation_id != '' ? $ticket->reservation->ticketech_code : '';
                    $msg['booking_start_time'] = date("g:i A", strtotime($ticket->check_in_datetime));
                    $msg['booking_exit_time'] = date("g:i A", strtotime($ticket->checkout_datetime));
                    $msg['booking_entry_time'] = date("g:i A", strtotime($ticket->checkin_time));
                    $msg['booking_type'] = 'reservation';
                } */
                $this->log->info(json_encode($ticket));
                $ticket->save();
                $response_type = '1';
                //$this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                return true;
            }else if($ticket->is_checkout == 1){ //overstay case

                
                if (strtotime($ticket->estimated_checkout) >= strtotime($checkoutTime)) {

                } else { //overstay
                    $this->log->info("overstay created.".$ticket->id);
                    $updatedCheckinTime = '';
                    if (strtotime($today) != strtotime($checkinDate)) {
                        $updatedCheckinTime = date("d M", strtotime($checkinDate)) . ' ' . date("g:i A", strtotime($ticket->checkin_time));
                    } else {
                        $updatedCheckinTime = date("g:i A", strtotime($ticket->checkin_time));
                    }

                    $is_our_ticket = '1';

                    if(empty($ticket->estimated_checkout) || is_null($ticket->estimated_checkout)){}
                    else{

                        //$overstayCheckinTime = $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout;
                        $overstayCheckinTime = $ticket->estimated_checkout;
                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayCheckinTime);
                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayCheckinTime);

                        $isMember = 0;
                        if(isset($ticket->facility->rate_duration_in_hours) && !is_null($ticket->facility)){
                        
                            if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
                                $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                            } else {
                                $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                            }


                            if ($rate['price'] <= 0) {
                                $ticket->is_checkout = '1';
                                $ticket->checkout_gate = $gate->gate;
                                $ticket->checkout_facility_id = $gate->facility_id;
                                $ticket->checkout_time = $checkoutTime;
                                $ticket->checkout_datetime = $checkoutTime;
                                $ticket->checkout_license_plate = $plate_number;
                                $ticket->checkout_session_id = $ticket->session_id;
                                $ticket->checkout_mode = '4';
                                $ticket->is_transaction_status = '0';
                                $ticket->is_closed = '1';
                                $this->log->info(json_encode($ticket));
                                $ticket->save();

                                $response_type = '1';
                                /* $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                                $msg['is_phone_linked'] = '0';
                                $msg['phone_linked_msg'] = '';
                                $msg['booking_type'] = 'reservation';
                                $msg['is_check_in_ontime'] = '1';
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type); */
                                return true;
                            }

                            $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                            $rate['price'] = $rate['price'] + $taxRate;
                            if ($ticket->facility->is_lpr_cloud_payment_enabled == '0') {

                                $ticket->is_transaction_status = '1';
                                $ticket->save();
                                $this->log->info("is_lpr_cloud_payment_enabled entered for overstay ".$ticket->id);
                                /* if ($ticket->session_id == '') {
                                    $ticket->is_transaction_status = '0';
                                    $ticket->save();
                                    $response_type = '3';
                                    $msg = [];
                                    $msg['msg'] = "Amount Due";
                                    $msg['is_phone_linked'] = '0';
                                    $msg['checkin_time'] = $updatedCheckinTime;
                                    $msg['amount'] = $rate['price'];
                                    $msg['is_our_ticket'] = $is_our_ticket;
                                    $msg['is_overstay'] =  '1';
                                    $msg['eticket_id'] =  $ticket->ticket_number;
                                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                    return true;
                                } */
                            
                                $amt = $rate['price'];
                                try {
                                    $this->log->info("test1 ".$ticket->id);                            
                                    /* $request->request->add(['Amount' => $amt]);
                                    $request->request->add(['total' => $amt]);
                                    $request->request->add(['original_total' => $amt]);
                                    $request->request->add(['token' => $ticket->payment_token]);
                                    $card_month = substr($ticket->expiry, 0, 2);
                                    $card_year = substr($ticket->expiry, -2);
                                    $request->request->add(['expiration_month' => $card_month]);
                                    $request->request->add(['expiration_year' => $card_year]);
                                    $request->request->add(['expiration_date' => $ticket->expiry]);
                                    $request->request->add(['expiration' => $ticket->expiry]);
                                    $request->request->add(['card_last_four' => $ticket->card_last_four]); */
                                    if (isset($ticket->facility->FacilityPaymentDetails) && !empty($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id) == '4') {
                                        $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->where('transaction_retry', '<', '3')->whereNotNull('ticket_id')->first();
                                        $this->log->info("Payment Profile Data --" . $cardCheck);

                                        if ($cardCheck) {     
                                            $request->request->add(['Amount' => $amt]);
                                            $request->request->add(['total' => $amt]);
                                            $request->request->add(['token' => $cardCheck->token]);
                                            $request->request->add(['transactionId' => $cardCheck->trans_id]);
                                            $request->request->add(['expiration_date' => $cardCheck->expiry]);
                                            $request->request->add(['expiration' => $cardCheck->expiry]);
                                            $request->request->add(['card_last_four' => $cardCheck->card_last_four]);                                                
                                            $this->log->info("test5 ".$ticket->id);               
                                            $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($request, $ticket->facility);                                    
                                            $this->log->info("Heartland card Payment Response: " . json_encode($paymentResponse));
                                        }else{
                                            $this->log->error("Card not exist: ".$ticket->id);      
                                            //$this->throwException('Card not exist', 422);
                                        }
                                    }else{
                                        $this->log->info("Payment Settings not Found for this Facility -- " . json_encode($ticket->facility_id));
                                        //$this->throwException('Payment not Found for this Garage', 422);
                                    }
                                } catch (Exception $e) {
                                    $this->log->info("Error in Heartland Payment with card --" . json_encode($e->getMessage()));      
                                    //$this->throwException('Invalid payment information. Please verify and try again or use another card.', 422); 
                                } catch (Throwable $t) {
                                    $this->log->error("Throwable error in Heartland Payment with card --" . json_encode($t->getMessage()));      
                                    //$this->throwException('Invalid payment information. Please verify and try again or use another card.', 422);
                                }

                                if (isset($paymentResponse) && ($paymentResponse->responseMessage == 'Success' || $paymentResponse->responseMessage == 'APPROVAL')) {
                                    $this->log->info("Payment Amount inside extend3--" . $amt);
                                    $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $ticket->user_id);
                                    $this->log->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                                }else{
                                    $this->log->info("Heartland Payment Error Data ".$ticket->id);                           
                                    //$this->throwException('Payment overstay failed.', 422);
                                    //return true;
                                }

                                if(isset($authorized_anet_transaction) && !is_null($authorized_anet_transaction)){
                                    $overstay = $this->saveOverstayDetails($rate, $ticket, $ticket->facility,$diff_in_hours, $rate['price'],$taxRate,$authorized_anet_transaction->id);
                                    $this->log->info("saveOverstayDetails: ".$ticket->id); 
                                    //$ticket->anet_transaction_id = $authorized_anet_transaction->id;
                                    $ticket->payment_date = $current_time;    
                                    $ticket->is_extended = '1';
                                    $ticket->is_transaction_status = '1';
                                }
                                else{
                                    $this->log->info("Heartland Payment Error Data on save transaction".$ticket->id);                           
                                    //$this->throwException('Payment overstay failed.', 422);
                                }

                                $ticket->is_checkout = '1';
                                $ticket->closed_date = $current_time;
                                $ticket->checkout_gate = $gate->gate;
                                $ticket->checkout_facility_id = $gate->facility_id;
                                $ticket->checkout_time = $checkoutTime;
                                $ticket->checkout_datetime = $checkoutTime;
                                $ticket->checkout_license_plate = $plate_number;
                                $ticket->checkout_session_id = $ticket->session_id;
                                $ticket->checkout_mode = '4';
                                $this->log->info(json_encode($ticket));
                                $ticket->save();
                                $this->log->info("saveTicket: ".$ticket->id);  
                                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                                if ($ticket->user_id != '' && isset($authorized_anet_transaction) && !is_null($authorized_anet_transaction)) {
                                    $this->log->info("SMS condition entered");
                                    if(isset($ticket->user->phone)){
                                        $this->log->info("Overtsay SMS for price: ".$rate['price']." : and duration: ".$diff_in_hours." for ticket: ".$ticket->id);
                                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                                        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                        //$name = 'usmparking';
                                        $url = env('RECEIPT_URL');
                                        $sms_msg = "Thank you for visiting ". $facilityName . ". You are charged $".$rate['price']." as an overstay amount for the duration ".$diff_in_hours." hours. Please use the following link to view your E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                                        dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                                    }
                                }
                                $response_type = '1';
                                $this->log->info("Save Checkout response one: ".$ticket->id);       
                                return true;
                            }
                            $response_type = '3';
                            $this->log->info("Save Checkout response three: ".$ticket->id);       
                            return true;
                        }else{
                            $response_type = '7';
                            $this->log->info("Save Checkout response seven: ".$ticket->id);       
                            return true;
                        }
                    }
                }
            }
            $this->log->info("Save Checkout: No checkin is against this license plate " .$checkoutTime);
            $this->throwException('No checkin is against this license plate', 422);
        } 
        else {
            $this->log->info("Save Checkout:: no checkin found in checkout function");
            $this->throwException('No checkin found against this license plate.', 422);
            return true;
        }
    }

    public function checkExistingReservation($plate, $facility_id,$partner_id, $timestamp, $neighborhoodIds)
    {
        //$reservation = Reservation::with('user')->where("license_plate", $plate)->where("facility_id", $facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
        #comment above for multi laneid

        $start_timestamp = date('Y-m-d H:i:s', strtotime('+30 minutes', strtotime($timestamp)));
        $end_timestamp = date('Y-m-d H:i:s', strtotime('-30 minutes', strtotime($timestamp)));
        if(count($neighborhoodIds) > 0){
            $reservation = Reservation::with('user')->where("license_plate", $plate)->where("partner_id", $partner_id)->where('start_timestamp', '<=', $start_timestamp)->where('end_timestamp', '>=', $end_timestamp)->where('is_ticket', '0')->whereNull('cancelled_at')->whereIn("facility_id", $neighborhoodIds)->first();
        }else{
            $reservation = Reservation::with('user')->where("license_plate", $plate)->where("partner_id", $partner_id)->where('start_timestamp', '<=', $start_timestamp)->where('end_timestamp', '>=', $end_timestamp)->where('is_ticket', '0')->whereNull('cancelled_at')->where("facility_id", $facility_id)->first();
        }
        if (!$reservation) {
            return $reservation;
        }
        return $reservation;
    }

    public function alreadyCheckinStatus($plate, $gate, $neighborhoodIds)
    {
        //$ticket = Ticket::where("license_plate", $plate)->where("facility_id", $gate->facility_id)->where("checkin_gate", $gate->gate)->where("is_checkin", '1')->where("is_checkout", '0')->first();
        #comment above for multi laneid
        if(count($neighborhoodIds) > 0){
            $ticket = Ticket::where("license_plate", $plate)->where("is_checkin", '1')->whereIn("facility_id", $neighborhoodIds)->where("is_checkout", '0')->orderBy('id', 'desc')->first();
        }else{
            $ticket = Ticket::where("license_plate", $plate)->where("is_checkin", '1')->where("checkin_gate", $gate->gate)->where("is_checkout", '0')->orderBy('id', 'desc')->first();
        }
        return $ticket;
    }

    public function alreadyOpenTicketStatus($plate, $gate, $timestamp, $neighborhoodIds)
    {
        #comment above for multi same feed
        if(count($neighborhoodIds) > 0){
            $ticket = Ticket::where("license_plate", $plate)->where("is_checkin", '0')->where("is_checkout", '0')->where("is_closed", '0')->whereIn("facility_id", $neighborhoodIds)->orderBy('id', 'desc')->first();
        }else{
            $ticket = Ticket::where("license_plate", $plate)->where("is_checkin", '0')->where("is_checkout", '0')->where("is_closed", '0')->where("checkin_gate", $gate->gate)->orderBy('id', 'desc')->first();
        }
        return $ticket;
    }

    public function checkUserCheckoutSession($plate, $gate, $timestamp, $neighborhoodIds)
    {
        #comment above for multi same feed
        $checkinTime = date("Y-m-d H:i:s", strtotime($timestamp));
 /*        
        $startTime = Carbon::parse($checkinTime)->subMinutes(15); // 10 minutes before
        $endTime = Carbon::parse($checkinTime)->addMinutes(15); // 10 minutes after */

        if(count($neighborhoodIds) > 0){
            //$ticket = Ticket::where("license_plate", $plate)->where("is_checkout", '1')->whereBetween("checkin_time", [$startTime, $endTime])->whereIn("facility_id", $neighborhoodIds)->orderBy('id', 'desc')->first();
            $ticket = Ticket::where("license_plate", $plate)->where("is_checkout", '1')->where("estimated_checkout", '>=', $checkinTime)->whereIn("facility_id", $neighborhoodIds)->orderBy('id', 'desc')->first();
        }else{
            $ticket = Ticket::where("license_plate", $plate)->where("is_checkout", '1')->where("estimated_checkout", '>=', $checkinTime)->where("checkin_gate", $gate->gate)->orderBy('id', 'desc')->first();
        }
        return $ticket;
    }


    public function sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type)
    {
        $this->log->info("before queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send " . $queue_name);
        //Artisan::queue('read-license-plate', array('license_plate' => $ticket->license_plate, 'is_checkin_or_checkout' => '1', 'ticket' => $ticket, 'response_type' => $response_type, "msg" => $msg, 'queue_name' => $queue_name, 'gate_type' => $gate_type));
        return $ticket;
    }


    //tickets number is total 7 digit number for kstreet
    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "PE";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 10000000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 99999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }


    public function saveOverstayDetails($rate, $ticket, $facility,$length,$total,$taxfee,$anet_transaction)
    {

        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';

        $currentTime = Carbon::parse('now');
        $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
        /* $overstay = new OverstayTicket();
        $overstay->user_id = $ticket->user_id;
        $overstay->facility_id = $ticket->facility_id;
        $overstay->ticket_number = $ticket->ticket_number;
        $overstay->is_checkin = '1';
        $overstay->is_checkout = '1';
        $overstay->check_in_datetime = $ticket->estimated_checkout;
        $overstay->checkout_datetime = $estimated_checkout;
        $overstay->estimated_checkout = $estimated_checkout;
        $overstay->partner_id = $ticket->partner_id;
        $overstay->ticket_id = $ticket->id;
        $overstay->payment_date = date("Y-m-d H:i:s");
        $overstay->rate_id = $rate_id;
        $overstay->rate_description = $rate_description;
        $overstay->reservation_id = $ticket->reservation_id;
        $overstay->save(); */

        $ticketextend = new TicketExtend();
        $ticketextend->ticket_id = $ticket->id;
        $ticketextend->facility_id = $ticket->facility->id;
        $ticketextend->partner_id = $ticket->partner_id;
        $ticketextend->ticket_number = $ticket->ticket_number;
        $ticketextend->checkin_time = $ticket->estimated_checkout;
        $ticketextend->checkout_time = $estimated_checkout;
        $ticketextend->tax_fee = $ticket->tax_fee;
        //$ticketextend->processing_fee = $ticket->processing_fee;
        $ticketextend->is_priceband_apply = (isset($this->request->rate_band_id) && !empty($this->request->rate_band_id)) ? 1 : 0;

        $ticketextend->anet_transaction_id = $anet_transaction;
        $ticketextend->tax_fee = $taxfee;
        $ticketextend->length = $length;
        $ticketextend->total = $total;
        $ticketextend->grand_total = $total;
        $ticketextend->save();

        return $ticketextend;
    }



    public function saveLicensePlate($request, $gate)
    {
        $plate_number = $request->Data[0]['PlateNumber'];
        $plate_state = isset($request->Data[0]['PlateState']) ? $request->Data[0]['PlateState'] : '';
        $plate_type = isset($request->Data[0]['PlateType']) ? $request->Data[0]['PlateType'] : '';
        $lpr_session_id = $request->Data[0]['ParkingSessionId'];
        $timestamp  = $request->TimeStamp;

        UsmLicensePlate::where("facility_id", $gate->facility_id)->where("gate", $gate->gate)->delete();
        $license['license_plate'] = $plate_number;
        $license['plate_state'] = $plate_state;
        $license['lpr_session_id'] = $lpr_session_id;
        $license['plate_type']  = $plate_type;
        $license['partner_id']  =  $gate->partner_id;
        $license['facility_id'] =  $gate->facility_id;
        $license['gate'] = $gate->gate;
        $license['gate_type'] = $gate->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($timestamp));
        $result = UsmLicensePlate::create($license);
        return $result;
    }


    public function checkExistingPermit($plate, $facility_id, $partner_id, $timestamp, $neighborhoodIds)
    {
        $permitVehicles = PermitVehicle::where("license_plate_number", $plate)->where("partner_id", $partner_id)->get();
        if (count($permitVehicles) > 0) {
            foreach ($permitVehicles as $key => $permitVehicle) {
                //->whereNull("cancelled_at")
                //$permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $facility_id)->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))->orderBy("id", "DESC")->get();
                 #comment above for multi laneid
                 // ->whereNull("cancelled_at")
                if(count($neighborhoodIds) > 0){
                    $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('partner_id', $partner_id)->where('desired_start_date', '<=', $timestamp)->where('desired_end_date', '>=', $timestamp)->whereIn("facility_id", $neighborhoodIds)->whereNull('cancelled_at')->orderBy("id", "DESC")->get();
                }else{
                    $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('partner_id', $partner_id)->where('desired_start_date', '<=', $timestamp)->where('desired_end_date', '>=', $timestamp)->where("facility_id", $facility_id)->whereNull('cancelled_at')->orderBy("id", "DESC")->get();
                }
                if (count($permitRequests) > 0) {
                    foreach ($permitRequests as $key => $permitRequest) {

                        $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                        if (!$mapping) {
                            continue;
                        }
                        return $permitRequest;
                    }
                } else {
                    return $permitRequests;
                }
            }
        }
        return $permitVehicles;
    }

    public function checkExistingPass($plate, $facility_id, $partner_id, $timestamp, $neighborhoodIds)
    {
        if(count($neighborhoodIds) > 0){
            $userPass = UserPass::where('partner_id', $partner_id)->where('license_plate', $plate)->whereDate('start_date', '<=', $timestamp)->whereDate('end_date', '>=', $timestamp)->whereIn("facility_id", $neighborhoodIds)->where('remaining_days','>',0)->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
        }else{
            $userPass = UserPass::where('partner_id', $partner_id)->where('license_plate', $plate)->whereDate('start_date', '<=', $timestamp)->whereDate('end_date', '>=', $timestamp)->where("facility_id", $facility_id)->where('remaining_days','>',0)->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
        }
        return $userPass;
    }


    public function sendAnonymousCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type, $license_plate)
    {
        $this->log->info("sendAnonymousCheckinCheckoutInQueue queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['data' => $msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id' => 'PEERROR', 'license_plate' => $license_plate, 'data' => $msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send " . $queue_name);
        return true;
    }

    public function customeReplySms($msg, $phone, $imageURL = '')
    {
        try {
            if ($phone == '') {
                return "success";
            }
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                    // the number you'd like to send the message to
                    $phone,
                    array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        'body' => "$msg",
                        //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
                    )
                );
                $this->log->info("Message : {$msg} sent to $phone");
                return "success";
            } catch (RestException $e) {
                //echo "Error: " . $e->getMessage();
                $this->log->error($e->getMessage());
                return "success";
            }
        } catch (RestException $e) {
            //echo "Error: " . $e->getMessage();
            $this->log->error($e->getMessage());
            return "success";
        }
    }

    public function throwException($msg,$code){
        throw new ApiGenericException($msg, $code);
    }

    public function isValidUTCTimestamp($timestamp) { //validate timestamp utc
        $date = DateTime::createFromFormat('Y-m-d\TH:i:s\Z', $timestamp);
        if($date && $date->format('Y-m-d\TH:i:s\Z') === $timestamp){
            $receivedDateTime = Carbon::parse($timestamp);
            $currentDateTime = Carbon::now('UTC');
            if ($receivedDateTime->gt($currentDateTime)) {
                return false;
            } else {
                return true;
            }
        }
        return false;
    }
    
}