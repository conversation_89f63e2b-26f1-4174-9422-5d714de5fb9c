<?php
namespace App\Http\Controllers\ParkEngage;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Support\Facades\Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\ParkEngage\UserFacility;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\Facility;
use App\Exceptions\UserNotAuthorized;
use App\Models\Ticket;
use App\Models\ParkEngage\UserValidateMaster;
use App\Models\ParkEngage\UserValidateUsages;
use App\Models\BusinessFacilityPolicy;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use App\Models\BusinessQrCode;
use App\Models\ParkEngage\UserMembership;
use App\Models\ParkEngage\SubPartnerPermission;
use App\Models\ParkEngage\MembershipPlan;
use App\Models\ParkEngage\RoleUser;

class BusinessClerkController extends Controller
{
	
    const USERTYPE = 8;
	const USERTYPE_BUSINESS = 10;
	const PASSWORD_PATTERN = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%&*()';
	const SUPERADMIN = 1;
	const PARTNER = 3;
	const SUBORDINATE = 4;
	const REGIONAL_MANAGER = 12;

	const TRIAL_PLAN_DAYS = 15;
	const MONTHLY_PLAN_DAYS = 30;
	const ANNUAL_PLAN_DAYS = 365;

	protected $log;
	protected $countryCode;
	public $request;
		
	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/parkengage/business-clerk')->createLogger('business-clerk');
		$this->request = $request;
	}

	public function index(Request $request)
	{
		if (!Auth::user()) {
			throw new UserNotAuthorized("Invalid User!");
		}
		if (Auth::user()->status == '0') {
			throw new UserNotAuthorized("User is inactive. Please contact to admin.");
		}

		if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
			$partner_id = Auth::user()->created_by;
			$result = User::with('affliateBusiness', 'clerkLimit','BusinessPolicy')->whereNull('deleted_at')->where('created_by', $partner_id)->where('user_type', self::USERTYPE);
			
		} else if (Auth::user()->user_type == self::SUPERADMIN) {
			$partner_id = $request->partner_id;

			$result = User::with('affliateBusiness', 'clerkLimit','BusinessPolicy')->whereNull('deleted_at')->whereIn('status', [0, 1])->where('user_type', self::USERTYPE);
			if ($partner_id) {
				$result = $result->where('created_by', $partner_id);
			}
			$rm_id = $request->rm_id;
			if ($rm_id) {
				$facilities = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
				if ($facilities) {
					$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
					$result = $result->whereIn('business_id', $business_id);
				} else {
					throw new ApiGenericException("Facility is not mapped with RM");
				}
			}
		} else if (Auth::user()->user_type == self::SUBORDINATE) {
			$partner_id = Auth::user()->created_by;
			$result = User::with('affliateBusiness', 'clerkLimit','BusinessPolicy')->whereNull('deleted_at')->where('created_by', $partner_id)->where('user_parent_id', Auth::user()->user_parent_id)->where('user_type', self::USERTYPE);
		} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
			$partner_id = Auth::user()->created_by;
		//	$result = User::with('affliateBusiness', 'clerkLimit','BusinessPolicy')->whereNull('deleted_at')->where('created_by', $partner_id)->where('user_parent_id', Auth::user()->id)->where('user_type', self::USERTYPE);
			$result = User::with('affliateBusiness', 'clerkLimit', 'BusinessPolicy')
						->whereNull('deleted_at')
						->where('created_by', $partner_id)
						->where(function ($query) {
							$query->whereNull('user_parent_id')
								->orWhere('user_parent_id', Auth::user()->id);
						})
						->where('user_type', self::USERTYPE);
		} else {
			$partner_id = Auth::user()->id;
			$result = User::with('affliateBusiness', 'clerkLimit','BusinessPolicy')->whereNull('deleted_at')->where('created_by', $partner_id)->where('user_type', self::USERTYPE);
			$rm_id = $request->rm_id;
			if ($rm_id) {
				$facilities = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
				if ($facilities) {
					$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
					$result = $result->whereIn('business_id', $business_id);
				} else {
					throw new ApiGenericException("Facility is not mapped with RM");
				}
			}
		}

		if ($request->search) {

			$result = $result->where('user_type', self::USERTYPE)->where(function ($query) use ($request) {
				$query->orWhere('email', 'like', '%' . trim($request->search) . '%')
					->orWhere('name', 'like', '%' . trim($request->search) . '%')
					->orWhere('phone', 'like', '%' . trim($request->search) . '%');
			});
			/*
			$result = $result->orWhereHas(
					  'affliateBusiness', function ($query) use ($request) {
						  $query->where('business_name', 'like', "%{$request->search}%");						  
						});						
				*/
		}
		if ($request->business_id) {
			$result = $result->Where('business_id', $request->business_id);
		}

		if ($request->sort != '') {
			if ($request->sort == 'business_name') {
				//$result = $result->orderBy('business_name',$request->sortBy);
			} else {
				$result = $result->orderBy($request->sort, $request->sortBy);
			}
		} else {
			$result = $result->orderBy("id", "DESC");
		}
		//$result = $result->toSql();
		$result = $result->paginate(10);
		foreach($result as $key=>$val){
			$clerkFacility = UserFacility::where('user_id', $val->id)->pluck('facility_id');
        
			$policy = DB::table('business_facility_policy')->where('business_id',$val->affliateBusiness->id)->whereIn('facility_id',$clerkFacility)->whereNull('deleted_at')->pluck('policy_id');
			if($policy){
				$policyData = DB::table('business_policy')->select('discount_type as Type','policy_name as name')->whereIn('id',$policy)->get();
				$val->affliateBusiness->policyDiscountType = $policyData;
			}
		}
		return $result;
	}

	public function store(Request $request)
	{
		$this->countryCode = QueryBuilder::appendCountryCode();
		$this->log->error('Create Business Clerk' . json_encode($request->all()));
		if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
			$partner_id = Auth::user()->created_by;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else if (Auth::user()->user_type == self::SUPERADMIN) {
			$partner_id = $request->partner_id;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else if (Auth::user()->user_type == self::SUBORDINATE) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->user_parent_id;
			$subordinate_id = Auth::user()->id;
		} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->id;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else {
			$partner_id = Auth::user()->id;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		}

		$checkUser = User::whereNull('deleted_at')->where('email', $request->email)->where('created_by', $partner_id)->first();
		if(!$checkUser){
			$phone = $this->countryCode . $request->phone;
			$checkUser = User::where('phone', $phone)->where('created_by', $partner_id)->first();
		}
		$random_password = substr(str_shuffle(self::PASSWORD_PATTERN), 0, 8);
		if ($checkUser) {
			if(($checkUser->user_type =='5') && ($checkUser->anon == '1')){
				$checkUser->user_type ='8';
				$checkUser->anon = false;
				$checkUser->email = $request->email;
				$checkUser->phone = $request->phone !== '' ? $this->countryCode . $request->phone : '';
				$checkUser->password = Hash::make($random_password);
				$checkUser->business_id = $request->business_id;
				$checkUser->user_parent_id = isset($rm_id) ? $rm_id : NULL;	
				$checkUser->subordinate_id = isset($subordinate_id) ? $subordinate_id :NULL;	
				$checkUser->save();
				$result = $checkUser;
			}else{
				throw new ApiGenericException("User is Already Exist.");
			}
		}else{
			$this->countryCode = QueryBuilder::appendCountryCode();
			//$password = rand ( 10000 , 99999 );
			$data['name'] = $request->clerk_name;
			$data['email'] = $request->email;
			$data['phone'] = $request->phone !== '' ? $this->countryCode . $request->phone : '';
			$data['password'] = Hash::make($random_password);
			$data['anon'] = false;
			$data['user_type'] = self::USERTYPE;
			$data['created_by'] = $partner_id;
			$data['business_id'] = $request->business_id;
			$data['status'] = $request->status;
			$data['user_parent_id'] = isset($rm_id) ? $rm_id : NULL;	
			$data['subordinate_id'] = isset($subordinate_id) ? $subordinate_id :NULL;			
			$result = User::create($data);
			if (!$result) {
				throw new ApiGenericException("Record Not Added");
			}
		}
		

		DB::table('role_user')->insert([
			'user_id' => $result->id,
			'role_id' => '8'
		]);

		if ($request->facility_ids) {
			$facilityArr = explode(',', $request->facility_ids);
			foreach ($facilityArr as $val) {
				DB::table('user_facilities')->insert([
					'user_id' => $result->id,
					'facility_id' => $val
				]);
			}
		}

		if ($request->max_hour || $request->max_dollar || $request->full_amount_max) {
			$usages['user_id'] = $result->id;
			$usages['max_hour'] = (isset($request->max_hour) && !empty($request->max_hour)) ? $request->max_hour : NULL;
			$usages['max_remaining_hour'] = (isset($request->max_hour) && !empty($request->max_hour)) ? $request->max_hour : NULL;
			$usages['max_dollar'] = (isset($request->max_dollar) && !empty($request->max_dollar)) ? $request->max_dollar : NULL;
			$usages['max_remaining_dollar'] = (isset($request->max_dollar)  && !empty($request->max_dollar)) ? $request->max_dollar : NULL;
			$usages['full_amount_max'] = (isset($request->full_amount_max)  && !empty($request->full_amount_max)) ? $request->full_amount_max : NULL;
			$usages['full_amount_remaining'] = (isset($request->full_amount_max)  && !empty($request->full_amount_max)) ? $request->full_amount_max : NULL;			
			$usages['month'] = date("m");
			$usages['year'] = date("Y");
			UserValidateMaster::create($usages);
		}

		$permission = $request->permission;
		if($permission){
			$membershipPlan = UserMembership::where('user_id', $partner_id)->first();
			if ($membershipPlan) {
				$this->request = $request;
				$this->attachMembershipPlanToUser($result, $membershipPlan->membership_plan_id);
			}
			foreach ($permission as $key => $value) {			
				$membershipPlan = UserMembership::where('user_id', $result->created_by)->first();
				if ($membershipPlan) {
					$memberships['membership_plan_id'] =  $membershipPlan->membership_plan_id;
				} else {
					$memberships['membership_plan_id'] = $request->membership_plan_id;
				}
				$memberships['partner_id'] = $partner_id;
				$memberships['web'] = $value['web'];
				$memberships['display_name'] = $value['display_name'];
				$memberships['parent_id'] = isset($value['parent_id']) ? $value['parent_id'] : NULL;
				$memberships['user_id'] = $result->id;
				$memberships['type'] = $value['type'];
				$memberships['list_order'] = isset($value['list_order']) ? $value['list_order'] : 0;
	
				SubPartnerPermission::create($memberships);
			}
		}
		
		Artisan::queue('businessclerkemail:email', ['id' => $result->id, 'password' => $random_password]);
		return $result;
        
	}

    public function destroy($id) {
		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
				
			} else {
				if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
					$partner_id = Auth::user()->created_by;
				} else {
					$partner_id = Auth::user()->id;
				}
			}
		}
		if (Auth::user()->user_type == '1') {
			$result = User::find($id);
		} else {
			$result = User::where('created_by', $partner_id)->find($id);
		}

		if ($result) {
			$result->delete();
			UserFacility::where('user_id', $id)->delete();
			return "Data successfully deleted.";
		}
		throw new ApiGenericException("Record Not Found.");
	}

	public function update(Request $request)
	{
		$this->log->error('Update Business Clerk' . json_encode($request->all()));
		if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
			$partner_id = Auth::user()->created_by;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else if (Auth::user()->user_type == self::SUPERADMIN) {
			$partner_id = $request->partner_id;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else if (Auth::user()->user_type == self::SUBORDINATE) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->user_parent_id;
			$subordinate_id = Auth::user()->id;
		} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->id;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else {
			$partner_id = Auth::user()->id;
			$rm_id = isset($request->rm_id)?$request->rm_id:NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		}
		$checkUser = User::whereNull('deleted_at')->where('id', $request->id)->where('user_type', self::USERTYPE)->first();

		if (!$checkUser) {
			throw new ApiGenericException("User Not Found");
		}

		if ($checkUser->email != $request->email) {
			$checkUserEmail = User::where('email', $request->email)->where('user_type', self::USERTYPE)->first();

			if ($checkUserEmail) {
				throw new ApiGenericException("User email already exist.");
			}
		}

		$this->countryCode = QueryBuilder::appendCountryCode();

		$checkUser->name = $request->clerk_name;
		$checkUser->email = $request->email;
		$checkUser->phone = $this->countryCode . $request->phone;
		$checkUser->anon = false;
		$checkUser->user_type = self::USERTYPE;
		$checkUser->created_by = $partner_id;
		$checkUser->business_id = $request->business_id;
		$checkUser->status = $request->status;
		$checkUser->user_parent_id = isset($rm_id) ? $rm_id : NULL;
		$checkUser->subordinate_id = isset($subordinate_id) ? $subordinate_id : NULL;
		$checkUser->save();

		if ($request->facility_ids) {
			UserFacility::where('user_id', $checkUser->id)->delete();
			$facilityArr = explode(',', $request->facility_ids);
			foreach ($facilityArr as $val) {
				DB::table('user_facilities')->insert([
					'user_id' => $checkUser->id,
					'facility_id' => $val
				]);
			}
		}

		$usage = UserValidateMaster::where('user_id', $checkUser->id)->first();
		if ($usage) {
			//new changes
			if (isset($request->max_hour) && $request->max_hour != '') {
				$message = "Hourly Capping";
				$used_hrs_amount = UserValidateUsages::where('user_id', $checkUser->id)->where('validate_type', 1)->sum('validate_value');
				if (isset($used_hrs_amount)  && $usage->max_hour == NULL) {

					if ($request->max_hour <= $used_hrs_amount) {
						throw new ApiGenericException("Sorry, you already have validated more than monthly capping houlry");
					}
				}
				$remaining_new = $this->clerkCappingUpdate($request->max_hour, $usage->max_hour, $usage->max_remaining_hour, $message);

				if (isset($used_hrs_amount) && $usage->max_hour == NULL) {
					$remaining_new =  $remaining_new - $used_hrs_amount;
				}

				$usage->max_hour = $request->max_hour;
				$usage->max_remaining_hour = $remaining_new;
			} else {
				$usage->max_hour = Null;
				$usage->max_remaining_hour = Null;
			}
			if (isset($request->max_dollar) && !empty($request->max_dollar)) {
				$message = "Dollar Capping";

				$used_dollar = UserValidateUsages::where('user_id', $checkUser->id)->where('validate_type', 2)->sum('validate_value');
				if (isset($used_dollar)  && $usage->max_dollar == NULL) {

					if ($request->max_dollar <= $used_dollar) {
						throw new ApiGenericException("Sorry, you already have validated more than monthly capping dollar");
					}
				}
				$remaining_new = $this->clerkCappingUpdate($request->max_dollar, $usage->max_dollar, $usage->max_remaining_dollar, $message);
				if (isset($used_dollar) && $usage->max_dollar == NULL) {
					$remaining_new =  $remaining_new - $used_dollar;
				}
				$usage->max_dollar = $request->max_dollar;
				$usage->max_remaining_dollar = $remaining_new;
			} else {
				$usage->max_dollar = Null;
				$usage->max_remaining_dollar = Null;
			}

			if (isset($request->full_amount_max) && !empty($request->full_amount_max)) {
				$message = "Full off Capping";
				$used_fullamount = UserValidateUsages::where('user_id', $checkUser->id)->where('validate_type', 0)->sum('validate_amount');
				if (isset($used_fullamount)  && $usage->full_amount_max == NULL) {

					if ($request->full_amount_max <= $used_fullamount) {
						throw new ApiGenericException("Sorry, you already have validated more than monthly capping full amount");
					}
				}
				$remaining_new = $this->clerkCappingUpdate($request->full_amount_max, $usage->full_amount_max, $usage->full_amount_remaining, $message);
				if (isset($used_fullamount) && $usage->full_amount_max == NULL) {
					$remaining_new =  $remaining_new - $used_fullamount;
				}
				$usage->full_amount_max = $request->full_amount_max;
				$usage->full_amount_remaining = $remaining_new;
			} else {
				$usage->full_amount_max = Null;
				$usage->full_amount_remaining = Null;
			}


			$usage->month = date("m");
			$usage->year = date("Y");
			$usage->save();
		} else {
			//account will be create when update case, when user account is not created.
			$usages['user_id'] = $checkUser->id;
			//$usages['monthly_maximum_amount'] = $request->monthly_maximum_amount;
			//$usages['monthly_remaining_amount'] = $request->monthly_maximum_amount;
			//$usages['paid_type'] = $request->paid_type;
			// $usages['max_hour'] = $request->max_hour;
			// $usages['max_dollar'] = $request->max_dollar;
			$usages['month'] = date("m");
			$usages['year'] = date("Y");
			UserValidateMaster::create($usages);
			$this->update($request);
		}
		$permission = $request->permission;
		if ($permission) {
			$membershipPlan = UserMembership::where('user_id', $partner_id)->first();
			if ($membershipPlan) {
				$this->request = $request;
				$this->attachMembershipPlanToUser($checkUser, $membershipPlan->membership_plan_id);
			}
			SubPartnerPermission::where('user_id', $checkUser->id)->delete();
			foreach ($permission as $key => $value) {
				$memberships['partner_id'] = $partner_id;
				$memberships['membership_plan_id'] = $membershipPlan->membership_plan_id;
				$memberships['web'] = $value['web'];
				$memberships['display_name'] = $value['display_name'];
				$memberships['parent_id'] = isset($value['parent_id']) ? $value['parent_id'] : NULL;
				$memberships['user_id'] = $checkUser->id;
				$memberships['type'] = $value['type'];
				$memberships['list_order'] = isset($value['list_order']) ? $value['list_order'] : 0;
				SubPartnerPermission::create($memberships);
			}
		}		
		return $checkUser;
		
	}

	public function clerkCappingUpdate($request_max, $max, $remaining, $message){
		//$new_val = $request->max_hour;
		//$total_usage = $usage->max_hour;
		//$remaining_usage = $usage->max_remaining_hour;


		$this->log->error('Clerk Capping  Request max' . json_encode($request_max));
		$this->log->error('Clerk Capping max ' . json_encode($max));
		$this->log->error('Clerk Capping  remaining' . json_encode($remaining));
		$this->log->error('Clerk Capping message' . json_encode($message));
		$new_val = $request_max;
		$total_usage = $max;
		$remaining_usage = $remaining;

		if ($new_val > $total_usage) {
			$remaining_total = ($new_val - $total_usage);
			$remaining_usage_new = $remaining_total + $remaining_usage;
		} else if (($new_val < $total_usage)) {
			$remaining_total = ($total_usage - $new_val);

			if ($remaining_total > $remaining_usage) {
				throw new ApiGenericException("The Remaining Value not Accepted in " . $message);
			} else if ($remaining_total < $remaining_usage) {
				$remaining_usage_new = $remaining_usage - $remaining_total;
			} else {
				$remaining_usage_new = $remaining_total - $remaining_usage;
			}
		} else if ($new_val == $total_usage) {
			$remaining_usage_new = $remaining_usage;
		}

		//$usage->max_hour = $request->max_hour;
		//$usage->monthly_remaining_amount = $remaining_usage_new;
		return $remaining_usage_new;

	}

	public function show($id)
	{
		if (!Auth::user()) {
			throw new UserNotAuthorized("Invalid User!");
		}
		if (Auth::user()->status == '0') {
			throw new UserNotAuthorized("User is inactive. Please contact to admin.");
		}
		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
				
			} else {
				if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12') || (Auth::user()->user_type == '8') || (Auth::user()->user_type == '10')) {
					$partner_id = Auth::user()->created_by;
				} else {
					$partner_id = Auth::user()->id;
				}
			}
		}
		if (Auth::user()->user_type == '1') {
			$result = User::with('affliateBusiness', 'userFacility', 'clerkLimit')->whereNull('deleted_at')->where('user_type', self::USERTYPE)->where('id', $id)->first();
		} else {
			$result = User::with('affliateBusiness', 'userFacility', 'clerkLimit')->whereNull('deleted_at')->where('user_type', self::USERTYPE)->where('id', $id)->where('created_by', $partner_id)->first();
		}
		$facility = UserFacility::selectRaw('GROUP_CONCAT(facility_id) as facility_id')->where('user_id', $id)->first();
		$facilities = explode(",", $facility->facility_id);
		$facilityName = DB::table('facilities')->selectRaw('GROUP_CONCAT(full_name) as facility_name')->whereIn('id', $facilities)->first();
		if (!$result) {
			throw new NotFoundException("Invalid Record.");
		}
		$result->facility_ids = $facility->facility_id;
		$result->facilityName = $facilityName->facility_name;
		$clerkFacility = UserFacility::where('user_id', $result->id)->pluck('facility_id');
      
		$policy = DB::table('business_facility_policy')->where('business_id',$result->affliateBusiness->id)->whereIn('facility_id',$clerkFacility)->whereNull('deleted_at')->pluck('policy_id');
		if($policy){
			$policyData = DB::table('business_policy')->select('discount_type as Type','policy_name as name')->whereIn('id',$policy)->get();
			$result->affliateBusiness->policyDiscountType = $policyData;
		}
		$permission = SubPartnerPermission::where('user_id', $id)->get();
		$result->permission = $permission;
		return $result;
	}


	public function updateCheckinTime(Request $request)
	{
		$this->log->error('Update Checkin Time ' . json_encode($request->all()));

		if (isset($request->ticket) && isset($request->time)) 
        {
			$ticketData = Ticket::with(['user', 'facility'])->where('ticket_number', $request->ticket)->orderBy("id", "DESC")->first();
			$this->setCustomTimezone($ticketData->facility_id);
			if ($ticketData) 
            {

				$oldcheckintime = strtotime($ticketData->checkin_time);
				$newcheckintime = $oldcheckintime - ($request->time * 60 * 60);
				$newcheckintime = date("Y-m-d H:i:s", $newcheckintime);

				$ticketData->checkin_time = $newcheckintime;
				$ticketData->check_in_datetime = $newcheckintime;
				$ticketData->save();
				return 'Checkin Time Updated Successfully.';
			} 
            else {
				return 'Ticket Not found.';
			}
		} 
        else if (isset($request->ticket)) 
        {
			$ticketData = Ticket::with(['user', 'facility'])->where('ticket_number', $request->ticket)->orderBy("id", "DESC")->first();

			if ($ticketData) 
            {
				if ($ticketData->paid_amount == '') 
                {
					$ticketData->paid_amount = 0;
				}
				return nl2br("Payable Amount: $ticketData->grand_total\n\nTicket Validated Amount: $ticketData->paid_amount");
			} 
            else {
				return 'Ticket Not found.';
			}
		} 
        else {
			return 'Invalid Parameters';
		}
	}

	public function getPolicyByBusinessId($business_id, $facility_id){

		$facility_id = explode(",", $facility_id);
		//dd($facility_id);
		$businessPolicy = BusinessFacilityPolicy::select('id', 'business_id', 'policy_id', 'facility_id')->with('policies')->where('business_id', $business_id)->whereIn('facility_id', $facility_id)->get();
		if(count($businessPolicy) > 0){
			foreach($businessPolicy as $key=>$policy){
				$qrcodeExist = BusinessQrCode::where('business_id', $policy->business_id)->where('business_id', $business_id)->whereIn('facility_id', $facility_id)->first();
				if($qrcodeExist){
					$facility =  Facility::select('id','owner_id')->where('id',$qrcodeExist->facility_id)->first(); 
					$checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
					if(isset($getRM->slug)){
						$slug = ($getRM->slug) ? $getRM->slug : '';
					}else{
						$slug = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
					}
					$url = env('QRCODE_AUTOVALIDATION');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("AUTOVALIDATION_URL", $facility->owner_id);
					if($dynamicReceiptUrl){
						$url = $dynamicReceiptUrl->value;
					}
					$qrcode_path = "$url/$slug/" . base64_encode($qrcodeExist->business_qrcode);
					$businessPolicy[$key]['qr_code'] = $qrcode_path;
				}else{
					$businessPolicy[$key]['qr_code'] = '';
				}
			}
		}

		return $businessPolicy;
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		$this->log->error('Facility ID ' . $facility_id);
		$this->log->error('Facility Time Zome ' . $facility->timezone);
		if (($facility) && ($facility->timezone != '')) {
			$this->log->error('Facility Time Zome22 ' . $facility->timezone);
			date_default_timezone_set($facility->timezone);
		} else {
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					$this->log->error('Facility Time Zome2233 ' . $facility->timezone);
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}
	}

	private function attachMembershipPlanToUser($user, $membership_plan_id, $is_active = 1)
	{
		$membership_plan = MembershipPlan::find($membership_plan_id);
		if ($membership_plan) {
			$start_date = Carbon::now();
			$days = 0;
			if ($this->request->plan_type == '0') {
				$days = self::TRIAL_PLAN_DAYS;
			} elseif ($this->request->plan_type == '1') {
				$days = self::MONTHLY_PLAN_DAYS;
			} elseif ($this->request->plan_type == '2') {
				$days = self::ANNUAL_PLAN_DAYS;
			} else {
				$days = 0;
			}
			$end_date = $start_date->copy()->addDays($days);

			if (!$user->membershipPlans->contains($membership_plan->id)) {
				$user->membershipPlans()->save($user, [
					'membership_plan_id' => $membership_plan_id,
					'start_date' => $start_date,
					'end_date' => $end_date,
					'is_active' => $is_active,
					'plan_type' => isset($this->request->plan_type) ? $this->request->plan_type : '0'
				]);
				$membership_plan->start_date = $start_date->format('Y-m-d H:i:s');
				$membership_plan->end_date = $end_date->format('Y-m-d H:i:s');
				return $membership_plan;
			}
		}
		return null;
	}


	/*  
    * Code initiated by Kuldeep  
    * Date: March 24, 2025  
    * Purpose: Admin role update normal user to clerk.  
    */

	public function updateUserRole(Request $request, $user_id)
	{
		$targetUser = User::findOrFail($user_id);
		$partner_id = $targetUser->created_by;
		$userMembership = UserMembership::where('user_id', $partner_id)->first();

		if ($targetUser->status == '0') {
			throw new UserNotAuthorized("User is inactive. Please contact to admin.");
		}

		if ($targetUser->user_type != 5) {
			throw new UserNotAuthorized("Only users with user_type 5 can have their roles updated.");
		}

		// Ensure membership exists for the partner
		if (!$userMembership) {
			throw new \Exception("No membership found for partner ID: $partner_id");
		}

		try {
			DB::beginTransaction();

			// Update role_user
			RoleUser::updateOrCreate(
				['user_id' => $user_id],
				['role_id' => $request['role_id']]
			);

			// Update user
			$targetUser->update([
				'user_type' => $request['user_type'],
				'business_id' => $request['business_id'] ?? null
			]);

			// Update facilities if provided
			if (!empty($request['facility_ids'])) {
				UserFacility::where('user_id', $user_id)->delete();
				$now = Carbon::now();
				$facilityData = array_map(function ($facility_id) use ($user_id, $now) {
					return [
						'user_id' => $user_id,
						'facility_id' => $facility_id,
						'created_at' => $now,
						'updated_at' => $now
					];
				}, $request['facility_ids']);
				UserFacility::insert($facilityData);
			}

			$now = Carbon::now();
			// Insert into user_membership
			DB::table('user_membership')->insert([
				'user_id' => $user_id,
				'membership_plan_id' => $userMembership->membership_plan_id,
				'start_date' => $now,
				'end_date' => $now->copy()->addYear(),
				'plan_type' => 0,
				'is_active' => 1,
				'is_auto_renew' => 0
			]);

			// Insert into subpartner_permissions
			$permissions = [
				[
					'membership_plan_id' => $userMembership->membership_plan_id,
					'partner_id' => $partner_id,
					'user_id' => $user_id,
					'name' => '',
					'display_name' => 'eValidation',
					'web' => '',
					'created_at' => $now,
					'updated_at' => $now,
					'type' => 0,
					'list_order' => 18,
					'parent_id' => null
				],
				[
					'membership_plan_id' => $userMembership->membership_plan_id,
					'partner_id' => $partner_id,
					'user_id' => $user_id,
					'name' => '',
					'display_name' => 'Ticket Validation',
					'web' => '/admin/tickets',
					'created_at' => $now,
					'updated_at' => $now,
					'type' => 1,
					'list_order' => 5,
					'parent_id' => 85
				],
				[
					'membership_plan_id' => $userMembership->membership_plan_id,
					'partner_id' => $partner_id,
					'user_id' => $user_id,
					'name' => '',
					'display_name' => 'My Account',
					'web' => '/admin/user-profile',
					'created_at' => $now,
					'updated_at' => $now,
					'type' => 0,
					'list_order' => 1,
					'parent_id' => null
				],
				[
					'membership_plan_id' => $userMembership->membership_plan_id,
					'partner_id' => $partner_id,
					'user_id' => $user_id,
					'name' => '',
					'display_name' => 'My Profile',
					'web' => '/admin/user-profile',
					'created_at' => $now,
					'updated_at' => $now,
					'type' => 0,
					'list_order' => 1,
					'parent_id' => 44
				]
			];
			//dd($permissions);
			SubPartnerPermission::insert($permissions);

			DB::commit();
			$targetUser->load('facilities');

			return response()->json([
				'status' => 'success',
				'message' => 'User role updated successfully',
				'data' => [
					'user_id' => $user_id,
					'role_id' => $request['role_id'],
					'user_type' => $request['user_type'],
					'business_id' => $request['business_id'],
					'facilities' => $targetUser->facilities,
					'updated_at' => $now->toIso8601String()
				]
			]);
		} catch (Exception $e) {
			DB::rollBack();
			return response()->json([
				'status' => 'error',
				'message' => 'Failed to update user role: ' . $e->getMessage()
			], 500);
		}
	}

	public function updateUserRoleold(Request $request, $user_id)
	{
		$targetUser = User::findOrFail($user_id);

		if ($targetUser->status == '0') {
			throw new UserNotAuthorized("User is inactive. Please contact to admin.");
		}

		if ($targetUser->user_type != 5) {
			throw new UserNotAuthorized("Only users with user_type 5 can have their roles updated.");
		}

		try {
			DB::beginTransaction();

			RoleUser::updateOrCreate(
				['user_id' => $user_id],
				['role_id' => $request['role_id']]
			);

			$targetUser->update([
				'user_type' => $request['user_type'],
				'business_id' => $request['business_id'] ?? null
			]);

			if (!empty($request['facility_ids'])) {
				UserFacility::where('user_id', $user_id)->delete();
				$now = Carbon::now();
				$facilityData = array_map(function ($facility_id) use ($user_id, $now) {
					return [
						'user_id' => $user_id,
						'facility_id' => $facility_id,
						'created_at' => $now,
						'updated_at' => $now
					];
				}, $request['facility_ids']);
				UserFacility::insert($facilityData);
			}
			//insert into user_membership table

			DB::commit();
			$targetUser->load('facilities');

			return response()->json([
				'status' => 'success',
				'message' => 'User role updated successfully',
				'data' => [
					'user_id' => $user_id,
					'role_id' => $request['role_id'],
					'user_type' => $request['user_type'],
					'business_id' => $request['business_id'],
					'facilities' => $targetUser->facilities,
					'updated_at' => Carbon::now()->toIso8601String()
				]
			]);
		} catch (Exception $e) {
			DB::rollBack();
			return response()->json([
				'status' => 'error',
				'message' => 'Failed to update user role: ' . $e->getMessage()
			], 500);
		}
	}

}
