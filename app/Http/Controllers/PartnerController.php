<?php

namespace App\Http\Controllers;

use App\Classes\Inventory;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Exceptions\UnauthorizedException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\GeoLocation;
use App\Models\OauthClient;
use App\Models\Partner;
use App\Models\PartnerToken;
use App\Models\Photo;
use App\Models\User;
use Authorizer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;
use App\Services\LoggerFactory;
use App\Models\FacilityPaymentType;

//use Authorizer;

class PartnerController extends Controller
{
    public $inventory;
    public $partnerClass;

    public function __construct(LoggerFactory $logFactory)
    {
        $this->inventory    = new Inventory();
        $this->partnerClass = new \App\Classes\Partner();
        $this->log = $logFactory->setPath('logs/inventory_matrix_updated')->createLogger('inventory_matrix_updated');
    }

    public function index(Request $request)
    {
        $partners = Partner::query();

        if ($request->search) {
            $partners = QueryBuilder::buildSearchQuery($partners, $request->search, Partner::$searchFields);
        }

        return $partners->with('geolocations')->orderBy('title')->paginate(50);
    }

    public function show(Partner $partner)
    {
        return $partner->load('geolocations', 'photos');
    }

    public function store(Request $request)
    {
        $this->validate($request, Partner::$validParams);
        $partner     = Partner::create($request->all());
        //$geolocation = new GeoLocation();
        //$geolocation->fill($request->geolocations);
       // $partner->geolocations()->save($geolocation);
        $partner->save();

        \DB::table('oauth_clients')->insert(
            ['id' => $partner->slug, 'secret' => str_random('25'), 'partner_id' => $partner->id]);

        $user                  = new User();
        $user->name            = "partner_$partner->slug";
        $user->email           = "$partner-><EMAIL>";
        $user->password        = bcrypt("partner_$partner->slug");
        $user->email_confirmed = 1;
        $user->is_partner      = 1;
        $user->save();

        return $partner->load('geolocations', 'photos');
    }

    public function addPhoto(Partner $partner, Request $request)
    {
        $partner->photos()->delete(); //Delete all copies of the previous logos

        if (!$request->hasFile('image')) {
            return false;
        }

        $image    = $request->file('image');
        $filename = Uuid::generate(4) . '.' . $image->getClientOriginalExtension();

        $photo             = new Photo();
        $photo->image_name = $filename;
        $partner->photos()->save($photo);

        Image::make($image->getRealPath())->save(storage_path("app/" . $partner::IMAGE_FOLDER . '/' . $filename));

        return $photo;
    }

    public function destroy(Partner $partner)
    {
        $partner->geolocations()->delete();
        $partner->photos()->delete();
        $partner->delete();
        return $partner;
    }

    public function update(Request $request, Partner $partner)
    {
        $this->validate($request, Partner::$validParams);
        $partner->fill($request->all());
        if (!$partner->geolocations) {
            $geolocation = new GeoLocation();
            $geolocation->fill($request->geolocations);
            $partner->geolocations()->save($geolocation);
        } else {
            $partner->geolocations->fill($request->geolocations)->save();
            $partner->fill($request->all())->save();
        }
        return $partner->load('geolocations', 'photos');
    }

    public function partnerBySlug($slug)
    {
        $partner = Partner::where('slug', $slug)->first();
        if (!$partner) {
            throw new NotFoundException('Partner not found for slug.');
        }
        return $partner->load('geolocations', 'photos');
    }

    /**
     * @param Request $request
     * @return array
     * @throws UnauthorizedException
     */
    public function authenticate(Request $request)
    {
        $apiKey = $request->api_key;

        //check if this api key exists in the database
        $client = OauthClient::where('secret', $apiKey)->first();
	
        if ($client) {
            /*if (is_null($client->partner_id)) {
                $user    = User::where('is_partner', 1)->first();
                $name    = str_replace('partner_', '', $user->name);
                $name    = str_replace('_', ' ', $name);
                $partner = Partner::where('title', 'like', "$name%")->first();
                $request->request->add(
                    ['grant_type' => 'password', 'client_id' => $client->id, 'client_secret' => $request->api_key,
                     'username'   => "$partner-><EMAIL>", 'password' => "partner_$partner->slug"]);
            } else {*/
            $partner = User::find($client->partner_id);
			
            $request->request->add(
                ['grant_type' => 'user_id_login', 'client_id' => str_slug($partner->name).$partner->id, 'client_secret' => $request->api_key,
                 'username'   => $partner->id, 'password' => $partner->password]);
            //}
            $session = Authorizer::issueAccessToken();

            PartnerToken::create(
                ['token'      => $session['access_token'], 'partner_id' => $client->partner_id,
                 'is_partner' => $client->partner_id ? 1 : 0]);
            return ['access_token' => $session['access_token'], 'token_type' => 'Bearer'];
        }

        throw new UnauthorizedException('Invalid credentials');

    }

    /**
     * @param Request $request
     * @return array
     * @throws ApiGenericException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function facilityPricing(Request $request)
    {
        return $this->partnerClass->facilityPricing($request);
    }

    /**
     * @param Request $request
     * @return array
     * @throws ApiGenericException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function iconFacilityPricing(Request $request)
    {
        return $this->partnerClass->iconFacilityPricing($request);
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function getInventory(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }


        return $this->inventory->getInventory($request->facility_id);
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function getInventoryForSpecialDates(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }


        return $this->inventory->getInventoryForSpecialDates($request->facility_id);
    }

     public function getInventoryForSpecialDatesMob(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }
        $specialDates = $this->inventory->getInventoryForSpecialDates($request->facility_id);
        $updatedSpecialDates = [];

        if(count($specialDates) > 0){
            
            $i=0;
            $twentyFourArray = [];
            foreach ($specialDates as $key => $value) {
                $dateArray = ['key'=>$key];
                $arrayDetails = json_decode(json_encode($value),true);                
                for ($j=0; $j < 24; $j++) {
                    if(isset($arrayDetails[$j])){
                        $twentyFourArray[$j] = $arrayDetails[$j];
                    }else{
                        $twentyFourArray[$j] = '';    
                    }                    
                    
                }
                $updatedSpecialDates[$i] = array_merge($twentyFourArray,$dateArray);
            $i++;
            }
            return $updatedSpecialDates;
        }else{
            return $specialDates;
        }
        
    }

    /**
     * @param $spots
     * @param $facilityId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     */
    public function updateInventory($spots, $facilityId, Request $request)
    {
        if (!is_numeric($facilityId)) {
            throw new ApiGenericException('Invalid facility Id', 422);
        }

        $this->inventory->updateInventory($spots, $facilityId, $request);
        return response()->json('success');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function bulkUpdateInventory(Request $request)
    {
        $days  = 'Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday';
        $rules = ['availability' => 'required|numeric|min:0',
                  'facility_id'  => 'integer',
                  'macro'        => 'in:all-day,hr,dow,json,sd', 'hr' => 'required_if:macro,hr',
                  'day'          => 'required_if:macro,dow'];

        $request['day']   = ucfirst(strtolower($request->day));
        $request['macro'] = strtolower($request->macro);

        //check if facility is active
        $userData = \Auth::user()->id;
        $this->log->info("User: ".$userData."-URL:".$request->url()."-Request Data:-".json_encode($request->all())."Header".json_encode($request->headers->all()));
        $facility = Facility::find($request->facility_id);
        
        if($facility->active) {
            switch ($request->macro) {
                case 'dow':
                    $this->customValidation($request, $rules, $days);
                   
                    $response = $this->inventory->setInventoryForDay($request);
                    break;
                case 'hr':
                    $this->customValidation($request, $rules, 'hr');
                    $response = $this->inventory->setInventoryForHour($request);
                    break;
                case 'json':
                    $rules = ['availability' => 'required|array', 'facility_id' => 'integer',
                              'macro'        => 'in:all-day,hr,dow,json', 'hr' => 'required_if:macro,hr',
                              'day'          => 'required_if:macro,dow'];
                    $this->customValidation($request, $rules);
                    $response = $this->inventory->updateAllInventory($request);
                    if($request->has('spec_availability')){
                        $response = $this->inventory->updateAllInventoryForSpecialDate($request);
                    }
                    break;
                case 'all-day':
                    $this->customValidation($request, $rules);
                    $response = $this->inventory->setInventoryForAllDay($request);
                    break;
                case 'sd':
                    $this->customValidation($request, $rules);
                    $response = $this->inventory->setInventoryForSpecialDate($request);
                    break;
                default:
                    throw new ApiGenericException("The macro must be one of the following types: all-day,dow,hr", 422);
            }
            return response()->json($response);
        } else {
            throw new ApiGenericException('Cannot update inactive facility', 422);
        }

    }

    /**
     * @param $request
     * @param $rules
     * @param $days
     * @throws ApiGenericException
     */
    protected function customValidation($request, $rules, $days = null)
    {
        /**
         * adding custom validation for 2 fields because 2 validation rules
         * does not work in laravel e.g 'required_if:macro,dow|in:'.$days
         * will throw an error of `in` rule even if macro isn't dow
         * */

        if ($days) {
            if ($days != 'hr') {
                if (!in_array($request->day, explode(',', $days))) {
                    throw new ApiGenericException("Please enter a valid day name e.g Sunday", 422);
                }
            } else {
                if (!is_numeric($request->hr)) {
                    throw new ApiGenericException("Please select a valid time", 422);
                } elseif ($request->hr < 0 || $request->hr > 24) {
                    throw new ApiGenericException("hr field can be anything between 0 to 24", 422);
                }
            }
        }

        $validator = Validator::make(
            $request->all(), $rules, ['in' => ':attribute should be one of the following types: all-day,hr,dow']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|string
     * @throws \Exception
     */
    public function reservationConfirmation(Request $request)
    {
        return response()->json($this->partnerClass->reservationConfirmation($request));
        
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     * @throws NotFoundException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function reservationCancellation(Request $request)
    {
        return response()->json($this->partnerClass->reservationCancellation($request));
    }

 /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     * @throws NotFoundException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function reservationMigration(Request $request)
    {
        return response()->json($this->partnerClass->reservationMigration($request));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     */
    public function availabilityMatrix(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer|regex:/[0-9]/']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }
        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }
        return $this->inventory->availabilityMatrix($request);
        //return response()->json($this->inventory->availabilityMatrix($request));
    }

    public function paymentTypeList(){
        $paymentList = FacilityPaymentType::get();
        return $paymentList;
    }
}
